import re
import json
import requests
import time
import hashlib
import hmac
import base64
from urllib.parse import urlparse
from typing import List, Dict

# 星火API配置
APP_ID = "0f4dc878"
API_SECRET = "M2Q1NjkzZmM4ZDUzMmQxMTc0NzI5OWU2"
API_KEY = "934d2502b887854f8f0b375c1d27a17c"
API_URL = "https://spark-api-open.xf-yun.com/v1/chat/completions"
MODEL_VERSION = "generalv3.5"
# 自定义字段
SCHEMA = {
    '病历条目': ['姓名', '年龄', '性别', '联系方式', '主诉',
                 '患病持续时间', '现病史', '既往史', '主诊断名称', '次诊断名称']
}

# 提示模板优化
IE_PROMPT = """请严格从以下病历文本中提取信息：
{}
提取要求：
1. 按JSON格式输出
2. 字段列表：{}
3. 无信息时填写'未提供'(如果主诊断名称或次诊断名称没有，则根据现信息进推断，填充主诊断名称和次诊断名称)
4. 使用专业化的术语描述:主诉、患病持续时间、现病史、既往史
5. 多个值用英文逗号分隔"""

# 示例病历数据
ie_example = {
    '病历1':
        {
            'sentence':"""我叫李四，今年38岁，性别男，联系方式15908466204，
            反复头痛、头晕2周，加重1天。已经持续2周。2周前开始出现头痛、头晕，症状时轻时重，未予重视。1天前症状加重，伴有恶心、呕吐，
            无意识障碍，无肢体麻木。糖尿病病史3年，未规律服药，血糖控制不佳。""",
            'answers':{
                "姓名": "李四",
                "年龄": "38岁",
                "性别": "男",
                "联系方式": "1590846620",
                "主诉": "反复头痛、头晕2周，加重1天",
                "患病持续时间": "2周",
                "现病史": "患者2周前开始出现头痛、头晕，症状时轻时重，未予重视。1天前症状加重，伴有恶心、呕吐，无意识障碍，无肢体麻木。",
                "既往史": "糖尿病病史3年，未规律服药，血糖控制不佳。",
                "主诊断名称": "高血压性脑病",
                "次诊断名称": "2型糖尿病"
            }
        },
    '病历2':{
        'sentence':"女，42岁。腹痛、腹泻3天。我3天前开始出现腹痛、腹泻，大便为黄色水样便，无发热，无呕吐。急性胃肠炎。",
        'answers':{
            "姓名": "未提供相应信息",
            "年龄": "42岁",
            "性别": "女",
            "联系方式": "未提供相应信息",
            "主诉": "腹痛、腹泻3天",
            "患病持续时间": "3天",
            "现病史": "患者3天前开始出现腹痛、腹泻，大便为黄色水样便，无发热，无呕吐。",
            "既往史": "未提供相应信息",
            "主诊断名称": "急性胃肠炎",
            "次诊断名称": "未提供相应信息"
        }
    },
    '病历3':{
        'sentence':"""我叫王五，今年龄52岁，性别男，联系方式：13805489806。胸闷、胸痛1小时
        1小时前开始出现胸闷、胸痛，疼痛向左肩放射，伴有出汗，无恶心、呕吐。 之前冠心病病史2年，未规律服药。
      。""",
        'answers':{
            "姓名": "王五",
            "年龄": "52岁",
            "性别": "男",
            "联系方式": "13805489806",
            "主诉": "胸闷、胸痛1小时",
            "患病持续时间": "1小时",
            "现病史": "患者1小时前开始出现胸闷、胸痛，疼痛向左肩放射，伴有出汗，无恶心、呕吐。",
            "既往史": "冠心病病史2年，未规律服药。",
            "主诊断名称": "急性心肌梗死",
            "次诊断名称": "冠心病"
        }
    },
    '病历4':{
        'sentence':"""赵六，年龄65岁，性别女，联系方式：13646254321。咳嗽、咳痰5天，
        加重伴呼吸困难2天。我5天前开始出现咳嗽、咳痰，痰为黄色黏液痰，量多。2天前症状加重，出现呼吸困难，无发热。
        之前慢性支气管炎病史10年，规律服药。""",
        'answers':{
            "姓名": "赵六",
            "年龄": "65岁",
            "性别": "女",
            "联系方式": "13646254321",
            "主诉": "咳嗽、咳痰5天，加重伴呼吸困难2天",
            "患病持续时间": "5天",
            "现病史": "患者5天前开始出现咳嗽、咳痰，痰为黄色黏液痰，量多。2天前症状加重，出现呼吸困难，无发热。",
            "既往史": "慢性支气管炎病史10年，规律服药。",
            "主诊断名称": "慢性支气管炎急性发作",
            "次诊断名称": "肺气肿"
        }
    },
    '病历5':{
        'sentence':"""我叫钱七，今年40岁，男，电话13587349876。
        我已经腹痛、腹泻2天，加重伴呕吐1天
        我2天前开始出现腹痛、腹泻，大便为黄色水样便，无发热。1天前症状加重，出现呕吐，呕吐物为胃内容物。
        无特殊病史。""",
        'answers':{
            "姓名": "钱七",
            "年龄": "40岁",
            "性别": "男",
            "联系方式": "13587349876",
            "主诉": "腹痛、腹泻2天，加重伴呕吐1天",
            "患病持续时间": "2天",
            "现病史": "患者2天前开始出现腹痛、腹泻，大便为黄色水样便，无发热。1天前症状加重，出现呕吐，呕吐物为胃内容物。",
            "既往史": "无特殊病史",
            "主诊断名称": "急性胃肠炎",
            "次诊断名称": "未提供相应信息"
        }
    }
}


def generate_auth_header() -> dict:
    """生成星火API鉴权头（必须使用此函数替换原来的headers）"""
    # 生成RFC1123格式时间戳
    timestamp = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())

    # 构造签名字符串
    host = urlparse(API_URL).hostname
    signature_origin = f"host: {host}\ndate: {timestamp}\nPOST /v1/chat/completions HTTP/1.1"

    # 计算签名
    signature_sha = hmac.new(
        API_SECRET.encode('utf-8'),
        signature_origin.encode('utf-8'),
        digestmod=hashlib.sha256
    ).digest()
    signature = base64.b64encode(signature_sha).decode()

    # 构造Authorization
    authorization = (
        f'api_key="{API_KEY}", algorithm="hmac-sha256", '
        f'headers="host date request-line", signature="{signature}"'
    )

    return {
        "Authorization": authorization,
        "Host": host,
        "Date": timestamp,
        "Content-Type": "application/json"
    }


# 删除重复的函数定义


def build_messages(sentence: str) -> List[Dict]:
    """构建消息历史"""
    messages = [
        {
            "role": "system",
            "content": "你是一个专业医疗信息提取助手，需要准确提取病历中的结构化信息"
        }
    ]

    # 添加示例数据
    for example in ie_example.values():
        messages.append({"role": "user", "content": IE_PROMPT.format(
            example['sentence'], ', '.join(SCHEMA['病历条目']))})
        messages.append({"role": "assistant", "content": json.dumps(
            example['answers'], ensure_ascii=False)})

    # 添加当前查询
    messages.append({
        "role": "user",
        "content": IE_PROMPT.format(sentence, ', '.join(SCHEMA['病历条目']))
    })

    return messages


def format_response(response: str) -> Dict:
    """格式化模型响应"""
    try:
        # 提取JSON内容
        json_str = re.search(r'\{.*\}', response, re.DOTALL).group()
        json_str = json_str.replace("，", ",")  # 处理中文逗号
        return json.loads(json_str)
    except Exception as e:
        print(f"JSON解析失败: {str(e)}")
        return {"error": "响应格式异常"}


def spark_api_call(prompt: str) -> str:
    """调用星火API"""
    headers = generate_auth_header()  # 使用鉴权函数生成headers

    payload = {
        "model": MODEL_VERSION,
        "messages": build_messages(prompt),
        "temperature": 0.2,
        "top_k": 3,
        "max_tokens": 2000,
        "response_format": {"type": "json_object"}
    }

    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"API请求失败: {response.text}")
            return None
    except Exception as e:
        print(f"网络异常: {str(e)}")
        return None


def inference(sentences: List[str]):
    """批量处理病历"""
    for sentence in sentences:
        # print("\n【原始病历】")
        # print(sentence)

        # 调用API
        response = spark_api_call(sentence)
        if not response:
            continue

        # 解析结果
        # print(response)
        # result = format_response(response)
        return format_response(response)
        # print("\n【解析结果】")
        # incode = json.dumps(result, indent=2, ensure_ascii=False)
        # print(incode)
        # print("*"*100)

if __name__ == '__main__':
    pass
    # test_sentence = []
    # content = input()
    # test_sentence.append(content)
    # inference(test_sentence)
    # while True:
    #     test_sentences = []
    #     content = input()
    #     test_sentences.append(content)
    #     inference(test_sentences)

