// Patient Data Processor
// This module handles loading and processing patient data for visualization and clustering

import * as tf from '@tensorflow/tfjs';
import * as UMAP from 'umap-js';
import hdbscan from 'hdbscanjs';
import axios from 'axios';
import { scaleLinear } from 'd3-scale';
import mockPatientData from './mockPatientData';

// BertTokenizer and BertModel would be imported from a library like '@tensorflow-models/bert'
// For the actual implementation, we need to install the proper library
// import { BertTokenizer, BertModel } from '@tensorflow-models/bert';

class PatientDataProcessor {
  constructor() {
    this.rawData = null;
    this.processedData = null;
    this.textVectors = null;
    this.numericFeatures = null;
    this.categoricalFeatures = null;
    this.clusteredData = null;
    this.clusterStats = null;
    this.embedding2D = null;
    this.bertModel = null;
    this.bertTokenizer = null;
    this.isModelLoaded = false;
    this.uniqueDiseases = []; // Store all unique diseases from the dataset
  }

  /**
   * Load and preprocess patient data from JSON file
   */
  async loadData() {
    try {
      console.log('正在加载 All.json 数据...');
      // Load data from the All.json file
      const response = await fetch('/data/All.json');
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // 首先获取文本内容
      let text = await response.text();
      
      // Fix common JSON issues
      text = text.replace(/:\s*NaN\s*,/g, ': null,') // Replace NaN with null (with comma)
             .replace(/:\s*NaN\s*\n/g, ': null\n') // Replace NaN with null (at end of line)
             .replace(/:\s*NaN\s*}/g, ': null}') // Replace NaN with null (at end of object)
             .replace(/:\s*undefined\s*,/g, ': null,') // Replace undefined with null
             .replace(/,\s*}/g, '}') // Remove trailing commas
             .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays
      
      try {
        // 尝试解析 JSON
        this.rawData = JSON.parse(text);
      } catch (parseError) {
        console.error('解析 JSON 数据时出错:', parseError);
        
        // 尝试清理文本并再次解析
        const cleanedText = text.replace(/[\u0000-\u0019]+/g, " ");
        
        try {
          this.rawData = JSON.parse(cleanedText);
        } catch (manualParseError) {
          console.error('手动解析 JSON 失败:', manualParseError);
          throw new Error('无法解析 JSON 数据');
        }
      }
      
      // If the data is empty or failed to load, throw an error
      if (!this.rawData || !Array.isArray(this.rawData) || this.rawData.length === 0) {
        throw new Error('All.json 数据为空或无效');
      }
      
      console.log(`成功加载 ${this.rawData.length} 条患者记录`);
      
      // Extract all unique diseases from the dataset
      this.extractUniqueDiseases();
      
      return true;
    } catch (error) {
      console.error('加载患者数据时出错:', error);
      throw error; // 重新抛出错误，而不是回退到模拟数据
    }
  }

  /**
   * Extract all unique diseases from the dataset
   */
  extractUniqueDiseases() {
    if (!this.rawData) return;

    // Create a set to store unique disease codes
    const diseaseSet = new Set();
    
    // Extract the main diagnosis from each patient
    this.rawData.forEach(patient => {
      // 明确使用主要诊断名称，而不是代码
      const diagnosis = patient['主要诊断名称'] || patient['主要诊断'] || patient['涓昏璇婃柇鍚嶇О'] || patient['涓昏璇婃柇'];
      if (diagnosis) {
        diseaseSet.add(diagnosis);
      }
    });
    
    // Convert set to array
    this.uniqueDiseases = Array.from(diseaseSet);
    console.log(`Extracted ${this.uniqueDiseases.length} unique diseases from the dataset`);
  }

  /**
   * Get all unique diseases from the dataset
   */
  getUniqueDiseases() {
    return this.uniqueDiseases;
  }

  /**
   * Process the raw patient data to extract and format features
   */
  processData() {
    if (!this.rawData) {
      console.error('No data to process. Please load data first.');
      return false;
    }

    console.log(`开始处理 ${this.rawData.length} 条患者原始数据`);
    
    // Initialize arrays for different feature types
    const numericFeatures = [];
    const categoricalData = [];
    const textData = [];
    
    // Extract relevant fields from each patient
    this.processedData = this.rawData.map((patient, index) => {
      try {
        // 确保患者数据有效
        if (!patient) {
          console.warn(`患者记录 #${index} 无效`);
          return null;
        }
        
        // 尝试提取所有相关字段，并提供默认值
        
        // 提取数值字段
        const numericValues = {
          age: this.extractAge(patient['年龄'] || patient['骞撮緞'] || patient.age || '0岁'),
          HbA1c: this.extractLabValue(patient, 'HbA1c') || 5.0,
          eAG: this.extractLabValue(patient, 'eAG') || 5.0,
          IOP_R: this.extractLabValue(patient, 'IOP_R') || (patient.iop ? patient.iop.R : 15.0),
          IOP_L: this.extractLabValue(patient, 'IOP_L') || (patient.iop ? patient.iop.L : 15.0),
          visit_count: patient.visit_count || 1
        };
        
        // 提取分类字段
        const categoricalValues = {
          gender: patient['性别'] || patient['鎬у埆'] || patient.gender || '未知',
          main_diagnosis_code: patient['主要诊断名称'] || patient['主要诊断'] || patient['涓昏璇婃柇鍚嶇О'] || patient['涓昏璇婃柇'] || patient.diagnosis || '未知',
          department_code: patient['处置科室代码'] || patient['澶勭疆绉戝浠ｇ爜'] || patient.department || 0
        };
        
        // 提取文本字段
        const textValues = {
          chief_complaint: patient['主诉'] || patient['涓昏瘔'] || patient.complaint || '',
          present_illness_history: patient['现病史'] || patient['鐜扮梾鍙?'] || patient.history || ''
        };
        
        // 保存特征用于批处理
        numericFeatures.push(Object.values(numericValues));
        categoricalData.push(categoricalValues);
        textData.push(textValues);
        
        // 获取患者ID（就诊卡号）
        let patientId = null;
        if (patient['就诊卡号']) {
          patientId = patient['就诊卡号'];
        } else if (patient['灏辫瘖鍗″彿']) {
          patientId = patient['灏辫瘖鍗″彿'];
        } else if (patient.card_id) {
          patientId = patient.card_id;
        } else {
          patientId = `未知-${Math.random().toString(36).substring(2, 10)}`;
        }
        
        // 返回处理后的患者数据
        return {
          id: patientId,
          numeric: numericValues,
          categorical: categoricalValues,
          text: textValues,
          raw: patient // 保留原始数据供参考
        };
      } catch (error) {
        console.warn(`处理患者 #${index} 数据时出错: ${error.message}`);
        return null;
      }
    }).filter(patient => patient !== null); // 过滤掉处理失败的患者
    
    console.log(`成功处理 ${this.processedData.length} 条有效患者数据`);
    
    // 存储提取的特征
    this.numericFeatures = numericFeatures;
    this.categoricalData = categoricalData;
    this.textData = textData;
    
    return true;
  }
  
  /**
   * Extract numeric age from age string (e.g., "62岁" -> 62)
   */
  extractAge(ageString) {
    const match = ageString.match(/(\d+)/);
    return match ? parseInt(match[1]) : 50; // Default to 50 if parsing fails
  }
  
  /**
   * Extract lab values from patient data
   */
  extractLabValue(patient, labName) {
    // Check if patient has a lab results array
    const labResults = patient['检查项目'] || patient['妫€鏌ラ」鐩?'] || [];
    
    // Look for the specific lab test
    for (const lab of labResults) {
      if ((lab.name && lab.name.includes(labName)) || 
          (lab.labName && lab.labName.includes(labName))) {
        return parseFloat(lab.value || lab['结果值'] || '0');
      }
    }
    
    // Return null if not found
    return null;
  }

  /**
   * Step 1: Normalize numeric features
   */
  normalizeNumericFeatures() {
    if (!this.numericFeatures || this.numericFeatures.length === 0) {
      console.error('No numeric features to normalize');
      return null;
    }
    
    // Convert to tensor for efficient normalization
    const featuresTensor = tf.tensor2d(this.numericFeatures);
    
    // Calculate mean and standard deviation for each feature
    const mean = featuresTensor.mean(0);
    const std = featuresTensor.sub(mean).pow(2).mean(0).sqrt();
    
    // Normalize features: (x - mean) / std
    const normalizedFeatures = featuresTensor.sub(mean).div(std);
    
    // Convert back to JS array
    return normalizedFeatures.arraySync();
  }

  /**
   * Step 2: One-hot encode categorical features
   */
  oneHotEncodeCategoricalFeatures() {
    if (!this.categoricalData || this.categoricalData.length === 0) {
      console.error('No categorical features to encode');
      return null;
    }
    
    // Get unique values for each categorical feature
    const uniqueValues = {};
    const featureNames = Object.keys(this.categoricalData[0]);
    
    featureNames.forEach(feature => {
      uniqueValues[feature] = [...new Set(this.categoricalData.map(item => item[feature]))];
    });
    
    // Create one-hot encoding for each sample
    const encodedFeatures = this.categoricalData.map(sample => {
      const encoded = [];
      
      featureNames.forEach(feature => {
        const featureValues = uniqueValues[feature];
        const oneHot = new Array(featureValues.length).fill(0);
        const valueIndex = featureValues.indexOf(sample[feature]);
        
        if (valueIndex !== -1) {
          oneHot[valueIndex] = 1;
        }
        
        encoded.push(...oneHot);
      });
      
      return encoded;
    });
    
    return encodedFeatures;
  }

  /**
   * Step 3: Generate text embeddings using BERT
   * Note: This is a placeholder. In a real implementation, you would:
   * 1. Load a pre-trained BERT model
   * 2. Tokenize the text
   * 3. Get embeddings from BERT
   */
  async generateTextEmbeddings() {
    if (!this.textData || this.textData.length === 0) {
      console.error('No text data to process');
      return null;
    }
    
    // Placeholder for BERT embeddings
    // In a real implementation, you would use a pre-trained BERT model
    const mockEmbeddings = [];
    
    // In a real implementation, you would do something like:
    // await this.loadBertModel();
    
    for (const sample of this.textData) {
      // Combine chief complaint and present illness history
      const combinedText = `${sample.chief_complaint} ${sample.present_illness_history}`;
      
      // Generate random vector as placeholder for BERT embedding
      // Real implementation would use BERT model to get embeddings
      const embeddingDim = 768; // BERT base embedding dimension
      const mockEmbedding = Array(embeddingDim).fill(0).map(() => Math.random() - 0.5);
      
      mockEmbeddings.push(mockEmbedding);
    }
    
    this.textVectors = mockEmbeddings;
    return mockEmbeddings;
  }
  
  /**
   * Load BERT model for text embedding (placeholder)
   */
  async loadBertModel() {
    // In a real implementation, you would load the BERT model here
    // this.bertTokenizer = await BertTokenizer.fromPretrained('bert-base-uncased');
    // this.bertModel = await BertModel.fromPretrained('bert-base-uncased');
    this.isModelLoaded = true;
    console.log('BERT model loaded');
  }

  /**
   * Step 4: Reduce text embedding dimensions using UMAP
   */
  reduceTextEmbeddingDimensions(dimensions = 20) {
    if (!this.textVectors || this.textVectors.length === 0) {
      console.error('No text vectors to reduce dimensions');
      return null;
    }
    
    // Create a UMAP instance
    const umap = new UMAP.UMAP({
      nComponents: dimensions,
      nEpochs: 400,
      nNeighbors: 15,
      minDist: 0.1
    });
    
    // Perform dimension reduction
    const reducedEmbeddings = umap.fit(this.textVectors);
    
    return reducedEmbeddings;
  }

  /**
   * Step 5: Concatenate all features into a single feature vector
   */
  concatenateFeatures(normalizedNumeric, oneHotCategorical, reducedTextEmbeddings) {
    if (!normalizedNumeric || !oneHotCategorical || !reducedTextEmbeddings) {
      console.error('Missing feature sets for concatenation');
      return null;
    }
    
    // Concatenate features for each patient
    const concatenatedFeatures = normalizedNumeric.map((numFeatures, i) => {
      return [
        ...numFeatures,                    // Normalized numeric features
        ...oneHotCategorical[i],           // One-hot encoded categorical features
        ...reducedTextEmbeddings[i]        // Reduced text embeddings
      ];
    });
    
    return concatenatedFeatures;
  }

  /**
   * Step 6: Reduce dimensions of concatenated features using UMAP
   */
  reduceConcatenatedFeatures(concatenatedFeatures, dimensions = 10) {
    if (!concatenatedFeatures || concatenatedFeatures.length === 0) {
      console.error('No concatenated features to reduce');
      return null;
    }
    
    // Create a UMAP instance
    const umap = new UMAP.UMAP({
      nComponents: dimensions,
      nEpochs: 400,
      nNeighbors: 15,
      minDist: 0.1
    });
    
    // Perform dimension reduction
    const reducedFeatures = umap.fit(concatenatedFeatures);
    
    return reducedFeatures;
  }

  /**
   * Step 7: Cluster the data using HDBSCAN
   */
  clusterData(reducedFeatures, minClusterSize = 10) {
    if (!reducedFeatures || reducedFeatures.length === 0) {
      console.error('No reduced features to cluster');
      return null;
    }
    
    // For the mock implementation, let's use a simpler clustering approach
    // In a real implementation, you would use HDBSCAN properly
    
    // Create simple clusters based on the first two dimensions
    const numClusters = 5; // Number of clusters to create
    const clusters = [];
    
    // Initialize cluster centers randomly
    const centers = [];
    for (let i = 0; i < numClusters; i++) {
      // Create centers throughout the feature space
      centers.push({
        x: Math.random() * 10 - 5, // Random value between -5 and 5
        y: Math.random() * 10 - 5
      });
    }
    
    // Assign each point to the nearest cluster
    for (let i = 0; i < reducedFeatures.length; i++) {
      const point = reducedFeatures[i];
      const x = point[0]; // First dimension
      const y = point[1]; // Second dimension
      
      // Find the nearest center
      let minDist = Infinity;
      let clusterId = -1;
      
      for (let j = 0; j < centers.length; j++) {
        const center = centers[j];
        const dist = Math.sqrt(Math.pow(x - center.x, 2) + Math.pow(y - center.y, 2));
        
        if (dist < minDist) {
          minDist = dist;
          clusterId = j;
        }
      }
      
      // Assign to the nearest cluster
      clusters.push(clusterId);
    }
    
    // Format results
    const clusterResults = reducedFeatures.map((features, i) => {
      return {
        patientId: this.processedData[i].id,
        cluster: clusters[i],
        features: features
      };
    });
    
    this.clusteredData = clusterResults;
    return clusterResults;
  }

  /**
   * Step 8: Create 2D embedding for visualization
   */
  createVisualizationEmbedding(reducedFeatures) {
    if (!reducedFeatures || reducedFeatures.length === 0) {
      console.error('No reduced features for visualization');
      return null;
    }
    
    // Create a UMAP instance
    const umap = new UMAP.UMAP({
      nComponents: 2,
      nEpochs: 400,
      nNeighbors: 15,
      minDist: 0.1
    });
    
    // Perform dimension reduction to 2D
    const embedding2D = umap.fit(reducedFeatures);
    
    // Format results for visualization
    const visualizationData = embedding2D.map((coords, i) => {
      const clusterId = this.clusteredData ? this.clusteredData[i].cluster : -1;
      
      return {
        x: coords[0],
        y: coords[1],
        patientId: this.processedData[i].id,
        cluster: clusterId,
        age: this.processedData[i].numeric.age,
        gender: this.processedData[i].categorical.gender,
        diagnosis: this.processedData[i].categorical.main_diagnosis_code,
        visitCount: this.processedData[i].numeric.visit_count,
        HbA1c: this.processedData[i].numeric.HbA1c,
        IOP_R: this.processedData[i].numeric.IOP_R,
        IOP_L: this.processedData[i].numeric.IOP_L
      };
    });
    
    this.embedding2D = visualizationData;
    return visualizationData;
  }

  /**
   * Step 9: Calculate statistics for each cluster
   */
  calculateClusterStatistics() {
    if (!this.clusteredData || !this.processedData) {
      console.error('No clustered data for statistics');
      return null;
    }
    
    // Get unique cluster IDs
    const clusterIds = [...new Set(this.clusteredData.map(item => item.cluster))];
    
    // Calculate statistics for each cluster
    const clusterStats = clusterIds.map(clusterId => {
      // Get all patients in this cluster
      const clusterPatients = this.clusteredData
        .filter(item => item.cluster === clusterId)
        .map(item => {
          const patientId = item.patientId;
          return this.processedData.find(p => p.id === patientId);
        });
      
      // Skip if no patients found
      if (clusterPatients.length === 0) {
        return {
          clusterId: clusterId,
          patientCount: 0,
          averageAge: 0,
          averageHbA1c: 0,
          diagnosisCounts: {}
        };
      }
      
      // Calculate average age
      const averageAge = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.age, 0) / clusterPatients.length;
      
      // Calculate average HbA1c
      const averageHbA1c = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.HbA1c, 0) / clusterPatients.length;
      
      // Calculate average visit count
      const averageVisitCount = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.visit_count, 0) / clusterPatients.length;
      
      // Count diagnoses
      const diagnosisCounts = {};
      clusterPatients.forEach(patient => {
        const diagnosis = patient.categorical.main_diagnosis_code;
        diagnosisCounts[diagnosis] = (diagnosisCounts[diagnosis] || 0) + 1;
      });
      
      // Sort diagnoses by frequency
      const sortedDiagnoses = Object.entries(diagnosisCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5) // Top 5 diagnoses
        .reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {});
      
      return {
        clusterId: clusterId,
        patientCount: clusterPatients.length,
        averageAge: averageAge,
        averageHbA1c: averageHbA1c,
        averageVisitCount: averageVisitCount,
        diagnosisCounts: sortedDiagnoses
      };
    });
    
    this.clusterStats = clusterStats;
    return clusterStats;
  }

  /**
   * Run the full data processing pipeline
   */
  async runFullPipeline() {
    try {
      // 如果数据已经加载和处理过，就直接使用现有数据
      if (!this.rawData) {
        // Load data
        const dataLoaded = await this.loadData();
        if (!dataLoaded) {
          throw new Error('Failed to load data');
        }
      }
      
      if (!this.processedData) {
        // Process data
        const dataProcessed = this.processData();
        if (!dataProcessed) {
          throw new Error('Failed to process data');
        }
      }
      
      // For real data implementation, let's create more realistic clusters based on diagnosis
      const diagnosisClusters = this.clusterByDiagnosis();
      this.embedding2D = diagnosisClusters.visualizationData;
      this.clusterStats = diagnosisClusters.clusterStats;
      
      return {
        visualizationData: this.embedding2D,
        clusterStats: this.clusterStats
      };
    } catch (error) {
      console.error('Error in data processing pipeline:', error);
      throw error; // 重新抛出错误，让调用者处理
    }
  }
  
  /**
   * Create clusters based on diagnosis groups
   */
  clusterByDiagnosis() {
    if (!this.processedData) {
      console.error('没有可用的处理数据');
      throw new Error('没有可用的处理数据');
    }
    
    console.log(`开始对 ${this.processedData.length} 条患者数据进行疾病分类聚类`);
    
    // Group diseases by their first 3 characters (disease category)
    const diseaseCategories = new Map();
    
    // 处理每个患者记录
    this.processedData.forEach((patient, index) => {
      if (!patient.categorical) {
        console.warn(`患者 #${index} 缺少分类数据`);
        return;
      }
      
      const diagnosis = patient.categorical.main_diagnosis_code;
      if (!diagnosis) {
        console.warn(`患者 #${index} 缺少诊断码`);
        return;
      }
      
      // 确保诊断码至少有3个字符，否则使用完整的诊断码
      let category = diagnosis;
      if (diagnosis.length >= 3) {
        category = diagnosis.substring(0, 3);
      }
      
      if (!diseaseCategories.has(category)) {
        diseaseCategories.set(category, []);
      }
      diseaseCategories.get(category).push(patient);
    });
    
    console.log(`找到 ${diseaseCategories.size} 个不同的疾病类别`);
    
    // 如果没有有效的疾病类别，创建一个默认类别
    if (diseaseCategories.size === 0) {
      console.warn('没有找到有效的疾病类别，创建默认分类');
      diseaseCategories.set('默认', this.processedData);
    }
    
    // Convert to array of categories
    const categories = Array.from(diseaseCategories.entries())
      .filter(([_, patients]) => patients.length >= 3) // Only keep categories with at least 3 patients
      .map(([category, patients], index) => {
        return {
          category,
          patients,
          clusterId: index
        };
      });
    
    console.log(`生成 ${categories.length} 个疾病类别聚类`);
    
    // 如果没有足够的类别，添加所有患者到一个默认类别
    if (categories.length === 0) {
      console.warn('没有足够的疾病类别，创建默认分类');
      categories.push({
        category: '默认',
        patients: this.processedData,
        clusterId: 0
      });
    }
    
    // Generate visual coordinates based on disease clusters
    const visualizationData = [];
    
    // Generate cluster centers
    const centers = categories.map((category, index) => {
      // Create centers in a circle layout
      const angle = (index / categories.length) * Math.PI * 2;
      const distance = 5; // Radius of the circle
      return {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        clusterId: category.clusterId
      };
    });
    
    // Generate points around centers
    categories.forEach(category => {
      const center = centers.find(c => c.clusterId === category.clusterId);
      if (!center) {
        console.warn(`未找到类别 ${category.category} 的中心点`);
        return;
      }
      
      category.patients.forEach(patient => {
        try {
          // Add random jitter around the cluster center
          const angle = Math.random() * Math.PI * 2;
          const distance = Math.random() * 2; // Random distance from center
          const x = center.x + Math.cos(angle) * distance;
          const y = center.y + Math.sin(angle) * distance;
          
          // 确保所有必需的数据都存在
          const patientData = {
            x: x,
            y: y,
            patientId: patient.id || '未知ID',
            cluster: category.clusterId,
            category: category.category,
            age: patient.numeric?.age || 0,
            gender: patient.categorical?.gender || '未知',
            diagnosis: patient.categorical?.main_diagnosis_code || '未知',
            visitCount: patient.numeric?.visit_count || 1,
            HbA1c: patient.numeric?.HbA1c || 0,
            IOP_R: patient.numeric?.IOP_R || 0,
            IOP_L: patient.numeric?.IOP_L || 0
          };
          
          visualizationData.push(patientData);
        } catch (error) {
          console.warn(`处理患者数据时出错: ${error.message}`);
        }
      });
    });
    
    console.log(`生成了 ${visualizationData.length} 个数据点用于可视化`);
    
    // Calculate statistics for disease clusters
    const clusterStats = categories.map(category => {
      try {
        const patients = category.patients;
        
        // Calculate average age
        const ageValues = patients.map(p => p.numeric?.age || 0).filter(age => age > 0);
        const averageAge = ageValues.length > 0 
          ? ageValues.reduce((sum, age) => sum + age, 0) / ageValues.length 
          : 0;
        
        // Calculate average HbA1c
        const hba1cValues = patients.map(p => p.numeric?.HbA1c || 0).filter(v => v > 0);
        const averageHbA1c = hba1cValues.length > 0 
          ? hba1cValues.reduce((sum, v) => sum + v, 0) / hba1cValues.length 
          : 0;
        
        // Calculate average visit count
        const visitValues = patients.map(p => p.numeric?.visit_count || 0).filter(v => v > 0);
        const averageVisitCount = visitValues.length > 0 
          ? visitValues.reduce((sum, v) => sum + v, 0) / visitValues.length 
          : 0;
        
        // Count diagnoses
        const diagnosisCounts = {};
        patients.forEach(patient => {
          const diagnosis = patient.categorical?.main_diagnosis_code;
          if (diagnosis) {
            diagnosisCounts[diagnosis] = (diagnosisCounts[diagnosis] || 0) + 1;
          }
        });
        
        // Sort diagnoses by frequency
        const sortedDiagnoses = Object.entries(diagnosisCounts)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5) // Top 5 diagnoses
          .reduce((obj, [key, value]) => {
            obj[key] = value;
            return obj;
          }, {});
        
        return {
          clusterId: category.clusterId,
          label: `疾病类别 ${category.category}`,
          patientCount: patients.length,
          averageAge: averageAge,
          averageHbA1c: averageHbA1c,
          averageVisitCount: averageVisitCount,
          diagnosisCounts: sortedDiagnoses
        };
      } catch (error) {
        console.warn(`计算类别 ${category.category} 的统计信息时出错: ${error.message}`);
        return {
          clusterId: category.clusterId,
          label: `疾病类别 ${category.category}`,
          patientCount: category.patients.length,
          averageAge: 0,
          averageHbA1c: 0,
          averageVisitCount: 0,
          diagnosisCounts: {}
        };
      }
    });
    
    console.log(`生成了 ${clusterStats.length} 个聚类统计信息`);
    
    // 保存结果用于可视化
    this.embedding2D = visualizationData;
    this.clusterStats = clusterStats;
    
    return {
      visualizationData,
      clusterStats
    };
  }

  /**
   * Create an alternative clustering based on age
   */
  clusterByAge() {
    if (!this.processedData) {
      console.error('No processed data available');
      return null;
    }
    
    // Define age clusters
    const ageGroups = [
      { min: 0, max: 20, label: '0-20岁', color: '#c6dbef' },
      { min: 21, max: 40, label: '21-40岁', color: '#9ecae1' },
      { min: 41, max: 60, label: '41-60岁', color: '#6baed6' },
      { min: 61, max: 80, label: '61-80岁', color: '#2171b5' },
      { min: 81, max: 120, label: '81岁以上', color: '#084594' }
    ];
    
    // Group patients by age group
    const ageCategories = new Map();
    for (let i = 0; i < ageGroups.length; i++) {
      ageCategories.set(i, []);
    }
    
    this.processedData.forEach(patient => {
      const age = patient.numeric.age;
      let groupId = -1;
      
      // Find appropriate age group
      for (let i = 0; i < ageGroups.length; i++) {
        if (age >= ageGroups[i].min && age <= ageGroups[i].max) {
          groupId = i;
          break;
        }
      }
      
      // Default to middle group if not found
      if (groupId === -1) groupId = 2;
      
      ageCategories.get(groupId).push(patient);
    });
    
    // Generate visual coordinates based on age
    const visualizationData = [];
    
    // Generate cluster centers in a horizontal line
    const centers = ageGroups.map((group, index) => {
      return {
        x: (index - 2) * 3, // Space out horizontally
        y: 0,
        clusterId: index
      };
    });
    
    // Generate points around centers
    ageGroups.forEach((group, clusterId) => {
      const patients = ageCategories.get(clusterId);
      const center = centers[clusterId];
      
      patients.forEach(patient => {
        // Position based on age cluster and add some randomness
        const xBase = center.x;
        const yBase = 0;
        
        // Add random jitter
        const x = xBase + (Math.random() - 0.5) * 1.5;
        const y = yBase + (Math.random() - 0.5) * 3;
        
        visualizationData.push({
          x: x,
          y: y,
          patientId: patient.id,
          cluster: clusterId,
          ageGroup: ageGroups[clusterId].label,
          age: patient.numeric.age,
          gender: patient.categorical.gender,
          diagnosis: patient.categorical.main_diagnosis_code,
          visitCount: patient.numeric.visit_count,
          HbA1c: patient.numeric.HbA1c,
          IOP_R: patient.numeric.IOP_R,
          IOP_L: patient.numeric.IOP_L,
          color: ageGroups[clusterId].color
        });
      });
    });
    
    // Calculate statistics for age clusters
    const clusterStats = ageGroups.map((group, clusterId) => {
      // Get all patients in this cluster
      const clusterPatients = ageCategories.get(clusterId);
      
      // Skip if no patients found
      if (!clusterPatients || clusterPatients.length === 0) {
        return {
          clusterId: clusterId,
          label: group.label,
          patientCount: 0,
          averageAge: 0,
          averageHbA1c: 0,
          diagnosisCounts: {}
        };
      }
      
      // Calculate average age
      const averageAge = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.age, 0) / clusterPatients.length;
      
      // Calculate average HbA1c
      const averageHbA1c = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.HbA1c, 0) / clusterPatients.length;
      
      // Calculate average visit count
      const averageVisitCount = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.visit_count, 0) / clusterPatients.length;
      
      // Count diagnoses
      const diagnosisCounts = {};
      clusterPatients.forEach(patient => {
        const diagnosis = patient.categorical.main_diagnosis_code;
        diagnosisCounts[diagnosis] = (diagnosisCounts[diagnosis] || 0) + 1;
      });
      
      // Sort diagnoses by frequency
      const sortedDiagnoses = Object.entries(diagnosisCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5) // Top 5 diagnoses
        .reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {});
      
      return {
        clusterId: clusterId,
        label: group.label,
        patientCount: clusterPatients.length,
        averageAge: averageAge,
        averageHbA1c: averageHbA1c,
        averageVisitCount: averageVisitCount,
        diagnosisCounts: sortedDiagnoses
      };
    });
    
    this.embedding2D = visualizationData;
    this.clusterStats = clusterStats;
    
    return {
      visualizationData: this.embedding2D,
      clusterStats: this.clusterStats
    };
  }
  
  /**
   * Create an alternative clustering based on visit count
   */
  clusterByVisitCount() {
    if (!this.processedData) {
      console.error('No processed data available');
      return null;
    }
    
    // Define visit count clusters
    const visitGroups = [
      { min: 1, max: 1, label: '首次就诊', color: '#edf8e9' },
      { min: 2, max: 3, label: '2-3次就诊', color: '#bae4b3' },
      { min: 4, max: 5, label: '4-5次就诊', color: '#74c476' },
      { min: 6, max: 10, label: '6-10次就诊', color: '#31a354' },
      { min: 11, max: 1000, label: '11次以上就诊', color: '#006d2c' }
    ];
    
    // Group patients by visit count
    const visitCategories = new Map();
    for (let i = 0; i < visitGroups.length; i++) {
      visitCategories.set(i, []);
    }
    
    this.processedData.forEach(patient => {
      const visitCount = patient.numeric.visit_count;
      let groupId = -1;
      
      // Find appropriate visit group
      for (let i = 0; i < visitGroups.length; i++) {
        if (visitCount >= visitGroups[i].min && visitCount <= visitGroups[i].max) {
          groupId = i;
          break;
        }
      }
      
      // Default to middle group if not found
      if (groupId === -1) groupId = 2;
      
      visitCategories.get(groupId).push(patient);
    });
    
    // Generate visual coordinates based on visit count
    const visualizationData = [];
    
    // Generate cluster centers in a horizontal line
    const centers = visitGroups.map((group, index) => {
      return {
        x: (index - 2) * 3, // Space out horizontally
        y: 0,
        clusterId: index
      };
    });
    
    // Generate points around centers
    visitGroups.forEach((group, clusterId) => {
      const patients = visitCategories.get(clusterId);
      const center = centers[clusterId];
      
      patients.forEach(patient => {
        // Position based on visit count and add some randomness
        const xBase = center.x;
        const yBase = 0;
        
        // Add random jitter
        const x = xBase + (Math.random() - 0.5) * 1.5;
        const y = yBase + (Math.random() - 0.5) * 3;
        
        visualizationData.push({
          x: x,
          y: y,
          patientId: patient.id,
          cluster: clusterId,
          visitGroup: visitGroups[clusterId].label,
          age: patient.numeric.age,
          gender: patient.categorical.gender,
          diagnosis: patient.categorical.main_diagnosis_code,
          visitCount: patient.numeric.visit_count,
          HbA1c: patient.numeric.HbA1c,
          IOP_R: patient.numeric.IOP_R,
          IOP_L: patient.numeric.IOP_L,
          color: visitGroups[clusterId].color
        });
      });
    });
    
    // Calculate statistics for visit count clusters
    const visitClusterStats = visitGroups.map((group, clusterId) => {
      // Get all patients in this cluster
      const clusterPatients = visitCategories.get(clusterId);
      
      // Skip if no patients found
      if (!clusterPatients || clusterPatients.length === 0) {
        return {
          clusterId: clusterId,
          label: group.label,
          patientCount: 0,
          averageAge: 0,
          averageHbA1c: 0,
          averageVisitCount: 0,
          diagnosisCounts: {}
        };
      }
      
      // Calculate average age
      const averageAge = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.age, 0) / clusterPatients.length;
      
      // Calculate average HbA1c
      const averageHbA1c = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.HbA1c, 0) / clusterPatients.length;
      
      // Calculate average visit count
      const averageVisitCount = clusterPatients.reduce((sum, patient) => 
        sum + patient.numeric.visit_count, 0) / clusterPatients.length;
      
      // Count diagnoses
      const diagnosisCounts = {};
      clusterPatients.forEach(patient => {
        const diagnosis = patient.categorical.main_diagnosis_code;
        diagnosisCounts[diagnosis] = (diagnosisCounts[diagnosis] || 0) + 1;
      });
      
      // Sort diagnoses by frequency
      const sortedDiagnoses = Object.entries(diagnosisCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5) // Top 5 diagnoses
        .reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {});
      
      return {
        clusterId: clusterId,
        label: group.label,
        patientCount: clusterPatients.length,
        averageAge: averageAge,
        averageHbA1c: averageHbA1c,
        averageVisitCount: averageVisitCount,
        diagnosisCounts: sortedDiagnoses
      };
    });
    
    this.embedding2D = visualizationData;
    this.clusterStats = visitClusterStats;
    
    return {
      visualizationData: this.embedding2D,
      clusterStats: this.clusterStats
    };
  }
  
  /**
   * Get visualization data with specific color scale
   */
  getVisualizationWithColors(colorBy = 'cluster') {
    if (!this.embedding2D) {
      console.error('No visualization data available');
      return null;
    }
    
    // Define color scales for different attributes
    const clusterColors = [
      '#4682B4', '#5F9EA0', '#6495ED', '#00BFFF', '#1E90FF', 
      '#87CEEB', '#87CEFA', '#ADD8E6', '#B0E0E6', '#00CED1',
      '#48D1CC', '#40E0D0', '#7FFFD4', '#66CDAA', '#20B2AA'
    ];
    
    // Create age color scale
    const ageColorScale = (age) => {
      if (age <= 20) return '#c6dbef';
      if (age <= 40) return '#9ecae1';
      if (age <= 60) return '#6baed6';
      if (age <= 80) return '#2171b5';
      return '#084594';
    };
    
    // Create visit count color scale
    const visitColorScale = (visits) => {
      if (visits === 1) return '#edf8e9';
      if (visits <= 3) return '#bae4b3';
      if (visits <= 5) return '#74c476';
      if (visits <= 10) return '#31a354';
      return '#006d2c';
    };
    
    // Apply colors based on selected attribute
    const coloredData = this.embedding2D.map(item => {
      let color;
      
      switch (colorBy) {
        case 'cluster':
          // Use cluster-based coloring
          const clusterIdx = Math.max(0, item.cluster); // Handle -1 (noise)
          color = clusterColors[clusterIdx % clusterColors.length];
          break;
          
        case 'age':
          // Use age-based coloring
          color = ageColorScale(item.age);
          break;
          
        case 'visits':
          // Use visit count-based coloring
          color = visitColorScale(item.visitCount);
          break;
          
        case 'gender':
          // Use gender-based coloring
          color = item.gender.includes('男') ? '#1f77b4' : '#e377c2';
          break;
          
        default:
          color = '#999';
      }
      
      return {
        ...item,
        color
      };
    });
    
    return coloredData;
  }
}

// Export singleton instance
export default new PatientDataProcessor(); 