import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  // Serve static files from the data directory
  publicDir: 'public',
  // Custom configuration to handle large JSON files
  optimizeDeps: {
    exclude: ['large-json-files']
  }
})
