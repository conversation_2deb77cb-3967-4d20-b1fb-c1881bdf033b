<template>
  <div class="diagnosis-assistant">
    <header class="header">
      <h1>DiagnosisAssistant</h1>
      <span class="subtitle">A learning Tool for Medical Diagnosis</span>
    </header>
    
    <div class="main-content" :class="{ 'hidden-panel': isRightPanelHidden }">
      <!-- Left Panel -->
      <div class="left-panel">
        <h2>Panel</h2>
        
        <!-- 新增：导入功能区�?-->
        <div class="panel-section import-section">
          <h3>Import/Export:</h3>
          <div class="button-group">
            <button class="panel-button primary">
              <span class="button-icon">📂</span> Import Data
            </button>
            <button class="panel-button">
              <span class="button-icon">📊</span> Import Image
            </button>
            <button class="panel-button">
              <span class="button-icon">💾</span> Export Results
            </button>
          </div>
        </div>
        
        <!-- 新增：疾病选择 -->
        <div class="panel-section disease-section">
          <h3>Case Selection:</h3>
          <div class="disease-comparison">
            <div class="selector-group">
              <label>Disease 1:</label>
              <select class="disease-dropdown" v-model="selectedDisease1" @change="updateDiseaseDisplay">
                <option value="macular_edema">Macular Edema</option>
                <option value="open_angle_glaucoma">Open-Angle Glaucoma</option>
                <option value="closed_angle_glaucoma">Closed-Angle Glaucoma</option>
                <option value="diabetic_maculopathy">Diabetic Maculopathy</option>
                <option value="retinitis_pigmentosa">Retinitis Pigmentosa</option>
                <option value="branch_retinal_vein_occlusion">Branch Retinal Vein Occlusion</option>
              </select>
            </div>
            <div class="selector-group">
              <label>Disease 2:</label>
              <select class="disease-dropdown" v-model="selectedDisease2" @change="updateDiseaseDisplay">
                <option value="open_angle_glaucoma">Open-Angle Glaucoma</option>
                <option value="macular_edema">Macular Edema</option>
                <option value="closed_angle_glaucoma">Closed-Angle Glaucoma</option>
                <option value="diabetic_maculopathy">Diabetic Maculopathy</option>
                <option value="retinitis_pigmentosa">Retinitis Pigmentosa</option>
                <option value="branch_retinal_vein_occlusion">Branch Retinal Vein Occlusion</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="panel-section">
          <h3>Dataset:</h3>
          <div class="dataset-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Datainfo:</h3>
          <div class="datainfo-item">Diagnostic Task:</div>
          <div class="datainfo-value"></div>
          
          <div class="datainfo-item">Diagnostic Entry:</div>
          <div class="datainfo-value"></div>
          
          <div class="datainfo-item">No. of cases:</div>
          <div class="datainfo-value"></div>
        </div>
        
        <div class="panel-section">
          <h3>Modality:</h3>
          <div class="modality-item selected"></div>
          <div class="modality-item"></div>
          <div class="modality-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Composition:</h3>
        </div>
        
        <div class="panel-section">
          <h3>Body Part:</h3>
          <div class="body-part-item"></div>
          <div class="body-part-item">Cross-section:</div>
          <div class="body-part-item"></div>
          <div class="body-part-item">Resolution:</div>
          <div class="body-part-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Model Performance:</h3>
        </div>
      </div>
      
      <!-- Center Content Area -->
      <div class="center-content">
        <!-- 第一屏：嵌入过渡和可视化面板 -->
        <div class="first-screen" v-show="currentScreen === 'first'">
        <!-- Section A: Embedding Transition -->
        <div class="embedding-section">
          <div class="section-header">
            <span class="section-label">A</span>
            <h3>Embedding Transition</h3>
              <div class="save-button" @click="toggleScreen">{{ currentScreen === 'first' ? 'View Detail' : 'Back' }}</div>
          </div>
          
          <div class="case-selection">
            <span>Selected Cases:</span>
            <span class="case normal"></span>
            <span class="case herniated"></span>
            <span class="case bulging"></span>
            <span>Current ID: </span>
            <span class="current-case bulging"></span>
          </div>
          
          <div class="embedding-visualization">
            <div class="visualization-canvas">
              <!-- 嵌入式可视化散点�?-->
              <v-chart class="chart" :option="scatterOption" autoresize />
            </div>
            
              <!-- 添加聚类控制面板 -->
              <div class="clustering-controls">
                <div class="clustering-title">Clustering By:</div>
                <div class="clustering-options">
                  <div class="clustering-option" :class="{ active: clusteringBy === 'none' }" @click="setClusteringBy('none')">None</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'age' }" @click="setClusteringBy('age')">Age</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'disease' }" @click="setClusteringBy('disease')">Disease</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'visits' }" @click="setClusteringBy('visits')">Visit Count</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'other' }" @click="setClusteringBy('other')">Other</div>
                </div>
              </div>
          </div>
        </div>
        
        <!-- Section C: Visualization Panels -->
        <div class="visualization-section">
            <div class="section-header">
              <span class="section-label">C</span>
              <h3>Visualization Panels</h3>
            </div>
          
          <div class="visualization-panels">
            <div class="panel">
                <div class="panel-header">Disease Statistics</div>
              <div class="panel-controls">
                  <span class="control" :class="{ active: activeVisualizationMode === 'count' }" @click="activeVisualizationMode = 'count'">Case Count</span>
                  <span class="control" :class="{ active: activeVisualizationMode === 'trend' }" @click="activeVisualizationMode = 'trend'">Incidence Trend</span>
              </div>
              <div class="panel-content indicator-content">
                  <!-- 融合图表：根据模式切换显�?-->
                  <v-chart class="chart" :option="activeCombinedChartOption" autoresize />
              </div>
            </div>
            
            <div class="panel">
                <div class="panel-header">Disease Correlation</div>
              <div class="panel-controls">
                  <span class="filter-control">Filter</span>
                  <span class="label-control">Label</span>
              </div>
              <div class="panel-content text-distribution-content">
                  <!-- 热力图：疾病关联 -->
                  <v-chart class="chart" :option="diseaseCorrelationOption" autoresize />
              </div>
            </div>
            
            <div class="panel">
                <div class="panel-header">Seasonal Distribution</div>
              <div class="panel-controls">
                  <span class="control" :class="{ active: seasonalViewMode === 'quarter' }" @click="seasonalViewMode = 'quarter'">Quarter</span>
                  <span class="control" :class="{ active: seasonalViewMode === 'month' }" @click="seasonalViewMode = 'month'">Month</span>
              </div>
              <div class="panel-content image-gallery-content">
                  <!-- 季度/月度分布图表 -->
                  <v-chart class="chart" :option="seasonalDistributionOption" autoresize />
                </div>
                </div>
              </div>
            </div>
          </div>
          
        <!-- 第二屏：Detail区域 -->
        <div class="detail-section" v-show="currentScreen === 'detail'">
          <div class="detail-header">
            <div class="section-header">
              <span class="section-label">D</span>
              <h3>Detail</h3>
              <div class="save-button" @click="toggleScreen">{{ currentScreen === 'first' ? 'View Detail' : 'Back' }}</div>
            </div>
          </div>
            
          <div class="detail-content">
            <!-- 知识图谱展示区域 -->
            <div class="knowledge-graph-container">
            <div class="knowledge-graphs">
              <div class="graph-section">
                <div class="graph-header">{{ formatDiseaseName(selectedDisease1) }} Knowledge Graph</div>
                <div class="knowledge-graph">
                  <DiseasePatientNetwork 
                    :diseases="[getDiseaseData(selectedDisease1)]" 
                    :patients="getPatientsDataForDisease(selectedDisease1)" 
                    @node-click="handleNodeClick"
                  />
                  </div>
                      </div>
                  
              <div class="graph-section">
                <div class="graph-header">{{ formatDiseaseName(selectedDisease2) }} Knowledge Graph</div>
                <div class="knowledge-graph">
                  <DiseasePatientNetwork 
                    :diseases="[getDiseaseData(selectedDisease2)]" 
                    :patients="getPatientsDataForDisease(selectedDisease2)" 
                    @node-click="handleNodeClick"
                  />
                    </div>
                  </div>
                  </div>
                      </div>
            
            <!-- 修改图像比较部分，将图表移动到灰色框�?-->
            <div class="image-comparison-enhanced">
              <div class="raw-image-section">
                <div class="image-header">Patient 1: {{ selectedPatient1 }} ({{ formatDiseaseName(selectedDisease1) }})</div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.visits || '5' }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.hospitalized || '1' }}</span>
                  </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.age }}</span>
                </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.gender }}</span>
              </div>
            </div>
            
                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart class="patient-trend-chart" :option="patientTrend1Option" autoresize />
                  </div>
                    <!-- 移除指标图表 -->
                </div>
              </div>
              </div>
              
              <div class="cam-image-section">
                <div class="image-header">Patient 2: {{ selectedPatient2 }} ({{ formatDiseaseName(selectedDisease2) }})</div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.visits || '3' }}</span>
                  </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.hospitalized || '0' }}</span>
                </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.age }}</span>
              </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.gender }}</span>
            </div>
          </div>
                  
                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart class="patient-trend-chart" :option="patientTrend2Option" autoresize />
                    </div>
                    <!-- 移除指标图表 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 添加右侧面板拉环 -->
      <div class="panel-toggle" @click="toggleRightPanel">
        <div class="toggle-tab" :class="{ 'panel-hidden': isRightPanelHidden }">
          {{ isRightPanelHidden ? '◀' : '�? }}
        </div>
      </div>
      
      <!-- Right Panel -->
      <div class="right-panel" :class="{ 'hidden': isRightPanelHidden }">
        <div class="section-header">
          <span class="section-label">B</span>
          <h3>Comparison</h3>
        </div>
        
        <div class="comparison-section">
          <div class="comparison-item full-height">
            <span class="item-number">1</span>
            <h4>Patient Comparison</h4>
            
            <div class="patient-comparison">
              <div class="patient">
                <div class="patient-header">Patient 1</div>
                <div class="patient-selector">
                  <select class="patient-dropdown" v-model="selectedPatient1" @change="handlePatientSelection($event, 1)">
                    <option v-for="patientId in getPatientsForDisease(selectedDisease1)" :key="patientId" :value="patientId">
                      ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease1) }})
                    </option>
                  </select>
                </div>
                
                <div class="indicator-section">
                  <div class="indicator-header">
                    <span class="indicator-title">Key Indicators</span>
                    <span class="patient-info">{{ patientDetails[selectedPatient1]?.age }}y, {{ patientDetails[selectedPatient1]?.gender }}</span>
                  </div>
                  <div class="indicator-visualization">
                    <!-- SHAP图表 -->
                    <v-chart class="small-chart" :option="shapChartOption1" autoresize />
                  </div>
                </div>
                
                <div class="text-section">
                  <div class="text-header">Text</div>
                  <div class="text-content">
                    {{ patientDetails[selectedPatient1]?.textData }}
                  </div>
                </div>
                
                <div class="image-section">
                  <div class="image-header">Image</div>
                  <div class="image-content">
                    <img 
                      :src="patientDetails[selectedPatient1]?.imagePath" 
                      alt="Patient 1 Eye Image" 
                      class="patient-eye-image" 
                    />
                  </div>
                </div>
              </div>
              
              <div class="patient">
                <div class="patient-header">Patient 2</div>
                <div class="patient-selector">
                  <select class="patient-dropdown" v-model="selectedPatient2" @change="handlePatientSelection($event, 2)">
                    <option v-for="patientId in getPatientsForDisease(selectedDisease2)" :key="patientId" :value="patientId">
                      ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease2) }})
                    </option>
                  </select>
                </div>
                
                <div class="indicator-section">
                  <div class="indicator-header">
                    <span class="indicator-title">Key Indicators</span>
                    <span class="patient-info">{{ patientDetails[selectedPatient2]?.age }}y, {{ patientDetails[selectedPatient2]?.gender }}</span>
                  </div>
                  <div class="indicator-visualization">
                    <!-- SHAP图表 -->
                    <v-chart class="small-chart" :option="shapChartOption2" autoresize />
                  </div>
                </div>
                
                <div class="text-section">
                  <div class="text-header">Text</div>
                  <div class="text-content">
                    {{ patientDetails[selectedPatient2]?.textData }}
                  </div>
                </div>
                
                <div class="image-section">
                  <div class="image-header">Image</div>
                  <div class="image-content">
                    <img 
                      :src="patientDetails[selectedPatient2]?.imagePath" 
                      alt="Patient 2 Eye Image" 
                      class="patient-eye-image" 
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="section-header">
          <span class="section-label">D</span>
          <h3>AI Diagnostic Assistant</h3>
        </div>
        
        <!-- 修改：简化AI诊断功能，使用弹出对话框 -->
        <div class="diagnostic-assistant-section">
          <div class="diagnosis-card" :class="{ 'collapsed': isComparisonCollapsed }">
            <div class="card-header-row">
            <h4>AI-Powered Diagnostic Comparison</h4>
              <button class="toggle-button" @click="toggleComparisonPanel" v-if="showAIChat">
                {{ isComparisonCollapsed ? '�? : '�? }}
              </button>
            </div>
            
            <div class="card-content" v-show="!isComparisonCollapsed">
            <p class="diagnosis-description">Use AI to analyze and compare diagnostic information in real-time.</p>
            
            <div class="patient-selector">
              <div class="selector-group">
                <label>Patient 1:</label>
                <select class="patient-dropdown" v-model="selectedPatient1" @change="handlePatientSelection($event, 1)">
                  <option v-for="patientId in getPatientsForDisease(selectedDisease1)" :key="patientId" :value="patientId">
                    ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease1) }})
                  </option>
                </select>
              </div>
              <div class="selector-group">
                <label>Patient 2:</label>
                <select class="patient-dropdown" v-model="selectedPatient2" @change="handlePatientSelection($event, 2)">
                  <option v-for="patientId in getPatientsForDisease(selectedDisease2)" :key="patientId" :value="patientId">
                    ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease2) }})
                  </option>
                </select>
              </div>
            </div>
            
            <div class="ai-controls">
              <div class="ai-options">
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">Realtime analysis</span>
                </label>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">Include clinical data</span>
                </label>
                </div>
              </div>
            </div>
            
            <button class="action-button compare-button" @click="startComparison" v-if="!showAIChat">
              <span class="button-icon">🔍</span> Run AI Comparison
            </button>
          </div>
          
          <!-- 修改AI聊天面板结构，修复布局问题 -->
          <div class="ai-chat-panel" v-if="showAIChat">
            <div class="panel-header">
              <h4>AI Diagnostic Chat</h4>
              <div class="tab-buttons">
                <button class="tab-button" 
                        :class="{ active: activeChatTab === 'chat' }" 
                        @click="activeChatTab = 'chat'">Chat</button>
                <button class="tab-button" 
                        :class="{ active: activeChatTab === 'analysis' }" 
                        @click="activeChatTab = 'analysis'">Analysis</button>
            </div>
          </div>
            
            <!-- 聊天面板 -->
            <div v-if="activeChatTab === 'chat'" class="chat-container">
              <div class="chat-messages-wrapper">
                <div class="chat-messages" ref="chatMessages">
                  <div v-for="(message, index) in chatMessages" :key="index" 
                       :class="['message', message.sender === 'ai' ? 'ai-message' : 'user-message']">
                    <div class="message-avatar">
                      <span v-if="message.sender === 'ai'">🤖</span>
                      <span v-else>👨‍⚕�?/span>
        </div>
                    <div class="message-content">
                      <div class="message-text" v-html="message.text"></div>
                      <div class="message-time">{{ message.time }}</div>
      </div>
    </div>
                  <div v-if="isAiTyping" class="message ai-message typing-indicator">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                      <div class="typing-dots">
                        <span></span><span></span><span></span>
                      </div>
                    </div>
                  </div>
                </div>
        </div>
        
              <!-- 移除建议问题区域，只保留输入�?-->
              <div class="chat-input-container">
                <input type="text" class="chat-input" v-model="userMessage" 
                       @keyup.enter="sendMessage" placeholder="Ask AI about the comparison...">
                <button class="chat-send-button" @click="sendMessage">
                  <span>Send</span>
                </button>
              </div>
          </div>
          
            <!-- 分析结果面板 -->
            <div v-if="activeChatTab === 'analysis'" class="analysis-container">
              <div class="analysis-section">
                <h5>Key Differences</h5>
            <div class="insight-item">
              <span class="insight-icon">🔍</span>
              <div class="insight-content">
                <strong>Structural Difference:</strong> 
                <span class="insight-text">Patient 1 shows disc protrusion at C5-C6 level, while Patient 2 has normal alignment.</span>
              </div>
            </div>
            <div class="insight-item">
              <span class="insight-icon">📊</span>
              <div class="insight-content">
                <strong>Signal Intensity:</strong>
                <span class="insight-text">Higher T2 signal intensity in Patient 1 indicates potential inflammation.</span>
              </div>
            </div>
          </div>
          
              <div class="analysis-section">
                <h5>Similarities</h5>
            <div class="insight-item">
              <span class="insight-icon">🔄</span>
              <div class="insight-content">
                <strong>Vertebral Alignment:</strong>
                <span class="insight-text">Both patients maintain normal cervical lordosis.</span>
              </div>
            </div>
          </div>
          
              <div class="analysis-section">
                <h5>Recommendations</h5>
            <div class="insight-item">
              <span class="insight-icon">💡</span>
              <div class="insight-content">
                <strong>Learning Point:</strong>
                <span class="insight-text">Compare the disc margin contour to differentiate between bulging and herniation.</span>
            </div>
          </div>
        </div>
        
              <div class="analysis-actions">
                <button class="action-button secondary-button">
            <span class="button-icon">📥</span> Download Report
          </button>
                <button class="action-button secondary-button" @click="activeChatTab = 'chat'">
                  <span class="button-icon">💬</span> Ask Follow-up Questions
          </button>
        </div>
      </div>
    </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-ignore
import DiseasePatientNetwork from './DiseasePatientNetwork.vue';
import * as echarts from 'echarts';

// Add data type definitions
interface DiseaseData {
  macular_edema: number;
  open_angle_glaucoma: number;
  closed_angle_glaucoma: number;
  diabetic_maculopathy: number;
  retinitis_pigmentosa: number;
  branch_retinal_vein_occlusion: number;
}

// 2. Add interfaces for patient data
interface PatientDetail {
  disease: string;
  age: number;
  gender: string;
  visits: number;
  hospitalized: number;
  indicators: Record<string, any>;
  textData: string;
  imagePath: string;
}

interface PatientData {
  id: string;
  disease: string;
  details: PatientDetail;
}

interface QuarterlyData {
  Q1: DiseaseData;
  Q2: DiseaseData;
  Q3: DiseaseData;
  Q4: DiseaseData;
}

interface MonthlyData {
  Jan: DiseaseData;
  Feb: DiseaseData;
  Mar: DiseaseData;
  Apr: DiseaseData;
  May: DiseaseData;
  Jun: DiseaseData;
  Jul: DiseaseData;
  Aug: DiseaseData;
  Sep: DiseaseData;
  Oct: DiseaseData;
  Nov: DiseaseData;
  Dec: DiseaseData;
}

export default {
  name: 'DiagnosisAssistant',
  components: {
    DiseasePatientNetwork
  },
  data() {
    return {
      // 添加右侧面板隐藏状�?      isRightPanelHidden: false,
      selectedCases: {
        normal: 0,
        herniated: 0,
        bulging: 0
      },
      currentCase: {
        id: '',
        label: '',
        description: ''
      },
      comparisonData: [
        {
          cardId: '',
          label: '',
          notes: ''
        },
        {
          cardId: '',
          label: '',
          notes: ''
        }
      ],
      // 添加当前屏幕状�?      currentScreen: 'first',
      // AI对话相关状�?      showAIDialog: false,
      showAIChat: false,
      activeChatTab: 'chat',
      // 聊天相关数据
      chatMessages: [
        {
          sender: 'ai',
          text: 'Hello! I am your AI diagnostic assistant. How can I help you analyze the comparison between these patients?',
          time: '12:30'
        }
      ],
      userMessage: '',
      isAiTyping: false,
      // 添加聚类选项，修改默认值为 'none'
      clusteringBy: 'none' as 'none' | 'age' | 'disease' | 'visits' | 'other', // 默认为不聚类
      // 散点图配�?      scatterOption: {
        animation: true,
        animationDuration: 1500,
        animationEasing: 'cubicOut',
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        grid: {
          left: '2%',
          right: '2%',
          top: '2%',
          bottom: '2%',
          containLabel: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            return `<div style="padding: 8px 12px; min-width: 180px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">ID: ${params.dataIndex}</div>
              <div style="margin-bottom: 3px;">Type: ${params.seriesIndex === 0 ? 'Patient' : params.seriesIndex === 1 ? 'Group B' : 'Group C'}</div>
              <div>Value: (${params.data[0].toFixed(2)}, ${params.data[1].toFixed(2)})</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100; min-width: 180px;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        series: [
          {
            type: 'scatter',
            symbolSize: function() {
              return Math.random() * 6 + 6; // 6-12 的随机大�?            },
            itemStyle: {
              opacity: 0.85,
              shadowBlur: 8,
              shadowColor: 'rgba(33, 150, 243, 0.4)',
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [{
                  offset: 0, color: '#2196f3' // 浅蓝�?                }, {
                  offset: 1, color: '#1565c0' // 深蓝�?                }]
              }
            },
            emphasis: {
              scale: true,
              itemStyle: {
                shadowBlur: 15,
                borderColor: '#fff',
                borderWidth: 2,
                opacity: 1
              }
            },
            data: this.generateRandomScatterData(400, null), // 增加点的数量
            z: 10
          },
          // 添加连接线效�?          {
            type: 'graph',
            layout: 'none',
            coordinateSystem: 'cartesian2d',
            symbolSize: [10, 20], // 添加不同大小的中心点
            symbol: 'circle',
            itemStyle: {
              color: function(params: any) {
                // 为中心点设置颜色
                if (params.dataIndex < 6) { // �?个点是中心点
                  const colors = ['#2196f3', '#f44336', '#4caf50', '#ff9800', '#9c27b0', '#607d8b'];
                  return colors[params.dataIndex % 6];
                }
                return '#ddd';
              },
              opacity: 0.8,
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 1,
              color: '#aaa',
              opacity: 0.6,
              curveness: 0
            },
            emphasis: {
              lineStyle: {
                width: 2,
                color: '#555'
              }
            },
            data: this.generateClusterCenters(),
            edges: this.generateClusterEdges(400),
            z: 1
          }
        ]
      },
      // 柱状图配�?      barOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Value',
            type: 'bar',
            barWidth: '60%',
            data: [10, 52, 200, 334, 390, 330, 220]
          }
        ]
      },
      // 折线图配�?      lineOption: {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      // 小型柱状图配�?
      smallBarOption: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'bar',
            data: [12, 24, 36, 48, 60],
            itemStyle: {
              color: '#2196f3'
            }
          }
        ]
      },
      // 小型柱状图配�?
      smallBarOption2: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'bar',
            data: [60, 48, 36, 24, 12],
            itemStyle: {
              color: '#4caf50'
            }
          }
        ]
      },
      // 饼图配置1
      pieOption1: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: 'Normal' },
              { value: 735, name: 'Bulging' },
              { value: 580, name: 'Herniated' }
            ]
          }
        ]
      },
      // 饼图配置2
      pieOption2: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 500, name: 'Normal' },
              { value: 900, name: 'Bulging' },
              { value: 600, name: 'Herniated' }
            ]
          }
        ]
      },
      // 修改：Raw图像下方的图表，移除背景相关设置
      rawImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7', '8'],
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: [28, 32, 36, 34, 45, 33, 28, 24],
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            itemStyle: {
              color: '#2196f3'
            },
            lineStyle: {
              width: 2,
              color: '#2196f3'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(33, 150, 243, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(33, 150, 243, 0.05)'
                  }
                ]
              }
            }
          }
        ]
      },
      // 修改：CAM图像下方的图表，移除背景相关设置
      camImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: '50%',
            data: [
              {
                value: 60,
                itemStyle: {
                  color: '#f44336'
                }
              },
              {
                value: 45,
                itemStyle: {
                  color: '#f44336'
                }
              },
              {
                value: 30,
                itemStyle: {
                  color: '#2196f3'
                }
              },
              {
                value: 20,
                itemStyle: {
                  color: '#2196f3'
                }
              },
              {
                value: 15,
                itemStyle: {
                  color: '#4caf50'
                }
              }
            ]
          }
        ]
      },
      // 添加新的状态变�?      isComparisonCollapsed: false,
      // 眼科疾病数据
      ophthalmicDiseases: [
        { 
          id: 'macular_edema', 
          name: 'Macular Edema', 
          patients: [
            '80145987', '80156560', '80136152', '80145988', '80156561', 
            '80136153', '80145989', '80156562', '80136154', '80145990',
            '80156563', '80136155', '80145991', '80156564', '80136156',
            '80156565', '80136157', '80145992', '80156566', '80136158',
            '80145993', '80156567', '80136159', '80145994', '80156568',
            '80136160', '80145995', '80156569', '80136161', '80145996',
            '80156570', '80136162', '80145997', '80156571', '80136163',
            '80145998', '80156572', '80136164', '80145999', '80156573'
          ],
          incidence: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
          correlations: {
            'open_angle_glaucoma': 0.3,
            'closed_angle_glaucoma': 0.2,
            'diabetic_maculopathy': 0.8,
            'retinitis_pigmentosa': 0.4,
            'branch_retinal_vein_occlusion': 0.6
          }
        },
        { 
          id: 'open_angle_glaucoma', 
          name: 'Open-Angle Glaucoma', 
          patients: [
            '80145123', '80156789', '80136234', '80145124', '80156790', 
            '80136235', '80145125', '80156791', '80136236', '80145126',
            '80156792', '80136237', '80145127', '80156793', '80136238',
            '80145128', '80156794', '80136239', '80145129', '80156795',
            '80136240', '80145130', '80156796', '80136241', '80145131',
            '80156797', '80136242', '80145132', '80156798', '80136243',
            '80145133', '80156799', '80136244', '80145134', '80156800',
            '80136245', '80145135', '80156801', '80136246', '80145136'
          ],
          incidence: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
          correlations: {
            'macular_edema': 0.3,
            'closed_angle_glaucoma': 0.7,
            'diabetic_maculopathy': 0.4,
            'retinitis_pigmentosa': 0.3,
            'branch_retinal_vein_occlusion': 0.2
          }
        },
        { 
          id: 'closed_angle_glaucoma', 
          name: 'Closed-Angle Glaucoma', 
          patients: [
            '80145111', '80156222', '80136333', '80145112', '80156223', 
            '80136334', '80145113', '80156224', '80136335', '80145114',
            '80156225', '80136336', '80145115', '80156226', '80136337',
            '80145116', '80156227', '80136338', '80145117', '80156228',
            '80136339', '80145118', '80156229', '80136340', '80145119',
            '80156230', '80136341', '80145120', '80156231', '80136342',
            '80145121', '80156232', '80136343', '80145122', '80156233'
          ],
          incidence: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
          correlations: {
            'macular_edema': 0.2,
            'open_angle_glaucoma': 0.7,
            'diabetic_maculopathy': 0.3,
            'retinitis_pigmentosa': 0.2,
            'branch_retinal_vein_occlusion': 0.1
          }
        },
        { 
          id: 'diabetic_maculopathy', 
          name: 'Diabetic Maculopathy', 
          patients: [
            '80145444', '80156555', '80136666', '80145445', '80156556', 
            '80136667', '80145446', '80156557', '80136668', '80145447',
            '80156558', '80136669', '80145448', '80156559', '80136670',
            '80145449', '80156550', '80136671', '80145450', '80156551',
            '80145451', '80156552', '80136672', '80145452', '80156553',
            '80136673', '80145453', '80156554', '80136674', '80145454',
            '80156560', '80136675', '80145455', '80156561', '80136676',
            '80145456', '80156562', '80136677', '80145457', '80156563'
          ],
          incidence: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
          correlations: {
            'macular_edema': 0.8,
            'open_angle_glaucoma': 0.4,
            'closed_angle_glaucoma': 0.3,
            'retinitis_pigmentosa': 0.2,
            'branch_retinal_vein_occlusion': 0.5
          }
        },
        { 
          id: 'retinitis_pigmentosa', 
          name: 'Retinitis Pigmentosa', 
          patients: [
            '80145777', '80156888', '80136999', '80145778', '80156889', 
            '80137000', '80145779', '80156890', '80137001', '80145780',
            '80156891', '80137002', '80145781', '80156892', '80137003',
            '80145782', '80156893', '80137004', '80145783', '80156894',
            '80137005', '80145784', '80156895', '80137006', '80145785',
            '80156896', '80137007', '80145786', '80156897', '80137008'
          ],
          incidence: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
          correlations: {
            'macular_edema': 0.4,
            'open_angle_glaucoma': 0.3,
            'closed_angle_glaucoma': 0.2,
            'diabetic_maculopathy': 0.2,
            'branch_retinal_vein_occlusion': 0.3
          }
        },
        { 
          id: 'branch_retinal_vein_occlusion', 
          name: 'Branch Retinal Vein Occlusion', 
          patients: [
            '80146001', '80157001', '80137101', '80146002', '80157002', 
            '80137102', '80146003', '80157003', '80137103', '80146004',
            '80157004', '80137104', '80146005', '80157005', '80137105',
            '80146006', '80157006', '80137106', '80146007', '80157007',
            '80137107', '80146008', '80157008', '80137108', '80146009',
            '80157009', '80137109', '80146010', '80157010', '80137110',
            '80146011', '80157011', '80137111', '80146012', '80157012'
          ],
          incidence: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
          correlations: {
            'macular_edema': 0.6,
            'open_angle_glaucoma': 0.2,
            'closed_angle_glaucoma': 0.1,
            'diabetic_maculopathy': 0.5,
            'retinitis_pigmentosa': 0.3
          }
        }
      ],
      
      // 选中的疾病用于比�?      selectedLeftDisease: 'macular_edema',
      selectedRightDisease: 'open_angle_glaucoma',
      
      // 眼科疾病图像数据
      diseaseImages: {
        macular_edema: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg'
        },
        open_angle_glaucoma: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg'
        },
        closed_angle_glaucoma: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-oct.jpg'
        },
        diabetic_maculopathy: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-oct.jpg'
        },
        retinitis_pigmentosa: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-oct.jpg'
        },
        branch_retinal_vein_occlusion: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-oct.jpg'
        }
      },
      
      // 当前选中的疾病图�?      currentDiseaseImages: {
        fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-fundus.jpg',
        oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-oct.jpg'
      },
      
      // 选中的疾�?      selectedDisease1: 'macular_edema',
      selectedDisease2: 'open_angle_glaucoma',
      
      // 柱状图：疾病数量
      diseaseCaseCountOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: [
              'Macular\nEdema', 
              'Open-Angle\nGlaucoma', 
              'Closed-Angle\nGlaucoma',
              'Diabetic\nMaculopathy',
              'Retinitis\nPigmentosa', 
              'Branch Retinal\nVein Occlusion'
            ],
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'Number of Cases',
            nameLocation: 'middle',
            nameGap: 40
          }
        ],
        series: [
          {
            name: 'Cases',
            type: 'bar',
            barWidth: '60%',
            data: [
              {value: 20, itemStyle: {color: '#1976d2'}},
              {value: 24, itemStyle: {color: '#2196f3'}},
              {value: 17, itemStyle: {color: '#42a5f5'}},
              {value: 20, itemStyle: {color: '#64b5f6'}},
              {value: 14, itemStyle: {color: '#90caf9'}},
              {value: 17, itemStyle: {color: '#bbdefb'}}
            ],
            label: {
              show: true,
              position: 'top',
              color: '#333'
            }
          }
        ]
      },
      
      // 折线图：疾病发病趋势
      diseaseIncidenceTrendOption: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: [
            'Macular Edema', 
            'Open-Angle Glaucoma', 
            'Closed-Angle Glaucoma',
            'Diabetic Maculopathy',
            'Retinitis Pigmentosa', 
            'Branch Retinal Vein Occlusion'
          ],
          type: 'scroll',
          orient: 'horizontal',
          bottom: 0,
          textStyle: {
            fontSize: 10
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        },
        yAxis: {
          type: 'value',
          name: 'Incidence per 100,000',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [
          {
            name: 'Macular Edema',
            type: 'line',
            data: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
            itemStyle: {color: '#0d47a1'},
            lineStyle: {width: 2, color: '#0d47a1'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Open-Angle Glaucoma',
            type: 'line',
            data: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
            itemStyle: {color: '#1565c0'},
            lineStyle: {width: 2, color: '#1565c0'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Closed-Angle Glaucoma',
            type: 'line',
            data: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
            itemStyle: {color: '#1976d2'},
            lineStyle: {width: 2, color: '#1976d2'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Diabetic Maculopathy',
            type: 'line',
            data: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
            itemStyle: {color: '#2196f3'},
            lineStyle: {width: 2, color: '#2196f3'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Retinitis Pigmentosa',
            type: 'line',
            data: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
            itemStyle: {color: '#64b5f6'},
            lineStyle: {width: 2, color: '#64b5f6'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Branch Retinal Vein Occlusion',
            type: 'line',
            data: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
            itemStyle: {color: '#90caf9'},
            lineStyle: {width: 2, color: '#90caf9'},
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      },
      
      // 热力图：疾病关联
      diseaseCorrelationOption: {
        tooltip: {
          position: 'top',
          formatter: function(params: any) {
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${params.name}</div>
              <div style="font-size: 13px;">Correlation: ${params.value[2].toFixed(2)}</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        animation: true,
        grid: {
          left: '2%',
          right: '7%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            'ME', 'OAG', 'CAG', 'DM', 'RP', 'BRVO'
          ],
          splitArea: {
            show: true
          },
          axisLabel: {
            fontSize: 10,
            interval: 0
          }
        },
        yAxis: {
          type: 'category',
          data: [
            'ME', 'OAG', 'CAG', 'DM', 'RP', 'BRVO'
          ],
          splitArea: {
            show: true
          },
          axisLabel: {
            fontSize: 10,
            interval: 0
          }
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: 0,
          inRange: {
            color: ['#f3f8fe', '#deeafe', '#c6dbfd', '#90c1f9', '#5ba3f4', '#1e88e5', '#0d47a1']
          }
        },
        series: [{
          name: 'Disease Correlation',
          type: 'heatmap',
          data: [
            // ME correlations
            [0, 0, 1.0], [0, 1, 0.3], [0, 2, 0.2], [0, 3, 0.8], [0, 4, 0.4], [0, 5, 0.6],
            // OAG correlations
            [1, 0, 0.3], [1, 1, 1.0], [1, 2, 0.7], [1, 3, 0.4], [1, 4, 0.3], [1, 5, 0.2],
            // CAG correlations
            [2, 0, 0.2], [2, 1, 0.7], [2, 2, 1.0], [2, 3, 0.3], [2, 4, 0.2], [2, 5, 0.1],
            // DM correlations
            [3, 0, 0.8], [3, 1, 0.4], [3, 2, 0.3], [3, 3, 1.0], [3, 4, 0.2], [3, 5, 0.5],
            // RP correlations
            [4, 0, 0.4], [4, 1, 0.3], [4, 2, 0.2], [4, 3, 0.2], [4, 4, 1.0], [4, 5, 0.3],
            // BRVO correlations
            [5, 0, 0.6], [5, 1, 0.2], [5, 2, 0.1], [5, 3, 0.5], [5, 4, 0.3], [5, 5, 1.0]
          ],
          label: {
            show: true,
            formatter: function(params: any) {
              return params.data[2].toFixed(1);
            },
            fontSize: 10,
            color: function(params: any) {
              return params.data[2] > 0.5 ? '#fff' : '#333';
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      },
      
      // 添加患者详细数�?      patientDetails: {
        // 黄斑水肿患�?        '80145987': {
          disease: 'macular_edema',
          age: 65,
          gender: 'Male',
          visits: 5,
          hospitalized: 1,
          indicators: {
            visual_acuity: 0.4,
            intraocular_pressure: 18,
            central_macular_thickness: 450,
            hba1c: 7.8,
            blood_pressure: '145/90'
          },
          textData: 'Patient presents with decreased visual acuity and central vision distortion. OCT shows increased retinal thickness with intraretinal cysts. History of type 2 diabetes for 12 years with suboptimal glycemic control.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg'
        },
        '80156560': {
          disease: 'macular_edema',
          age: 58,
          gender: 'Female',
          visits: 7,
          hospitalized: 2,
          indicators: {
            visual_acuity: 0.5,
            intraocular_pressure: 17,
            central_macular_thickness: 380,
            hba1c: 8.2,
            blood_pressure: '138/85'
          },
          textData: 'Patient complains of blurry vision in both eyes, worse in the morning. Fundus examination reveals hard exudates and microaneurysms. Patient has hypertension and poorly controlled diabetes.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg'
        },
        
        // 开角型青光眼患�?        '80145123': {
          disease: 'open_angle_glaucoma',
          age: 72,
          gender: 'Male',
          visits: 3,
          hospitalized: 0,
          indicators: {
            visual_acuity: 0.7,
            intraocular_pressure: 28,
            cup_to_disc_ratio: 0.8,
            visual_field_md: -12.5,
            corneal_thickness: 520
          },
          textData: 'Patient reports gradual peripheral vision loss. Examination shows increased intraocular pressure and optic nerve cupping. Family history of glaucoma in father and paternal grandmother.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg'
        },
        '80156789': {
          disease: 'open_angle_glaucoma',
          age: 68,
          gender: 'Female',
          visits: 4,
          hospitalized: 0,
          indicators: {
            visual_acuity: 0.8,
            intraocular_pressure: 25,
            cup_to_disc_ratio: 0.7,
            visual_field_md: -8.3,
            corneal_thickness: 535
          },
          textData: 'Patient is asymptomatic but was referred after routine eye exam showed elevated intraocular pressure. Gonioscopy confirms open angles. OCT shows thinning of the retinal nerve fiber layer.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg'
        },
        
        // 闭角型青光眼患�?        '80145111': {
          disease: 'closed_angle_glaucoma',
          age: 62,
          gender: 'Female',
          visits: 6,
          hospitalized: 3,
          indicators: {
            visual_acuity: 0.3,
            intraocular_pressure: 42,
            anterior_chamber_depth: 2.1,
            corneal_edema: 'Moderate',
            pupil_reaction: 'Sluggish'
          },
          textData: 'Patient presented with acute eye pain, headache, and nausea. Examination showed mid-dilated pupil, corneal edema, and shallow anterior chamber. Emergency iridotomy was performed.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg'
        },
        
        // 糖尿病性黄斑病变患�?        '80145444': {
          disease: 'diabetic_maculopathy',
          age: 55,
          gender: 'Male',
          visits: 8,
          hospitalized: 2,
          indicators: {
            visual_acuity: 0.4,
            hba1c: 9.2,
            microaneurysms: 'Numerous',
            hard_exudates: 'Present',
            macular_edema: 'Clinically significant'
          },
          textData: 'Patient with 15-year history of type 2 diabetes presents with gradually worsening vision. Fundus photography shows multiple microaneurysms, dot and blot hemorrhages, and hard exudates in a circinate pattern.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg'
        },
        
        // 视网膜色素变性患�?        '80145777': {
          disease: 'retinitis_pigmentosa',
          age: 42,
          gender: 'Male',
          visits: 12,
          hospitalized: 1,
          indicators: {
            visual_acuity: 0.6,
            visual_field: 'Constricted',
            erg_amplitude: 'Reduced',
            bone_spicules: 'Present',
            night_vision: 'Severely impaired'
          },
          textData: 'Patient reports difficulty seeing in dim light and progressive loss of peripheral vision. Fundus examination reveals bone spicule pigmentation, attenuated vessels, and waxy pallor of the optic disc.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg'
        },
        
        // 视网膜分支静脉阻塞患�?        '80146001': {
          disease: 'branch_retinal_vein_occlusion',
          age: 68,
          gender: 'Female',
          visits: 9,
          hospitalized: 4,
          indicators: {
            visual_acuity: 0.5,
            intraocular_pressure: 19,
            hemorrhages: 'Flame-shaped',
            cotton_wool_spots: 'Present',
            macular_edema: 'Present'
          },
          textData: 'Patient noticed sudden, painless vision loss in the right eye 2 weeks ago. Fundus examination shows flame-shaped hemorrhages in the superotemporal quadrant with cotton wool spots. Patient has history of hypertension.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg'
        }
      } as { [key: string]: any },
      
      // 当前选择的患者用于对�?      selectedPatient1: '80145987', // 默认为第一个黄斑水肿患�?      selectedPatient2: '80145123', // 默认为第一个开角型青光眼患�?      
      // SHAP图配�?      shapChartOption1: null as any,
      shapChartOption2: null as any,
      
      // 添加可视化模式切换状�?      activeVisualizationMode: 'count',
      
      // 添加季度数据
      quarterlyData: {
        'Q1': {
          'macular_edema': 15,
          'open_angle_glaucoma': 49,
          'closed_angle_glaucoma': 17,
          'diabetic_maculopathy': 32,
          'retinitis_pigmentosa': 11,
          'branch_retinal_vein_occlusion': 20
        },
        'Q2': {
          'macular_edema': 25,
          'open_angle_glaucoma': 56,
          'closed_angle_glaucoma': 25,
          'diabetic_maculopathy': 45,
          'retinitis_pigmentosa': 15,
          'branch_retinal_vein_occlusion': 28
        },
        'Q3': {
          'macular_edema': 30,
          'open_angle_glaucoma': 68,
          'closed_angle_glaucoma': 27,
          'diabetic_maculopathy': 58,
          'retinitis_pigmentosa': 19,
          'branch_retinal_vein_occlusion': 33
<template>
  <div class="diagnosis-assistant">
    <header class="header">
      <h1>DiagnosisAssistant</h1>
      <span class="subtitle">A learning Tool for Medical Diagnosis</span>
    </header>
    
    <div class="main-content" :class="{ 'hidden-panel': isRightPanelHidden }">
      <!-- Left Panel -->
      <div class="left-panel">
        <h2>Panel</h2>
        
        <!-- 新增：导入功能区�?-->
        <div class="panel-section import-section">
          <h3>Import/Export:</h3>
          <div class="button-group">
            <button class="panel-button primary">
              <span class="button-icon">📂</span> Import Data
            </button>
            <button class="panel-button">
              <span class="button-icon">📊</span> Import Image
            </button>
            <button class="panel-button">
              <span class="button-icon">💾</span> Export Results
            </button>
          </div>
