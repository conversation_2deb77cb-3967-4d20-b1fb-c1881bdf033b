<template>
  <div class="disease-view">
    <h1>Disease-Patient Knowledge Graph</h1>
    <div class="graph-container">
      <DiseasePatientNetwork 
        :diseases="diseases" 
        :patients="patients" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DiseasePatientNetwork from '../components/DiseasePatientNetwork.vue';

// Sample data - this would typically come from an API
const diseases = ref([
  { id: 'cataract', name: 'Cataract', color: '#ff9642' },
  { id: 'glaucoma', name: 'Glaucoma', color: '#4287f5' }
]);

const patients = ref([
  // Cataract patients
  { id: '80145123', diseaseId: 'cataract' },
  { id: '80145789', diseaseId: 'cataract' },
  { id: '80145234', diseaseId: 'cataract' },
  
  // Glaucoma patients
  { id: '80145987', diseaseId: 'glaucoma' },
  { id: '80145560', diseaseId: 'glaucoma' },
  { id: '80145152', diseaseId: 'glaucoma' }
]);
</script>

<style scoped>
.disease-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.graph-container {
  height: 600px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>