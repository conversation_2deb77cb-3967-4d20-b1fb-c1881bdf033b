{"name": "bibm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tensorflow-models/universal-sentence-encoder": "^1.3.3", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.10.0", "d3": "^7.9.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "hdbscanjs": "^1.0.12", "papaparse": "^5.5.3", "umap-js": "^1.4.0", "vue": "^3.5.13", "vue-echarts": "^6.7.3", "vue-router": "^4.5.1"}, "devDependencies": {"@types/d3": "^7.4.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}