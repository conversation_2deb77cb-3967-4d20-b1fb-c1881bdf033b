<template>
  <div class="diagnosis-assistant">
    <header class="header">
      <h1>DiagnosisAssistant</h1>
      <span class="subtitle">A learning Tool for Medical Diagnosis</span>
    </header>

    <div class="main-content">
      <!-- Left Panel -->
      <div class="left-panel">
        <h2>Panel</h2>

        <!-- 新增：导入功能区域 -->
        <div class="panel-section import-section">
          <h3>Import/Export:</h3>
          <div class="button-group">
            <button class="panel-button primary">
              <span class="button-icon">📂</span> Import Data
            </button>
            <button class="panel-button">
              <span class="button-icon">📊</span> Import Image
            </button>
            <button class="panel-button">
              <span class="button-icon">💾</span> Export Results
            </button>
          </div>
        </div>

        <!-- 新增：疾病选择 -->
        <div class="panel-section disease-section">
          <h3>Case Selection:</h3>
          <div class="disease-comparison">
            <div class="selector-group">
              <label>Disease 1:</label>
              <select
                class="disease-dropdown"
                v-model="selectedDisease1"
                @change="updateDiseaseDisplay"
              >
                <option
                  v-for="disease in diseases"
                  :key="disease"
                  :value="disease"
                >
                  {{ disease }}
                </option>
              </select>
            </div>
            <div class="selector-group">
              <label>Disease 2:</label>
              <select
                class="disease-dropdown"
                v-model="selectedDisease2"
                @change="updateDiseaseDisplay"
              >
                <option
                  v-for="disease in diseases"
                  :key="disease"
                  :value="disease"
                >
                  {{ disease }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>Dataset:</h3>
          <div class="dataset-item"></div>
        </div>

        <div class="panel-section">
          <h3>Datainfo:</h3>
          <div class="datainfo-item">Diagnostic Task:</div>
          <div class="datainfo-value"></div>

          <div class="datainfo-item">Diagnostic Entry:</div>
          <div class="datainfo-value"></div>

          <div class="datainfo-item">No. of cases:</div>
          <div class="datainfo-value"></div>
        </div>

        <div class="panel-section">
          <h3>Modality:</h3>
          <div class="modality-item selected"></div>
          <div class="modality-item"></div>
          <div class="modality-item"></div>
        </div>

        <div class="panel-section">
          <h3>Composition:</h3>
        </div>

        <div class="panel-section">
          <h3>Body Part:</h3>
          <div class="body-part-item"></div>
          <div class="body-part-item">Cross-section:</div>
          <div class="body-part-item"></div>
          <div class="body-part-item">Resolution:</div>
          <div class="body-part-item"></div>
        </div>

        <div class="panel-section">
          <h3>Model Performance:</h3>
        </div>
      </div>

      <!-- Center Content Area -->
      <div class="center-content">
        <!-- 第一屏：嵌入过渡和可视化面板 -->
        <div class="first-screen" v-show="currentScreen === 'first'">
          <!-- Section A: Embedding Transition -->
          <div class="embedding-section">
            <div class="section-header">
              <span class="section-label">A</span>
              <h3>Patient Data Analysis</h3>
              <div class="save-button" @click="toggleScreen">
                {{ currentScreen === "first" ? "View Detail" : "Back" }}
              </div>
            </div>

            <!-- Replace existing content with PatientClustering component -->
            <PatientClustering
              @clustering-method-changed="handleClusteringMethodChanged"
              @patients-selected="handlePatientsSelected" />
          </div>

          <!-- Section C: Visualization Panels -->
          <div class="visualization-section">
            <div class="section-header">
              <span class="section-label">C</span>
              <h3>Visualization Panels</h3>
            </div>

            <div class="visualization-panels">
              <div class="panel">
                <div class="panel-header">Disease Statistics</div>
                <div class="panel-controls">
                  <span
                    class="control"
                    :class="{ active: activeVisualizationMode === 'count' }"
                    @click="activeVisualizationMode = 'count'"
                    >药品雷达图</span
                  >
                  <span
                    class="control"
                    :class="{ active: activeVisualizationMode === 'trend' }"
                    @click="activeVisualizationMode = 'trend'"
                    >疾病标签云</span
                  >
                </div>
                <div class="panel-content indicator-content">
                  <!-- 单一组件显示，内部根据选择切换显示雷达图或标签云 -->
                  <MedicationRadarChart :display-mode="activeVisualizationMode" />
                </div>
              </div>

              <div class="panel">
                <div class="panel-header">Disease Correlation</div>
                <div class="panel-controls">
                  <!-- <span class="filter-control">Filter</span>
                  <span class="label-control">Label</span> -->
                  <span 
                    class="control"
                    :class="{ active: correlationMode === 'standard' }"
                    @click="correlationMode = 'standard'"
                  >Standard</span>
                  <span 
                    class="control"
                    :class="{ active: correlationMode === 'apriori' }"
                    @click="switchToAprioriMode"
                  >Apriori</span>
                </div>
                <div class="panel-content text-distribution-content">
                  <!-- 热力图：疾病关联 -->
                  <v-chart
                    v-if="correlationMode === 'standard'"
                    class="chart"
                    :option="diseaseCorrelationOption"
                    autoresize
                  />
                  <v-chart
                    v-else
                    class="chart"
                    ref="correlationChart"
                    :option="aprioriCorrelationOption || {}"
                    autoresize
                  />
                </div>
              </div>

              <div class="panel">
                <div class="panel-header">Seasonal Distribution</div>
                <div class="panel-controls">
                  <span
                    class="control"
                    :class="{ active: seasonalViewMode === 'quarter' }"
                    @click="seasonalViewMode = 'quarter'"
                    >Quarter</span
                  >
                  <span
                    class="control"
                    :class="{ active: seasonalViewMode === 'month' }"
                    @click="seasonalViewMode = 'month'"
                    >Month</span
                  >
                </div>
                <div class="panel-content image-gallery-content">
                  <!-- 季度/月度分布图表 -->
                  <v-chart
                    class="chart"
                    :option="seasonalDistributionOption"
                    autoresize
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二屏：Detail区域 -->
        <div class="detail-section" v-show="currentScreen === 'detail'">
          <div class="detail-header">
            <div class="section-header">
              <span class="section-label">D</span>
              <h3>Detail</h3>
              <div class="save-button" @click="toggleScreen">
                {{ currentScreen === "first" ? "View Detail" : "Back" }}
              </div>
            </div>
          </div>

          <div class="detail-content">
            <!-- 知识图谱展示区域 -->
            <div class="knowledge-graph-container">
              <div class="knowledge-graphs">
                <div class="graph-section">
                  <div class="graph-header">
                    {{ formatDiseaseName(selectedDisease1) }} Knowledge Graph (Max 50 Patients)
                  </div>
                  <div class="knowledge-graph">
                    <DiseasePatientNetwork
                      :diseases="[getDiseaseData(selectedDisease1)]"
                      :patients="getPatientsFromClustering(selectedDisease1)"
                      @node-click="handleNodeClick"
                    />
                  </div>
                </div>

                <div class="graph-section">
                  <div class="graph-header">
                    {{ formatDiseaseName(selectedDisease2) }} Knowledge Graph (Max 50 Patients)
                  </div>
                  <div class="knowledge-graph">
                    <DiseasePatientNetwork
                      :diseases="[getDiseaseData(selectedDisease2)]"
                      :patients="getPatientsFromClustering(selectedDisease2)"
                      @node-click="handleNodeClick"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 修改图像比较部分，将图表移动到灰色框内 -->
            <div class="image-comparison-enhanced">
              <div class="raw-image-section">
                <div class="image-header">
                  Patient 1: {{ selectedPatient1 }} ({{
                    formatDiseaseName(selectedDisease1)
                  }})
                </div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient1]?.visits || "5"
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient1]?.hospitalized || "1"
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient1]?.age
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient1]?.gender
                      }}</span>
                    </div>
                  </div>

                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart
                        class="patient-trend-chart"
                        :option="patientTrend1Option"
                        autoresize
                      />
                    </div>
                    <!-- 移除指标图表 -->
                  </div>
                </div>
              </div>

              <div class="cam-image-section">
                <div class="image-header">
                  Patient 2: {{ selectedPatient2 }} ({{
                    formatDiseaseName(selectedDisease2)
                  }})
                </div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient2]?.visits || "3"
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient2]?.hospitalized || "0"
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient2]?.age
                      }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{
                        patientDetails[selectedPatient2]?.gender
                      }}</span>
                    </div>
                  </div>

                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart
                        class="patient-trend-chart"
                        :option="patientTrend2Option"
                        autoresize
                      />
                    </div>
                    <!-- 移除指标图表 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel -->
      <div class="right-panel">
        <div class="section-header">
          <span class="section-label">B</span>
          <h3>Comparison</h3>
        </div>
        <!-- 患者对比区，可选择不同患者进行指标、文本、图像对比 -->
        <div class="comparison-section">
          <div class="comparison-item full-height">
            <span class="item-number">1</span>
            <h4>Patient Analysis</h4>
            <!-- 患者选择区 -->
            <div class="patient-selector-section">
              <div class="patient-header">Select Patient</div>
              <div class="patient-selector">
                <select
                  class="patient-dropdown"
                  v-model="selectedPatient1"
                  @change="handlePatientSelection($event, 1)"
                >
                  <option value="">请选择患者</option>
                  <option
                    v-for="patientId in realPatientIds"
                    :key="patientId"
                    :value="patientId"
                  >
                    ID: {{ patientId }} {{ getPatientGraphLabel(patientId) }}
                  </option>
                </select>
              </div>
            </div>

            <!-- 热力图对比区 -->
            <div class="heatmap-comparison">
              <!-- 左侧：患者数据热力图 -->
              <div class="heatmap-section">
                <div class="indicator-visualization">
                  <v-chart
                    class="small-chart"
                    :option="heatmapOption1"
                    autoresize
                  />
                </div>
              </div>

              <!-- 右侧：正常值范围热力图 -->
              <div class="heatmap-section">
                <div class="indicator-visualization">
                  <v-chart
                    class="small-chart"
                    :option="normalRangeHeatmapOption"
                    autoresize
                  />
                </div>
              </div>
            </div>

            <!-- 患者病情智能概况 -->
            <div class="patient-overview-section" v-if="selectedPatient1">

              <!-- 病情概况总结区域 -->
              <div class="summary-section">
                <div class="section-title">
                  <span class="title-icon">📋</span>
                  <h4>病情概况总结</h4>
                </div>
                <div class="summary-content" :class="{'is-loading': summaryLoading}">
                  <div v-if="summaryLoading" class="loading-container">
                    <div class="loading-spinner">
                      <div class="spinner"></div>
                      <p class="loading-text">正在分析患者病情...</p>
                    </div>
                  </div>
                  <div v-else-if="patientSummary" class="summary-text">
                    {{ patientSummary }}
                  </div>
                  <div v-else class="no-summary">
                    暂无概况信息
                  </div>
                </div>
              </div>

              <!-- 病情关键词区域 -->
              <div class="keywords-section">
                <div class="section-title">
                  <span class="title-icon">🏷️</span>
                  <h4>病情关键词</h4>
                </div>
                <div class="keywords-chart-container" :class="{'is-loading': keywordsLoading}">
                  <div v-if="keywordsLoading" class="loading-container">
                    <div class="loading-spinner">
                      <div class="spinner"></div>
                      <p class="loading-text">正在提取关键词...</p>
                    </div>
                  </div>
                  <div
                    v-else
                    ref="keywordsChart"
                    class="keywords-chart"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- AI辅助诊断区域 -->
        <div class="section-header">
          <span class="section-label">D</span>
          <h3>AI辅助诊断</h3>
        </div>
        <div class="diagnostic-assistant-section">
          <div class="diagnosis-card">
            <div class="card-header-row">
              <h4>AI智能辅助诊断系统</h4>
            </div>
            <div class="card-content">
              <p class="diagnosis-description">
                使用人工智能分析患者临床信息，提供专业诊断建议和治疗方案
              </p>
              <button
                class="action-button analyze-button"
                @click="openAIDiagnosisModal"
              >
                <span class="button-icon">🔍</span> 开始AI诊断分析
              </button>
            </div>
          </div>
        </div>

        <!-- AI诊断弹窗 -->
        <div
          class="modal-overlay"
          v-if="showAIDiagnosisModal"
          @click.self="closeAIDiagnosisModal"
        >
          <div class="ai-diagnosis-modal">
            <div class="modal-header">
              <h3>AI辅助诊断分析</h3>
              <button class="close-button" @click="closeAIDiagnosisModal">
                &times;
              </button>
            </div>
            <div class="modal-body">
              <div v-if="diagnosisLoading" class="loading-container">
                <div class="spinner"></div>
                <p>正在进行AI诊断分析，请稍候...</p>
              </div>

              <div v-else-if="diagnosisError" class="error-container">
                <div class="error-icon">⚠️</div>
                <p>{{ diagnosisError }}</p>
                <button
                  class="action-button retry-button"
                  @click="startAIDiagnosis"
                >
                  <span class="button-icon">🔄</span> 重试
                </button>
              </div>

              <div v-else-if="diagnosisResult" class="diagnosis-result">
                <div class="patient-info">
                  <h4>患者基本信息</h4>
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">ID:</span>
                      <span class="info-value">{{
                        diagnosisResult.patient_id
                      }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">姓名:</span>
                      <span class="info-value">{{
                        diagnosisResult.patient_name
                      }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">性别:</span>
                      <span class="info-value">{{
                        diagnosisResult.patient_gender
                      }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">年龄:</span>
                      <span class="info-value">{{
                        diagnosisResult.patient_age
                      }}</span>
                    </div>
                    <div class="info-item full-width">
                      <span class="info-label">主诊断:</span>
                      <span class="info-value diagnosis-name">{{
                        diagnosisResult.main_diagnosis
                      }}</span>
                    </div>
                  </div>
                </div>

                <div class="diagnosis-tabs">
                  <button
                    class="tab-btn"
                    data-tab="analysis"
                    :class="{ active: activeTab === 'analysis' }"
                    @click="switchTab('analysis')"
                  >
                    诊断分析
                  </button>
                  <button
                    class="tab-btn"
                    data-tab="result"
                    :class="{ active: activeTab === 'result' }"
                    @click="switchTab('result')"
                  >
                    诊断结果
                  </button>
                  <button
                    class="tab-btn"
                    data-tab="recommendation"
                    :class="{ active: activeTab === 'recommendation' }"
                    @click="switchTab('recommendation')"
                  >
                    就诊建议
                  </button>
                  <button
                    class="tab-btn"
                    data-tab="chat"
                    :class="{ active: activeTab === 'chat' }"
                    @click="switchTab('chat')"
                  >
                    医疗咨询
                  </button>
                </div>

                <div class="tab-content">
                  <div
                    v-show="activeTab === 'analysis'"
                    class="analysis-content"
                  >
                    <div class="content-header">
                      <div class="header-icon">🔍</div>
                      <h4>诊断分析</h4>
                    </div>
                    <div class="diagnostic-content-panel analysis-panel">
                      <div class="content-wrapper">
                        <div
                          class="formatted-content"
                          v-html="
                            formatText(diagnosisResult.diagnosis_analysis)
                          "
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div v-show="activeTab === 'result'" class="result-content">
                    <div class="content-header">
                      <div class="header-icon">📋</div>
                      <h4>诊断结果</h4>
                    </div>
                    <div class="diagnostic-content-panel result-panel">
                      <div class="content-wrapper">
                        <div
                          class="formatted-content"
                          v-html="formatText(diagnosisResult.diagnosis_result)"
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div
                    v-show="activeTab === 'recommendation'"
                    class="recommendation-content"
                  >
                    <div class="content-header">
                      <div class="header-icon">💡</div>
                      <h4>就诊建议</h4>
                    </div>
                    <div class="diagnostic-content-panel recommendation-panel">
                      <div class="content-wrapper">
                        <div
                          class="formatted-content"
                          v-html="
                            formatText(diagnosisResult.visit_recommendation)
                          "
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div v-show="activeTab === 'chat'" class="chat-content">
                    <h4>医疗咨询</h4>
                    <p class="chat-intro">
                      您可以向AI医疗助手询问关于患者诊断和疾病的问题
                    </p>
                    <div class="chat-messages" ref="chatMessages">
                      <template v-for="(message, index) in chatMessages" :key="index">
                        <!-- AI消息 - 左侧显示 -->
                        <div v-if="message.type === 'ai'" class="ai-message-container">
                          <div class="ai-message">
                            <div class="ai-avatar">
                              <span class="avatar-icon">🤖</span>
                            </div>
                            <div class="ai-content">
                              <div class="ai-text" v-html="formatText(message.text)"></div>
                              <div class="ai-time">{{ message.time }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- 用户消息 - 右侧显示 -->
                        <div v-else class="user-message-container">
                          <div class="user-message">
                            <div class="user-content">
                              <div class="user-text" v-html="formatText(message.text)"></div>
                              <div class="user-time">{{ message.time }}</div>
                            </div>
                            <div class="user-avatar">
                              <span class="avatar-icon">👨‍⚕️</span>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>

                    <div class="chat-input-container">
                      <input
                        type="text"
                        class="chat-input"
                        v-model="chatMessage"
                        @keyup.enter="sendChatMessage"
                        placeholder="输入您的问题..."
                        :disabled="chatLoading"
                      />
                      <button
                        class="chat-send-button"
                        @click="sendChatMessage"
                        :disabled="chatLoading"
                      >
                        <span v-if="!chatLoading">发送</span>
                        <div v-else class="button-spinner"></div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-result">
                <p>请选择患者并开始AI诊断分析</p>
              </div>
            </div>
            <div class="modal-footer">
              <button
                class="action-button secondary-button"
                @click="closeAIDiagnosisModal"
              >
                关闭
              </button>
              <button
                class="action-button primary-button"
                @click="startAIDiagnosis"
                :disabled="!selectedPatient1"
              >
                <span class="button-icon">🔍</span>
                {{ diagnosisResult ? "重新分析" : "开始分析" }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
} from "echarts/components";
import {
  LineChart,
  BarChart,
  HeatmapChart,
  RadarChart,
  PieChart,
} from "echarts/charts";
import VChart from "vue-echarts";
import PatientClustering from "./PatientClustering.vue";
import DiseasePatientNetwork from "./DiseasePatientNetwork.vue";
import patientDataProcessor from "../utils/patientDataProcessor";
import * as echarts from "echarts";
import MedicationRadarChart from './MedicationRadarChart.vue';

// 注册echarts组件
use([
  CanvasRenderer,
  GridComponent,
  LegendComponent,
  TooltipComponent,
  LineChart,
  BarChart,
  HeatmapChart,
  RadarChart,
  PieChart,
]);

// Add data type definitions
interface DiseaseData {
  id: string;
  name: string;
  relatedSymptoms: string[];
  patientCount: number;
}

interface PatientDetail {
  disease: string;
  age: number;
  gender: string;
  visits: number;
  hospitalized: number;
  indicators: Record<string, any>;
  textData: string;
  imagePath: string;
}

interface PatientData {
  id: string;
  disease: string;
  details: PatientDetail;
}

interface NetworkData {
  nodes: any[];
  links: any[];
}

interface Interaction {
  diseaseId: string;
  patientId: string;
  strength: number;
}

interface ComparisonTab {
  title: string;
  key: string;
}

interface QuarterlyData {
  Q1: DiseaseData;
  Q2: DiseaseData;
  Q3: DiseaseData;
  Q4: DiseaseData;
}

interface MonthlyData {
  Jan: DiseaseData;
  Feb: DiseaseData;
  Mar: DiseaseData;
  Apr: DiseaseData;
  May: DiseaseData;
  Jun: DiseaseData;
  Jul: DiseaseData;
  Aug: DiseaseData;
  Sep: DiseaseData;
  Oct: DiseaseData;
  Nov: DiseaseData;
  Dec: DiseaseData;
}

export default {
  name: "DiagnosisAssistant",
  components: {
    VChart,
    PatientClustering,
    DiseasePatientNetwork,
    MedicationRadarChart,
  },
  watch: {
    correlationMode(newVal) {
      if (newVal === 'apriori') {
        this.fetchAprioriCorrelationData();
      }
    },

    // 监听屏幕切换，确保图表正确重绘
    currentScreen(newScreen, oldScreen) {
      console.log(`Screen changed from ${oldScreen} to ${newScreen}`);
      this.$nextTick(() => {
        setTimeout(() => {
          this.resizeAllCharts();
        }, 300);
      });
    },
  },
  data() {
    return {
      selectedCases: {
        normal: 0,
        herniated: 0,
        bulging: 0,
      },
      currentCase: {
        id: "",
        label: "",
        description: "",
      },
      comparisonData: [
        {
          cardId: "",
          label: "",
          notes: "",
        },
        {
          cardId: "",
          label: "",
          notes: "",
        },
      ],
      // 添加当前屏幕状态
      currentScreen: "first",
      // 疾病关联分析模式（standard/apriori）
      correlationMode: "apriori",
      // Apriori关联图表选项
      aprioriCorrelationOption: null,
      // AI对话相关状态
      showAIDialog: false,
      showAIChat: false,
      activeChatTab: "chat",
      // 聊天相关数据
      isAiTyping: false,
      userMessage: "",
      chatMessages: [
        {
          type: "ai",
          text: "Hello! I can help analyze patient data and answer medical questions. How can I assist you today?",
          time: new Date().toLocaleTimeString(),
        },
      ],

      // 添加AI诊断相关状态
      showAIDiagnosisModal: false,
      diagnosisLoading: false,
      diagnosisError: null as string | null,
      diagnosisResult: null as any,
      activeTab: "analysis",
      chatMessage: "",
      chatLoading: false,
      realPatientIds: [] as string[],
      // 患者数据相关
      patientComparisonData: {
        patient1: [],
        patient2: [],
      },
      patientDetails: {} as Record<string, any>,

      // 患者病情智能概况相关
      patientSummary: "" as string,
      patientKeywords: [] as any[],
      summaryLoading: false,
      keywordsLoading: false,

      // 添加聚类选项，修改默认值为 'none'
      clusteringBy: "none" as "none" | "age" | "disease" | "visits" | "other", // 默认为不聚类
      // 聚类数据
      visualizationData: [], // 保存聚类可视化数据
      patientGraphMapping: {} as Record<string, string>, // 记录患者所属的图谱
      // 散点图配置
      scatterOption: {
        animation: true,
        animationDuration: 1500,
        animationEasing: "cubicOut",
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "value",
          show: false,
        },
        grid: {
          left: "2%",
          right: "2%",
          top: "2%",
          bottom: "2%",
          containLabel: false,
        },
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            return `<div style="padding: 8px 12px; min-width: 180px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">ID: ${
                params.dataIndex
              }</div>
              <div style="margin-bottom: 3px;">Type: ${
                params.seriesIndex === 0
                  ? "Patient"
                  : params.seriesIndex === 1
                  ? "Group B"
                  : "Group C"
              }</div>
              <div>Value: (${params.data[0].toFixed(
                2
              )}, ${params.data[1].toFixed(2)})</div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText:
            "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100; min-width: 180px;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        series: [
          {
            type: "scatter",
            symbolSize: function () {
              return Math.random() * 6 + 6; // 6-12 的随机大小
            },
            itemStyle: {
              opacity: 0.85,
              shadowBlur: 8,
              shadowColor: "rgba(33, 150, 243, 0.4)",
              color: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0,
                    color: "#2196f3", // 浅蓝色
                  },
                  {
                    offset: 1,
                    color: "#1565c0", // 深蓝色
                  },
                ],
              },
            },
            emphasis: {
              scale: true,
              itemStyle: {
                shadowBlur: 15,
                borderColor: "#fff",
                borderWidth: 2,
                opacity: 1,
              },
            },
            data: this.generateRandomScatterData(400, null), // 增加点的数量
            z: 10,
          },
          // 添加连接线效果
          {
            type: "graph",
            layout: "none",
            coordinateSystem: "cartesian2d",
            symbolSize: [10, 20], // 添加不同大小的中心点
            symbol: "circle",
            itemStyle: {
              color: function (params: any) {
                // 为中心点设置颜色
                if (params.dataIndex < 6) {
                  // 前6个点是中心点
                  const colors = [
                    "#2196f3",
                    "#f44336",
                    "#4caf50",
                    "#ff9800",
                    "#9c27b0",
                    "#607d8b",
                  ];
                  return colors[params.dataIndex % 6];
                }
                return "#ddd";
              },
              opacity: 0.8,
              borderWidth: 2,
              borderColor: "#fff",
            },
            lineStyle: {
              width: 1,
              color: "#aaa",
              opacity: 0.6,
              curveness: 0,
            },
            emphasis: {
              lineStyle: {
                width: 2,
                color: "#555",
              },
            },
            data: this.generateClusterCenters(),
            edges: this.generateClusterEdges(400),
            z: 1,
          },
        ],
      },
      // 柱状图配置
      barOption: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: ["A", "B", "C", "D", "E", "F", "G"],
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "Value",
            type: "bar",
            barWidth: "60%",
            data: [10, 52, 200, 334, 390, 330, 220],
          },
        ],
      },
      // 折线图配置
      lineOption: {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: "line",
          },
        ],
      },
      // 小型柱状图配置1
      smallBarOption: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30,
        },
        xAxis: {
          type: "category",
          data: ["A", "B", "C", "D", "E"],
          axisLabel: {
            fontSize: 10,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            fontSize: 10,
          },
        },
        series: [
          {
            type: "bar",
            data: [12, 24, 36, 48, 60],
            itemStyle: {
              color: "#2196f3",
            },
          },
        ],
      },
      // 小型柱状图配置2
      smallBarOption2: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30,
        },
        xAxis: {
          type: "category",
          data: ["A", "B", "C", "D", "E"],
          axisLabel: {
            fontSize: 10,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            fontSize: 10,
          },
        },
        series: [
          {
            type: "bar",
            data: [60, 48, 36, 24, 12],
            itemStyle: {
              color: "#4caf50",
            },
          },
        ],
      },
      // 饼图配置1
      pieOption1: {
        tooltip: {
          trigger: "item",
        },
        legend: {
          bottom: "0%",
          left: "center",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10,
          },
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 1048, name: "Normal" },
              { value: 735, name: "Bulging" },
              { value: 580, name: "Herniated" },
            ],
          },
        ],
      },
      // 饼图配置2
      pieOption2: {
        tooltip: {
          trigger: "item",
        },
        legend: {
          bottom: "0%",
          left: "center",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10,
          },
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 500, name: "Normal" },
              { value: 900, name: "Bulging" },
              { value: 600, name: "Herniated" },
            ],
          },
        ],
      },
      // 修改：Raw图像下方的图表，移除背景相关设置
      rawImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["1", "2", "3", "4", "5", "6", "7", "8"],
          axisLabel: {
            fontSize: 8,
            color: "#666",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            fontSize: 8,
            color: "#666",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            data: [28, 32, 36, 34, 45, 33, 28, 24],
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 5,
            itemStyle: {
              color: "#2196f3",
            },
            lineStyle: {
              width: 2,
              color: "#2196f3",
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(33, 150, 243, 0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(33, 150, 243, 0.05)",
                  },
                ],
              },
            },
          },
        ],
      },
      // 修改：CAM图像下方的图表，移除背景相关设置
      camImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["A", "B", "C", "D", "E"],
          axisLabel: {
            fontSize: 8,
            color: "#666",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            fontSize: 8,
            color: "#666",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            type: "bar",
            barWidth: "50%",
            data: [
              {
                value: 60,
                itemStyle: {
                  color: "#f44336",
                },
              },
              {
                value: 45,
                itemStyle: {
                  color: "#f44336",
                },
              },
              {
                value: 30,
                itemStyle: {
                  color: "#2196f3",
                },
              },
              {
                value: 20,
                itemStyle: {
                  color: "#2196f3",
                },
              },
              {
                value: 15,
                itemStyle: {
                  color: "#4caf50",
                },
              },
            ],
          },
        ],
      },
      // 添加新的状态变量
      isComparisonCollapsed: false,
      // 眼科疾病数据
      ophthalmicDiseases: [
        {
          id: "macular_edema",
          name: "Macular Edema",
          patients: [
            "80145987",
            "80156560",
            "80136152",
            "80145988",
            "80156561",
            "80136153",
            "80145989",
            "80156562",
            "80136154",
            "80145990",
            "80156563",
            "80136155",
            "80145991",
            "80156564",
            "80136156",
            "80156565",
            "80136157",
            "80145992",
            "80156566",
            "80136158",
            "80145993",
            "80156567",
            "80136159",
            "80145994",
            "80156568",
            "80136160",
            "80145995",
            "80156569",
            "80136161",
            "80145996",
            "80156570",
            "80136162",
            "80145997",
            "80156571",
            "80136163",
            "80145998",
            "80156572",
            "80136164",
            "80145999",
            "80156573",
          ],
          incidence: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
          correlations: {
            open_angle_glaucoma: 0.3,
            closed_angle_glaucoma: 0.2,
            diabetic_maculopathy: 0.8,
            retinitis_pigmentosa: 0.4,
            branch_retinal_vein_occlusion: 0.6,
          },
        },
        {
          id: "open_angle_glaucoma",
          name: "Open-Angle Glaucoma",
          patients: [
            "80145123",
            "80156789",
            "80136234",
            "80145124",
            "80156790",
            "80136235",
            "80145125",
            "80156791",
            "80136236",
            "80145126",
            "80156792",
            "80136237",
            "80145127",
            "80156793",
            "80136238",
            "80145128",
            "80156794",
            "80136239",
            "80145129",
            "80156795",
            "80136240",
            "80145130",
            "80156796",
            "80136241",
            "80145131",
            "80156797",
            "80136242",
            "80145132",
            "80156798",
            "80136243",
            "80145133",
            "80156799",
            "80136244",
            "80145134",
            "80156800",
            "80136245",
            "80145135",
            "80156801",
            "80136246",
            "80145136",
          ],
          incidence: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
          correlations: {
            macular_edema: 0.3,
            closed_angle_glaucoma: 0.7,
            diabetic_maculopathy: 0.4,
            retinitis_pigmentosa: 0.3,
            branch_retinal_vein_occlusion: 0.2,
          },
        },
        {
          id: "closed_angle_glaucoma",
          name: "Closed-Angle Glaucoma",
          patients: [
            "80145111",
            "80156222",
            "80136333",
            "80145112",
            "80156223",
            "80136334",
            "80145113",
            "80156224",
            "80136335",
            "80145114",
            "80156225",
            "80136336",
            "80145115",
            "80156226",
            "80136337",
            "80145116",
            "80156227",
            "80136338",
            "80145117",
            "80156228",
            "80136339",
            "80145118",
            "80156229",
            "80136340",
            "80145119",
            "80156230",
            "80136341",
            "80145120",
            "80156231",
            "80136342",
            "80145121",
            "80156232",
            "80136343",
            "80145122",
            "80156233",
          ],
          incidence: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
          correlations: {
            macular_edema: 0.2,
            open_angle_glaucoma: 0.7,
            diabetic_maculopathy: 0.3,
            retinitis_pigmentosa: 0.2,
            branch_retinal_vein_occlusion: 0.1,
          },
        },
        {
          id: "diabetic_maculopathy",
          name: "Diabetic Maculopathy",
          patients: [
            "80145444",
            "80156555",
            "80136666",
            "80145445",
            "80156556",
            "80136667",
            "80145446",
            "80156557",
            "80136668",
            "80145447",
            "80156558",
            "80136669",
            "80145448",
            "80156559",
            "80136670",
            "80145449",
            "80156550",
            "80136671",
            "80145450",
            "80156551",
            "80145451",
            "80156552",
            "80136672",
            "80145452",
            "80156553",
            "80136673",
            "80145453",
            "80156554",
            "80136674",
            "80145454",
            "80156560",
            "80136675",
            "80145455",
            "80156561",
            "80136676",
            "80145456",
            "80156562",
            "80136677",
            "80145457",
            "80156563",
          ],
          incidence: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
          correlations: {
            macular_edema: 0.8,
            open_angle_glaucoma: 0.4,
            closed_angle_glaucoma: 0.3,
            retinitis_pigmentosa: 0.2,
            branch_retinal_vein_occlusion: 0.5,
          },
        },
        {
          id: "retinitis_pigmentosa",
          name: "Retinitis Pigmentosa",
          patients: [
            "80145777",
            "80156888",
            "80136999",
            "80145778",
            "80156889",
            "80137000",
            "80145779",
            "80156890",
            "80137001",
            "80145780",
            "80156891",
            "80137002",
            "80145781",
            "80156892",
            "80137003",
            "80145782",
            "80156893",
            "80137004",
            "80145783",
            "80156894",
            "80137005",
            "80145784",
            "80156895",
            "80137006",
            "80145785",
            "80156896",
            "80137007",
            "80145786",
            "80156897",
            "80137008",
          ],
          incidence: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
          correlations: {
            macular_edema: 0.4,
            open_angle_glaucoma: 0.3,
            closed_angle_glaucoma: 0.2,
            diabetic_maculopathy: 0.2,
            branch_retinal_vein_occlusion: 0.3,
          },
        },
        {
          id: "branch_retinal_vein_occlusion",
          name: "Branch Retinal Vein Occlusion",
          patients: [
            "80146001",
            "80157001",
            "80137101",
            "80146002",
            "80157002",
            "80137102",
            "80146003",
            "80157003",
            "80137103",
            "80146004",
            "80157004",
            "80137104",
            "80146005",
            "80157005",
            "80137105",
            "80146006",
            "80157006",
            "80137106",
            "80146007",
            "80157007",
            "80137107",
            "80146008",
            "80157008",
            "80137108",
            "80146009",
            "80157009",
            "80137109",
            "80146010",
            "80157010",
            "80137110",
            "80146011",
            "80157011",
            "80137111",
            "80146012",
            "80157012",
          ],
          incidence: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
          correlations: {
            macular_edema: 0.6,
            open_angle_glaucoma: 0.2,
            closed_angle_glaucoma: 0.1,
            diabetic_maculopathy: 0.5,
            retinitis_pigmentosa: 0.3,
          },
        },
      ],

      // 选中的疾病用于比较
      selectedLeftDisease: "macular_edema",
      selectedRightDisease: "open_angle_glaucoma",

      // 眼科疾病图像数据
      diseaseImages: {
        macular_edema: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg",
        },
        open_angle_glaucoma: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg",
        },
        closed_angle_glaucoma: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-oct.jpg",
        },
        diabetic_maculopathy: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-oct.jpg",
        },
        retinitis_pigmentosa: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-oct.jpg",
        },
        branch_retinal_vein_occlusion: {
          fundus:
            "https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg",
          oct: "https://www.aao.org/images/k-assets/eye-health/diseases/brvo-oct.jpg",
        },
      },

      // 当前选中的疾病图像
      currentDiseaseImages: {
        fundus:
          "https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-fundus.jpg",
        oct: "https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-oct.jpg",
      },

      // 选中的疾病
      selectedDisease1: "macular_edema",
      selectedDisease2: "open_angle_glaucoma",

      // 柱状图：疾病数量
      diseaseCaseCountOption: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [
              "Macular\nEdema",
              "Open-Angle\nGlaucoma",
              "Closed-Angle\nGlaucoma",
              "Diabetic\nMaculopathy",
              "Retinitis\nPigmentosa",
              "Branch Retinal\nVein Occlusion",
            ],
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              fontSize: 10,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "Number of Cases",
            nameLocation: "middle",
            nameGap: 40,
          },
        ],
        series: [
          {
            name: "Cases",
            type: "bar",
            barWidth: "60%",
            data: [
              { value: 20, itemStyle: { color: "#1976d2" } },
              { value: 24, itemStyle: { color: "#2196f3" } },
              { value: 17, itemStyle: { color: "#42a5f5" } },
              { value: 20, itemStyle: { color: "#64b5f6" } },
              { value: 14, itemStyle: { color: "#90caf9" } },
              { value: 17, itemStyle: { color: "#bbdefb" } },
            ],
            label: {
              show: true,
              position: "top",
              color: "#333",
            },
          },
        ],
      },

      // 折线图：疾病发病趋势
      diseaseIncidenceTrendOption: {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [
            "Macular Edema",
            "Open-Angle Glaucoma",
            "Closed-Angle Glaucoma",
            "Diabetic Maculopathy",
            "Retinitis Pigmentosa",
            "Branch Retinal Vein Occlusion",
          ],
          type: "scroll",
          orient: "horizontal",
          bottom: 0,
          textStyle: {
            fontSize: 10,
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ],
        },
        yAxis: {
          type: "value",
          name: "Incidence per 100,000",
          nameLocation: "middle",
          nameGap: 40,
        },
        series: [
          {
            name: "Macular Edema",
            type: "line",
            data: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
            itemStyle: { color: "#0d47a1" },
            lineStyle: { width: 2, color: "#0d47a1" },
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: "Open-Angle Glaucoma",
            type: "line",
            data: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
            itemStyle: { color: "#1565c0" },
            lineStyle: { width: 2, color: "#1565c0" },
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: "Closed-Angle Glaucoma",
            type: "line",
            data: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
            itemStyle: { color: "#1976d2" },
            lineStyle: { width: 2, color: "#1976d2" },
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: "Diabetic Maculopathy",
            type: "line",
            data: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
            itemStyle: { color: "#2196f3" },
            lineStyle: { width: 2, color: "#2196f3" },
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: "Retinitis Pigmentosa",
            type: "line",
            data: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
            itemStyle: { color: "#64b5f6" },
            lineStyle: { width: 2, color: "#64b5f6" },
            symbol: "circle",
            symbolSize: 8,
          },
          {
            name: "Branch Retinal Vein Occlusion",
            type: "line",
            data: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
            itemStyle: { color: "#90caf9" },
            lineStyle: { width: 2, color: "#90caf9" },
            symbol: "circle",
            symbolSize: 8,
          },
        ],
      },



      // 添加患者详细数据


      // 当前选择的患者用于对比
      selectedPatient1: "80145987", // 默认为第一个黄斑水肿患者
      selectedPatient2: "80145123", // 默认为第一个开角型青光眼患者

      // SHAP图配置
      shapChartOption1: null as any,
      shapChartOption2: null as any,

      // 添加可视化模式切换状态
      activeVisualizationMode: "count",

      // 季节视图模式

      seasonalViewMode: "quarter",

      // 患者趋势图配置
      patientTrend1Option: {},
      patientTrend2Option: {},
      diseases: [], // 所有可用的疾病列表

      // 后端连接状态
      backendConnected: false,
      backendConnectionError: null,
      clusteringDataLoaded: false,
      isLoadingClustering: false,

      // 热力图配置
      normalRangeHeatmapOption: {},
      heatmapOption1: {},

      // 选中患者和季节分布数据
      selectedPatients: [], // 从聚类图中选中的患者
      realSeasonalData: null, // 真实的季节分布数据
      filteredSeasonalData: null, // 过滤后的季节分布数据

      // 疾病关联分析数据
      diseaseCorrelationData: null, // Apriori分析结果
      associationRules: [], // 关联规则
      correlationMatrix: null, // 关联矩阵

      // 窗口resize处理函数
      handleResize: null as any,
    };
  },
  computed: {
    // 处理用于知识图谱的疾病数据
    selectedDiseases() {
      return [
        {
          id: this.selectedDisease1,
          name: this.formatDiseaseName(this.selectedDisease1),
          color: this.getDiseaseColor(this.selectedDisease1),
        },
        {
          id: this.selectedDisease2,
          name: this.formatDiseaseName(this.selectedDisease2),
          color: this.getDiseaseColor(this.selectedDisease2),
        },
      ];
    },
    // 处理用于知识图谱的患者数据
    selectedPatients() {
      const patients: Array<{ id: string; diseaseId: string }> = [];

      // 添加疾病1的患者
      const disease1Patients = this.getPatientsForDisease(
        this.selectedDisease1
      );
      disease1Patients.forEach((patientId) => {
        patients.push({
          id: patientId,
          diseaseId: this.selectedDisease1,
        });
      });

      // 添加疾病2的患者
      const disease2Patients = this.getPatientsForDisease(
        this.selectedDisease2
      );
      disease2Patients.forEach((patientId) => {
        patients.push({
          id: patientId,
          diseaseId: this.selectedDisease2,
        });
      });

      return patients;
    },
    // 添加组合图表选项
    activeCombinedChartOption(): any {
      // 使用药品雷达图替换 Case Count 图
      if (this.activeVisualizationMode === "count") {
        return null; // 返回 null，因为我们将使用独立组件而不是 echarts 选项
      } else {
        return this.diseaseIncidenceTrendOption;
      }
    },
    // 季节分布图表选项 - 只使用真实聚类数据
    seasonalDistributionOption(): any {
      // 使用真实数据（选中患者的过滤数据或全部数据）
      const useRealData = this.filteredSeasonalData || this.realSeasonalData;

      console.log('seasonalDistributionOption - useRealData:', useRealData);
      console.log('filteredSeasonalData:', this.filteredSeasonalData);
      console.log('realSeasonalData:', this.realSeasonalData);

      if (useRealData) {
        return this.generateRealSeasonalOption(useRealData);
      }

      // 如果没有真实数据，返回空的图表配置
      return {
        title: {
          text: 'Seasonal Distribution (Loading...)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          top: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.seasonalViewMode === 'quarter' ? ["Q1", "Q2", "Q3", "Q4"] : ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            axisLabel: {
              interval: 0,
              color: '#666'
            }
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "Cases",
            nameLocation: "middle",
            nameGap: 30,
            axisLabel: {
              color: '#666'
            }
          },
        ],
        series: []
      };
    },

    // 疾病关联热力图选项（使用Apriori算法分析结果）
    diseaseCorrelationOption() {
      console.log('🔍 检查疾病关联数据状态:');
      console.log('  correlationMatrix:', this.correlationMatrix);
      console.log('  diseaseCorrelationData:', this.diseaseCorrelationData);
      console.log('  backendConnected:', this.backendConnected);

      // 如果有真实的关联数据，使用真实数据
      if (this.correlationMatrix && (this.correlationMatrix as any).diseases && (this.correlationMatrix as any).matrix) {
        console.log('✅ 使用真实的疾病关联数据');
        return this.generateRealCorrelationOption(this.correlationMatrix);
      }

      console.log('⚠️ 使用模拟数据，原因: correlationMatrix为空或无效');
      // 回退到模拟数据
      return this.generateMockCorrelationOption();
    },
  },
  methods: {
    // 防抖函数
    debounce(func: Function, wait: number) {
      let timeout: any;
      return function executedFunction(...args: any[]) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // 重新调整所有图表大小
    resizeAllCharts() {
      console.log('Resizing all charts...');
      this.$nextTick(() => {
        // 获取所有ECharts实例并调用resize
        const chartElements = document.querySelectorAll('.chart, .small-chart, .patient-trend-chart');
        chartElements.forEach((element) => {
          const chartInstance = echarts.getInstanceByDom(element as HTMLElement);
          if (chartInstance) {
            try {
              chartInstance.resize();
              console.log('Resized chart:', element.className);
            } catch (error) {
              console.warn('Failed to resize chart:', error);
            }
          }
        });

        // 特别处理v-chart组件
        const vChartElements = document.querySelectorAll('[data-v-chart]');
        vChartElements.forEach((element) => {
          const chartInstance = echarts.getInstanceByDom(element as HTMLElement);
          if (chartInstance) {
            try {
              chartInstance.resize();
              console.log('Resized v-chart:', element.className);
            } catch (error) {
              console.warn('Failed to resize v-chart:', error);
            }
          }
        });

        // 触发窗口resize事件，确保所有图表都能响应
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
        }, 100);
      });
    },

    // 添加切换屏幕方法
    toggleScreen() {
      this.currentScreen = this.currentScreen === "first" ? "detail" : "first";
      console.log(`Switching to screen: ${this.currentScreen}`);

      // 滚动到顶部
      this.$nextTick(() => {
        window.scrollTo(0, 0);

        // 延迟调用图表重绘，确保DOM已经更新
        setTimeout(() => {
          this.resizeAllCharts();
        }, 200);

        // 如果切换到overview视图，额外确保聚类图表正确渲染
        if (this.currentScreen === "first") {
          setTimeout(() => {
            this.resizeAllCharts();
            // 更新散点图数据
            this.updateScatterChart();
          }, 500);
        }
      });
    },
    // 生成随机散点数据
    generateRandomScatterData(count: number) {
      const data = [];

      // 检查聚类方式，根据不同的聚类方式生成不同的数据
      switch (this.clusteringBy) {
        case "none":
          // 默认不聚类，所有点都是蓝色并分布在整个图表上
          for (let i = 0; i < count; i++) {
            // 为了创建更均匀的分布，使用随机角度和距离从中心点向外分散
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 4; // 0到4的距离
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;

            data.push({
              value: [x, y],
              itemStyle: {
                color: "#2196f3", // 所有点都是蓝色
              },
            });
          }
          break;

        case "age":
          // 按年龄段聚类: 青年(18-40)，中年(41-60)，老年(61+)
          const ageGroups = [
            { name: "18-40", x: -3, y: -3, radius: 2, color: "#2196f3" },
            { name: "41-60", x: 0, y: 0, radius: 2.5, color: "#f44336" },
            { name: "61+", x: 3, y: 3, radius: 2, color: "#4caf50" },
          ];

          for (let i = 0; i < count; i++) {
            const group =
              ageGroups[Math.floor(Math.random() * ageGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y], // 保存簇中心点坐标
            });
          }
          break;

        case "disease":
          // 按疾病类型聚类
          const diseaseGroups = [
            {
              name: "Macular Edema",
              x: -4,
              y: 0,
              radius: 1.5,
              color: "#2196f3",
            },
            {
              name: "Open-Angle Glaucoma",
              x: -2,
              y: 2,
              radius: 1.5,
              color: "#f44336",
            },
            {
              name: "Closed-Angle Glaucoma",
              x: 0,
              y: -2,
              radius: 1.5,
              color: "#4caf50",
            },
            {
              name: "Diabetic Maculopathy",
              x: 2,
              y: 2,
              radius: 1.5,
              color: "#ff9800",
            },
            {
              name: "Retinitis Pigmentosa",
              x: 4,
              y: 0,
              radius: 1.5,
              color: "#9c27b0",
            },
            {
              name: "Branch Retinal Vein Occlusion",
              x: 0,
              y: 3,
              radius: 1.5,
              color: "#607d8b",
            },
          ];

          for (let i = 0; i < count; i++) {
            const group =
              diseaseGroups[Math.floor(Math.random() * diseaseGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y], // 保存簇中心点坐标
            });
          }
          break;

        case "visits":
          // 按就诊次数聚类: 首诊, 随访1-3次, 随访4次以上
          const visitGroups = [
            { name: "First Visit", x: -3, y: 0, radius: 2, color: "#2196f3" },
            { name: "1-3 Visits", x: 0, y: 0, radius: 2, color: "#f44336" },
            { name: "4+ Visits", x: 3, y: 0, radius: 2, color: "#4caf50" },
          ];

          for (let i = 0; i < count; i++) {
            const group =
              visitGroups[Math.floor(Math.random() * visitGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y], // 保存簇中心点坐标
            });
          }
          break;

        case "other":
          // 按其他属性聚类：性别、地理区域
          const otherGroups = [
            { name: "Male", x: -2, y: -2, radius: 1.5, color: "#2196f3" },
            { name: "Female", x: 2, y: -2, radius: 1.5, color: "#e91e63" },
            { name: "Urban", x: 0, y: 2, radius: 1.5, color: "#ff9800" },
            { name: "Rural", x: 0, y: -2, radius: 1.5, color: "#4caf50" },
          ];

          for (let i = 0; i < count; i++) {
            const group =
              otherGroups[Math.floor(Math.random() * otherGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y], // 保存簇中心点坐标
            });
          }
          break;

        default:
          // 创建默认簇
          const clusters = [
            { x: -3, y: -3, radius: 2 },
            { x: 0, y: 0, radius: 3 },
            { x: 3, y: 3, radius: 2.5 },
          ];

          for (let i = 0; i < count; i++) {
            // 随机选择一个簇
            const cluster =
              clusters[Math.floor(Math.random() * clusters.length)];
            // 在簇周围生成点
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * cluster.radius;
            const x = cluster.x + Math.cos(angle) * radius;
            const y = cluster.y + Math.sin(angle) * radius;

            data.push([x, y]);
          }
          break;
      }

      return data;
    },

    // 生成从聚类中心到各点的连接线
    generateClusterLinks(scatterData: any[]): any[] {
      const links: any[] = [];

      // 如果是'none'聚类方式，则不生成连接线
      if (this.clusteringBy === "none") {
        return links;
      }

      // 为每个数据点创建一个指向其聚类中心的连接线
      scatterData.forEach((point: any, index: number) => {
        // 检查点是否有聚类中心信息
        if (point.clusterCenter) {
          // 创建一个虚拟的中心点（用于连线，不实际显示）
          const centerPointIndex = -10000 - index; // 使用负值确保不与实际点冲突

          // 添加中心点
          links.push({
            source: centerPointIndex,
            target: index,
            lineStyle: {
              color: point.itemStyle.color, // 使用与点相同的颜色
              width: 0.8,
              opacity: 0.5,
              curveness: 0, // 直线连接
            },
          });
        }
      });

      return links;
    },

    // 添加设置聚类方式的方法
    async setClusteringBy(
      clusterType: "none" | "age" | "disease" | "visits" | "other"
    ) {
      this.clusteringBy = clusterType;

      // 更新散点图和连接线
      this.updateScatterChart();

      // 重新生成主散点图数据
      this.scatterOption.series[0].data = this.generateRandomScatterData(400);

      // 更新连接线
      if (
        this.scatterOption &&
        this.scatterOption.series &&
        this.scatterOption.series.length >= 2
      ) {
        this.scatterOption.series[1].data = this.generateClusterCenters();
        this.scatterOption.series[1].edges = this.generateClusterEdges(400);

        // 根据是否聚类显示或隐藏中心点和连接线
        if (clusterType === "none") {
          // 不聚类时隐藏中心点和连接线
          this.scatterOption.series[1].symbolSize = [0, 0];
          if (this.scatterOption.series[1].lineStyle) {
            this.scatterOption.series[1].lineStyle.opacity = 0;
          }
        } else {
          // 聚类时显示中心点和连接线
          this.scatterOption.series[1].symbolSize = [10, 20];
          if (this.scatterOption.series[1].lineStyle) {
            this.scatterOption.series[1].lineStyle.opacity = 0.6;
          }
        }
      }

      // 如果聚类方法改变且后端已连接，同步疾病列表
      if (this.backendConnected && clusterType === "disease") {
        await this.syncDiseasesWithClusteringData();
      }
    },

    // 更新散点图
    updateScatterChart() {
      // 根据不同的聚类方式更新图表的数据和图例
      const newData: any[] = [];
      const legendData: string[] = [];

      // 生成实际数据点
      const scatterData = this.generateScatterData();

      // 生成连接线
      const clusterLinks = this.generateClusterLinks(scatterData);

      // 将中心点添加到数据中（用于连接线，但不显示）
      const centerPoints: any[] = [];
      scatterData.forEach((point, index) => {
        if (point.clusterCenter) {
          centerPoints.push({
            id: -10000 - index, // 与links中的source对应
            value: point.clusterCenter,
            symbolSize: 0, // 大小为0，不显示
          });
        }
      });

      // 根据聚类方式添加图例
      switch (this.clusteringBy) {
        case "none":
          // 不聚类，所有点都是蓝色
          newData.push({
            name: "All Patients",
            type: "scatter",
            symbolSize: 10,
            itemStyle: {
              opacity: 0.85,
              shadowBlur: 8,
              shadowColor: "rgba(33, 150, 243, 0.4)",
            },
            emphasis: {
              scale: true,
              itemStyle: {
                shadowBlur: 15,
                borderColor: "#fff",
                borderWidth: 2,
                opacity: 1,
              },
            },
            data: scatterData,
            z: 10,
          });
          break;

        case "age":
          // 按年龄段聚类
          const ageGroups = [
            { name: "18-40", color: "#2196f3" },
            { name: "41-60", color: "#f44336" },
            { name: "61+", color: "#4caf50" },
          ];

          ageGroups.forEach((group) => {
            legendData.push(group.name);

            // 筛选属于该年龄组的点
            const groupData = scatterData.filter(
              (item) => item.groupName === group.name
            );

            newData.push({
              name: group.name,
              type: "scatter",
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`,
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: "#fff",
                  borderWidth: 2,
                  opacity: 1,
                },
              },
              data: groupData,
              z: 10,
            });
          });
          break;

        case "disease":
          // 按疾病类型聚类
          const diseaseGroups = [
            { name: "Macular Edema", color: "#2196f3" },
            { name: "Open-Angle Glaucoma", color: "#f44336" },
            { name: "Closed-Angle Glaucoma", color: "#4caf50" },
            { name: "Diabetic Maculopathy", color: "#ff9800" },
            { name: "Retinitis Pigmentosa", color: "#9c27b0" },
            { name: "Branch Retinal Vein Occlusion", color: "#607d8b" },
          ];

          diseaseGroups.forEach((group) => {
            legendData.push(group.name);

            // 筛选属于该疾病组的点
            const groupData = scatterData.filter(
              (item) => item.groupName === group.name
            );

            newData.push({
              name: group.name,
              type: "scatter",
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`,
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: "#fff",
                  borderWidth: 2,
                  opacity: 1,
                },
              },
              data: groupData,
              z: 10,
            });
          });
          break;

        case "visits":
          // 按就诊次数聚类
          const visitGroups = [
            { name: "First Visit", color: "#2196f3" },
            { name: "1-3 Visits", color: "#f44336" },
            { name: "4+ Visits", color: "#4caf50" },
          ];

          visitGroups.forEach((group) => {
            legendData.push(group.name);

            // 筛选属于该就诊组的点
            const groupData = scatterData.filter(
              (item) => item.groupName === group.name
            );

            newData.push({
              name: group.name,
              type: "scatter",
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`,
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: "#fff",
                  borderWidth: 2,
                  opacity: 1,
                },
              },
              data: groupData,
              z: 10,
            });
          });
          break;

        case "other":
          // 按性别聚类
          const genderGroups = [
            { name: "Male", color: "#2196f3" },
            { name: "Female", color: "#e91e63" },
          ];

          genderGroups.forEach((group) => {
            legendData.push(group.name);

            // 筛选属于该性别组的点
            const groupData = scatterData.filter(
              (item) => item.groupName === group.name
            );

            newData.push({
              name: group.name,
              type: "scatter",
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`,
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: "#fff",
                  borderWidth: 2,
                  opacity: 1,
                },
              },
              data: groupData,
              z: 10,
            });
          });
          break;
      }

      // 更新图表选项，正确处理legend属性
      const options: any = {
        animation: true,
        animationDuration: 1500,
        animationEasing: "cubicOut",
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "value",
          show: false,
        },
        grid: {
          left: "2%",
          right: "2%",
          top: "2%",
          bottom: "2%",
          containLabel: false,
        },
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            // 检查是否有patientId字段
            if (params.data && params.data.patientId) {
              return `<div style="padding: 8px 12px; min-width: 180px;">
                <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">Patient ID: ${
                  params.data.patientId
                }</div>
                <div style="margin-bottom: 3px;">Disease: ${
                  params.data.groupName || params.seriesName
                }</div>
                <div>Click to view patient details</div>
              </div>`;
            }

            return `<div style="padding: 8px 12px; min-width: 180px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">ID: ${
                params.dataIndex
              }</div>
              <div>Value: (${params.data[0].toFixed(
                2
              )}, ${params.data[1].toFixed(2)})</div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText:
            "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100; min-width: 180px;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        series: newData,
      };

      // 只有在非'none'模式下才添加legend
      if (this.clusteringBy !== "none") {
        options.legend = {
          data: legendData,
          type: "scroll",
          orient: "horizontal",
          bottom: 5,
          textStyle: {
            fontSize: 10,
          },
          itemWidth: 10,
          itemHeight: 10,
          pageIconSize: 12,
        };

        // 添加连接线的图表
        options.series.push({
          type: "graph",
          layout: "none",
          coordinateSystem: "cartesian2d",
          symbolSize: 0,
          // 添加中心点数据（但不显示）
          data: centerPoints,
          // 添加从中心点到各数据点的连接
          edges: clusterLinks,
          z: 5,
          tooltip: {
            show: false,
          },
        });
      }

      this.scatterOption = options;
    },

    // 生成真实患者的散点数据
    generateScatterData() {
      const data: any[] = [];

      // 从疾病数据中获取所有患者，优先考虑选中的疾病
      const allPatients: PatientData[] = [];

      // 首先添加选中的疾病患者
      const selectedDiseases = [
        this.selectedDisease1,
        this.selectedDisease2,
      ].filter((d) => d);

      // 如果有选中的疾病，优先显示这些疾病的患者
      if (selectedDiseases.length > 0) {
        selectedDiseases.forEach((diseaseId) => {
          const disease = this.ophthalmicDiseases.find(
            (d) => d.id === diseaseId
          );
          if (disease) {
            disease.patients.forEach((patientId) => {
              if (this.patientDetails[patientId]) {
                allPatients.push({
                  id: patientId,
                  disease: disease.id,
                  details: this.patientDetails[patientId],
                });
              }
            });
          }
        });
      }

      // 如果没有选中疾病或者需要显示更多患者，添加其他疾病的患者
      if (allPatients.length < 50) {
        this.ophthalmicDiseases.forEach((disease) => {
          // 如果不是已选中的疾病，添加其患者
          if (!selectedDiseases.includes(disease.id)) {
            disease.patients.forEach((patientId) => {
              if (this.patientDetails[patientId] && allPatients.length < 100) {
                allPatients.push({
                  id: patientId,
                  disease: disease.id,
                  details: this.patientDetails[patientId],
                });
              }
            });
          }
        });
      }

      // 检查聚类方式，根据不同的聚类方式生成不同的数据
      switch (this.clusteringBy) {
        case "none":
          // 默认不聚类，所有点都是蓝色随机分布
          allPatients.forEach((patient: PatientData) => {
            // 使用更自然的随机分布
            // 使用高斯分布而不是均匀分布，使点更自然地聚集
            // Box-Muller变换生成正态分布的随机数
            const u = 1 - Math.random(); // 避免对数中的0
            const v = 1 - Math.random();
            const z =
              Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);

            // 缩放和偏移以适应可视区域
            const x = z * 1.5;
            const y =
              Math.sqrt(-2.0 * Math.log(u)) * Math.sin(2.0 * Math.PI * v) * 1.5;

            // 所有点都使用蓝色
            const color = "#2196f3"; // 统一蓝色

            data.push({
              value: [x, y],
              itemStyle: {
                color: color,
              },
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease,
            });
          });
          break;

        case "age":
          // 按年龄段聚类: 青年(18-40)，中年(41-60)，老年(61+)
          const ageGroups = [
            { name: "18-40", x: -3, y: -3, radius: 2, color: "#2196f3" },
            { name: "41-60", x: 0, y: 3, radius: 2, color: "#f44336" },
            { name: "61+", x: 3, y: -3, radius: 2, color: "#4caf50" },
          ];

          allPatients.forEach((patient: PatientData) => {
            const age = patient.details.age;
            let groupIndex;

            if (age <= 40) {
              groupIndex = 0; // 青年
            } else if (age <= 60) {
              groupIndex = 1; // 中年
            } else {
              groupIndex = 2; // 老年
            }

            const group = ageGroups[groupIndex];

            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;

            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;

            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;

            // 如果是选中的疾病，使用不同的标记样式
            let symbolSize = 10;
            if (
              patient.disease === this.selectedDisease1 ||
              patient.disease === this.selectedDisease2
            ) {
              symbolSize = 14;
            }

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease,
            });
          });
          break;

        case "disease":
          // 按疾病类型聚类，使用六边形布局避免直线
          const diseaseGroups = [
            {
              id: "macular_edema",
              name: "Macular Edema",
              x: -3,
              y: 0,
              radius: 2,
              color: "#2196f3",
            },
            {
              id: "open_angle_glaucoma",
              name: "Open-Angle Glaucoma",
              x: -1.5,
              y: 2.6,
              radius: 2,
              color: "#f44336",
            },
            {
              id: "closed_angle_glaucoma",
              name: "Closed-Angle Glaucoma",
              x: 1.5,
              y: 2.6,
              radius: 2,
              color: "#4caf50",
            },
            {
              id: "diabetic_maculopathy",
              name: "Diabetic Maculopathy",
              x: 3,
              y: 0,
              radius: 2,
              color: "#ff9800",
            },
            {
              id: "retinitis_pigmentosa",
              name: "Retinitis Pigmentosa",
              x: 1.5,
              y: -2.6,
              radius: 2,
              color: "#9c27b0",
            },
            {
              id: "branch_retinal_vein_occlusion",
              name: "Branch Retinal Vein Occlusion",
              x: -1.5,
              y: -2.6,
              radius: 2,
              color: "#607d8b",
            },
          ];

          // 按疾病分组
          const patientsByDisease: Record<string, PatientData[]> = {};
          allPatients.forEach((patient) => {
            if (!patientsByDisease[patient.disease]) {
              patientsByDisease[patient.disease] = [];
            }
            patientsByDisease[patient.disease].push(patient);
          });

          // 为每种疾病的患者创建点
          diseaseGroups.forEach((diseaseGroup) => {
            const diseasePatients = patientsByDisease[diseaseGroup.id] || [];

            // 高亮显示选中的疾病
            const isSelected =
              diseaseGroup.id === this.selectedDisease1 ||
              diseaseGroup.id === this.selectedDisease2;
            const groupRadius = isSelected
              ? diseaseGroup.radius * 1.2
              : diseaseGroup.radius;

            diseasePatients.forEach((patient: PatientData) => {
              // 使用高斯分布创建更自然的聚类
              const u = 1 - Math.random();
              const v = 1 - Math.random();
              const r = Math.sqrt(-2.0 * Math.log(u));
              const theta = 2.0 * Math.PI * v;

              // 缩放随机值以适应聚类半径
              const xOffset = r * Math.cos(theta) * groupRadius * 0.5;
              const yOffset = r * Math.sin(theta) * groupRadius * 0.5;

              // 添加一些随机抖动以打破任何可能的模式
              const jitter = 0.3;
              const x =
                diseaseGroup.x + xOffset + (Math.random() - 0.5) * jitter;
              const y =
                diseaseGroup.y + yOffset + (Math.random() - 0.5) * jitter;

              // 如果是选中的疾病，使用更大的点
              const symbolSize = isSelected ? 14 : 10;

              data.push({
                value: [x, y],
                itemStyle: {
                  color: diseaseGroup.color,
                  opacity: isSelected ? 1 : 0.8,
                },
                symbolSize: symbolSize,
                groupName: diseaseGroup.name,
                name: `Patient ${patient.id}`,
                patientId: patient.id,
                diseaseId: patient.disease,
              });
            });
          });
          break;

        case "visits":
          // 按就诊次数聚类: 首诊, 随访1-3次, 随访4次以上
          const visitGroups = [
            { name: "First Visit", x: -3, y: 0, radius: 2, color: "#2196f3" },
            { name: "1-3 Visits", x: 0, y: 2.6, radius: 2, color: "#f44336" },
            { name: "4+ Visits", x: 3, y: 0, radius: 2, color: "#4caf50" },
          ];

          allPatients.forEach((patient: PatientData) => {
            const visits = patient.details.visits || 1;
            let groupIndex;

            if (visits === 1) {
              groupIndex = 0; // 首诊
            } else if (visits <= 3) {
              groupIndex = 1; // 随访1-3次
            } else {
              groupIndex = 2; // 随访4次以上
            }

            const group = visitGroups[groupIndex];

            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;

            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;

            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;

            // 如果是选中的疾病，使用不同的标记样式
            const isSelectedDisease =
              patient.disease === this.selectedDisease1 ||
              patient.disease === this.selectedDisease2;
            const symbolSize = isSelectedDisease ? 14 : 10;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
                borderColor: isSelectedDisease ? "#000" : "transparent",
                borderWidth: isSelectedDisease ? 1 : 0,
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease,
            });
          });
          break;

        case "other":
          // 按性别聚类
          const genderGroups = [
            { name: "Male", x: -2, y: 0, radius: 2, color: "#2196f3" },
            { name: "Female", x: 2, y: 0, radius: 2, color: "#e91e63" },
          ];

          allPatients.forEach((patient: PatientData) => {
            const gender = patient.details.gender;
            const groupIndex = gender === "Male" ? 0 : 1;
            const group = genderGroups[groupIndex];

            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;

            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;

            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;

            // 如果是选中的疾病，使用不同的标记样式
            const isSelectedDisease =
              patient.disease === this.selectedDisease1 ||
              patient.disease === this.selectedDisease2;
            const symbolSize = isSelectedDisease ? 14 : 10;

            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
                borderColor: isSelectedDisease ? "#000" : "transparent",
                borderWidth: isSelectedDisease ? 1 : 0,
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease,
            });
          });
          break;
      }

      return data;
    },

    // 将hex颜色转换为RGB格式
    hexToRgb(hex: string) {
      // 移除#
      hex = hex.replace("#", "");

      // 解析r,g,b
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return `${r}, ${g}, ${b}`;
    },

    // 加深颜色
    darkenColor(hex: string) {
      // 移除#
      hex = hex.replace("#", "");

      // 解析r,g,b
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);

      // 降低颜色值以加深颜色
      r = Math.max(0, r - 40);
      g = Math.max(0, g - 40);
      b = Math.max(0, b - 40);

      // 将结果转换回hex
      return `#${r.toString(16).padStart(2, "0")}${g
        .toString(16)
        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
    },
    // 打开AI对话框
    openAIDialog() {
      this.showAIDialog = true;
      console.log("Opening AI comparison dialog...");
    },
    // 关闭AI对话框
    closeAIDialog() {
      this.showAIDialog = false;
    },
    // 下载报告
    downloadReport() {
      console.log("Downloading AI comparison report...");
      // 这里可以添加实际的下载逻辑
    },
    // 发送消息
    sendMessage() {
      if (!this.userMessage.trim()) return;

      // 获取当前时间
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      const timeString = `${hours}:${minutes}`;

      // 添加用户消息
      this.chatMessages.push({
        sender: "user",
        text: this.userMessage,
        time: timeString,
      });

      // 清空输入框
      const userQuestion = this.userMessage;
      this.userMessage = "";

      // 显示AI正在输入
      this.isAiTyping = true;

      // 立即滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 模拟AI思考时间
      setTimeout(() => {
        this.isAiTyping = false;

        // 根据用户问题生成AI回复
        const aiResponse = this.generateAIResponse(userQuestion);

        // 添加AI回复
        this.chatMessages.push({
          sender: "ai",
          text: aiResponse,
          time: timeString,
        });

        // 滚动到最新消息
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 1500);
    },
    // 添加一个新方法来滚动到底部
    scrollToBottom() {
      if (this.$refs.chatMessages) {
        (this.$refs.chatMessages as HTMLElement).scrollTop = (
          this.$refs.chatMessages as HTMLElement
        ).scrollHeight;
      }
    },
    // 生成AI回复
    generateAIResponse(question: string) {
      // 模拟AI分析问题并生成回复
      question = question.toLowerCase();

      // 一些预设的问题和回复
      if (question.includes("difference") || question.includes("different")) {
        return `Based on my analysis, the key differences between these patients are:<br>
               <ul>
                 <li><strong>Disc Morphology:</strong> Patient 1 has a posterior disc bulge at C5-C6 with mild spinal canal stenosis, while Patient 2 shows normal disc alignment.</li>
                 <li><strong>Neural Compression:</strong> Patient 1 shows mild compression of the ventral thecal sac, not seen in Patient 2.</li>
                 <li><strong>Signal Intensity:</strong> There is increased T2 signal intensity in the affected disc in Patient 1, indicating possible inflammatory changes.</li>
               </ul>`;
      } else if (
        question.includes("similar") ||
        question.includes("similarities")
      ) {
        return `The main similarities between these patients include:<br>
               <ul>
                 <li>Both maintain normal cervical lordosis</li>
                 <li>Neither patient shows evidence of fracture or dislocation</li>
                 <li>Both have normal vertebral body height and signal intensity</li>
                 <li>Adjacent C4-C5 and C6-C7 levels appear normal in both patients</li>
               </ul>`;
      } else if (
        question.includes("treatment") ||
        question.includes("therapy") ||
        question.includes("manage")
      ) {
        return `For the patient with disc bulging (Patient 1), typical management approaches include:<br>
               <ul>
                 <li><strong>Conservative treatment:</strong> Physical therapy, NSAIDs, and activity modification for 6-8 weeks</li>
                 <li><strong>If symptoms persist:</strong> Consider epidural steroid injection</li>
                 <li><strong>For significant neurological symptoms:</strong> Surgical consultation may be warranted</li>
                 <li><strong>Follow-up imaging:</strong> Recommended in 6 months if symptoms persist</li>
               </ul>
               Would you like me to provide more specific recommendations based on the patient's clinical presentation?`;
      } else if (
        question.includes("prognosis") ||
        question.includes("outlook")
      ) {
        return `The prognosis for Patient 1 with cervical disc bulging is generally favorable:<br>
               <ul>
                 <li>Approximately 70-80% of patients with similar findings improve with conservative management</li>
                 <li>Recovery timeline typically ranges from 6-12 weeks</li>
                 <li>Risk of progression to disc herniation is relatively low (~15%)</li>
                 <li>Regular follow-up is recommended to monitor for any neurological changes</li>
               </ul>`;
      } else if (
        question.includes("diagnosis") ||
        question.includes("diagnose")
      ) {
        return `Based on the imaging findings, I would diagnose Patient 1 with:<br>
               <strong>Cervical spondylosis with C5-C6 disc bulge and mild spinal canal stenosis</strong><br><br>
               Differential diagnoses to consider include:<br>
               <ul>
                 <li>Disc herniation (less likely given the contour pattern)</li>
                 <li>Facet joint arthropathy (would need additional views to evaluate)</li>
                 <li>Ligamentum flavum hypertrophy (contributing factor to stenosis)</li>
               </ul>`;
      } else {
        return `Thank you for your question. As an AI diagnostic assistant, I can help analyze the differences between these patients, suggest treatment approaches, provide prognostic information, or offer educational insights about cervical spine pathology. What specific aspect would you like me to address?`;
      }
    },
    // 添加：开始比较分析
    startComparison() {
      this.showAIChat = true;
      this.activeChatTab = "chat";

      // 自动收缩比较面板
      this.isComparisonCollapsed = true;

      // 添加初始AI消息
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      const timeString = `${hours}:${minutes}`;

      this.chatMessages = [
        {
          sender: "ai",
          text: "I've analyzed both patients. Patient 1 shows a C5-C6 disc bulge with mild canal stenosis, while Patient 2 has normal cervical spine anatomy. How can I help with your analysis?",
          time: timeString,
        },
      ];

      // 确保初始消息显示后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 添加：处理患者选择变化
    handlePatientChange(event: Event) {
      // 当患者选择变化时，更新SHAP图表
      this.updatePatientComparison();

      // 如果对话已经开始，则自动收起对话框
      if (this.showAIChat) {
        this.isComparisonCollapsed = false;
      }
    },
    // 添加：切换比较面板显示状态
    toggleComparisonPanel() {
      this.isComparisonCollapsed = !this.isComparisonCollapsed;
    },
    // 更新左侧疾病选择
    updateLeftDisease(event: Event) {
      this.selectedLeftDisease = (event.target as HTMLSelectElement).value;
      this.updateDiseaseImages(this.selectedLeftDisease);
    },

    // 更新右侧疾病选择
    updateRightDisease(event: Event) {
      this.selectedRightDisease = (event.target as HTMLSelectElement).value;
      // 这里可以添加更新图表或数据的逻辑
    },
    
    // 同步患者下拉列表与知识图谱患者
    syncPatientDropdownWithGraph() {
      // 获取当前所选疾病的患者列表（来自知识图谱数据）
      const disease1Patients = this.getPatientsFromClustering(this.selectedDisease1);
      const disease2Patients = this.getPatientsFromClustering(this.selectedDisease2);
      
      // 合并两个疾病的患者列表（用于下拉框）
      const allGraphPatients = [...disease1Patients, ...disease2Patients];
      
      // 更新下拉选择框的患者ID列表
      this.realPatientIds = allGraphPatients.map(patient => patient.id);
      
      // 保存患者所属的图谱信息（用于显示标签）
      this.patientGraphMapping = {};
      
      // 为第一个图谱的患者添加映射
      disease1Patients.forEach(patient => {
        this.patientGraphMapping[patient.id] = this.selectedDisease1;
      });
      
      // 为第二个图谱的患者添加映射
      disease2Patients.forEach(patient => {
        this.patientGraphMapping[patient.id] = this.selectedDisease2;
      });
      
      console.log(`已同步下拉框患者列表，共 ${this.realPatientIds.length} 个患者`);
    },
    
    // 获取患者所属图谱的标签
    getPatientGraphLabel(patientId) {
      const diseaseId = this.patientGraphMapping[patientId];
      if (!diseaseId) return '';
      
      // 根据疾病ID，返回简短标签
      if (diseaseId === this.selectedDisease1) {
        return `(图1: ${this.formatDiseaseName(diseaseId)})`;
      } else if (diseaseId === this.selectedDisease2) {
        return `(图2: ${this.formatDiseaseName(diseaseId)})`;
      }
      
      return '';
    },

    // 获取疾病相关患者
    getPatientsForDisease(diseaseId: string) {
      const disease = this.ophthalmicDiseases.find((d) => d.id === diseaseId);
      return disease ? disease.patients : [];
    },

    // 更新疾病图像
    updateDiseaseImages(diseaseId: string) {
      if (this.diseaseImages[diseaseId as keyof typeof this.diseaseImages]) {
        this.currentDiseaseImages =
          this.diseaseImages[diseaseId as keyof typeof this.diseaseImages];
      }
    },
    // 获取疾病描述
    getDiseaseDescription(diseaseId: string) {
      const descriptions = {
        macular_edema:
          "Macular edema is a condition characterized by swelling of the macula, which is the part of the retina responsible for central vision. The fundus image shows microaneurysms, hemorrhages, and exudates, while the OCT scan can reveal macular edema.",
        open_angle_glaucoma:
          "Open-angle glaucoma is a type of glaucoma that affects the optic nerve. The fundus image shows optic nerve cupping and the OCT scan reveals thinning of the retinal nerve fiber layer.",
        closed_angle_glaucoma:
          "Closed-angle glaucoma is a type of glaucoma that affects the optic nerve. The fundus image shows optic nerve cupping and the OCT scan reveals thinning of the retinal nerve fiber layer.",
        diabetic_maculopathy:
          "Diabetic maculopathy involves damage to the blood vessels in the retina. The fundus image shows microaneurysms, hemorrhages, and exudates, while the OCT scan can reveal macular edema.",
        retinitis_pigmentosa:
          "Retinitis pigmentosa is a genetic disorder that affects the retina. The fundus image shows drusen or geographic atrophy, and the OCT scan reveals changes in the retinal pigment epithelium.",
        branch_retinal_vein_occlusion:
          "Branch retinal vein occlusion is a condition that affects the retina. The fundus image shows a retinal break, subretinal fluid, vitreous traction, and photopsia (flashes of light).",
      };

      return (
        descriptions[diseaseId as keyof typeof descriptions] ||
        "No description available."
      );
    },

    // 获取疾病特征
    getDiseaseFeatures(diseaseId: string) {
      const features = {
        macular_edema: [
          "Increased cup-to-disc ratio",
          "Retinal nerve fiber layer thinning",
          "Visual field defects",
          "Elevated intraocular pressure",
        ],
        open_angle_glaucoma: [
          "Increased optic nerve cupping",
          "Retinal nerve fiber layer thinning",
          "Visual field defects",
          "Elevated intraocular pressure",
        ],
        closed_angle_glaucoma: [
          "Increased optic nerve cupping",
          "Retinal nerve fiber layer thinning",
          "Visual field defects",
          "Elevated intraocular pressure",
        ],
        diabetic_maculopathy: [
          "Microaneurysms",
          "Hemorrhages",
          "Hard exudates",
          "Cotton wool spots",
          "Neovascularization",
        ],
        retinitis_pigmentosa: [
          "Drusen",
          "Geographic atrophy",
          "Choroidal neovascularization",
          "Pigmentary changes",
        ],
        branch_retinal_vein_occlusion: [
          "Retinal breaks",
          "Subretinal fluid",
          "Vitreous traction",
          "Photopsia (flashes of light)",
        ],
      };

      return features[diseaseId as keyof typeof features] || [];
    },

    // 格式化疾病名称显示
    formatDiseaseName(diseaseId: string) {
      if (!diseaseId) return "";

      // 将下划线替换为空格，并首字母大写
      return diseaseId
        .split("_")
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    },

    // 获取疾病对应的颜色（与聚类颜色方案一致）
    getDiseaseColor(diseaseId: string) {
      // 使用与PatientClustering相同的颜色方案
      const clusteringColors = [
        '#4682B4', '#5F9EA0', '#6495ED', '#00BFFF', '#1E90FF',
        '#87CEEB', '#87CEFA', '#ADD8E6', '#B0E0E6', '#00CED1',
        '#48D1CC', '#40E0D0', '#7FFFD4', '#66CDAA', '#20B2AA'
      ];

      // 为常见疾病分配固定颜色
      const diseaseColorMap = {
        macular_edema: clusteringColors[0],
        open_angle_glaucoma: clusteringColors[1],
        closed_angle_glaucoma: clusteringColors[2],
        diabetic_maculopathy: clusteringColors[3],
        retinitis_pigmentosa: clusteringColors[4],
        branch_retinal_vein_occlusion: clusteringColors[5],
      };

      // 如果是已知疾病，返回固定颜色
      if (diseaseColorMap[diseaseId as keyof typeof diseaseColorMap]) {
        return diseaseColorMap[diseaseId as keyof typeof diseaseColorMap];
      }

      // 对于其他疾病，基于名称生成一致的颜色
      if (diseaseId) {
        let hash = 0;
        for (let i = 0; i < diseaseId.length; i++) {
          const char = diseaseId.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // 转换为32位整数
        }
        const colorIndex = Math.abs(hash) % clusteringColors.length;
        return clusteringColors[colorIndex];
      }

      return clusteringColors[0]; // 默认颜色
    },

    // 生成真实疾病关联热力图选项
    generateRealCorrelationOption(correlationMatrix) {
      const diseases = correlationMatrix.diseases;
      const matrix = correlationMatrix.matrix;

      // 创建热力图数据
      const heatmapData = [];
      for (let i = 0; i < diseases.length; i++) {
        for (let j = 0; j < diseases.length; j++) {
          heatmapData.push([i, j, matrix[i][j]]);
        }
      }

      // 生成疾病简称
      const diseaseLabels = diseases.map(disease => {
        // 简化疾病名称显示
        if (disease.length > 8) {
          return disease.substring(0, 6) + '..';
        }
        return disease;
      });

      return {
        title: {
          text: 'Disease Correlation (Real Clustering Data)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          position: "top",
          formatter: function (params) {
            const xDisease = diseases[params.data[0]];
            const yDisease = diseases[params.data[1]];
            const correlation = params.data[2];

            return `<div style="padding: 8px 12px; min-width: 200px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">
                ${xDisease} ↔ ${yDisease}
              </div>
              <div style="font-size: 13px;">Correlation: ${correlation.toFixed(3)}</div>
              <div style="font-size: 12px; color: #666; margin-top: 3px;">
                ${correlation > 0.5 ? 'Strong' : correlation > 0.2 ? 'Moderate' : 'Weak'} Association
              </div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText: "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        animation: true,
        grid: {
          left: "2%",
          right: "7%",
          bottom: "15%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: diseaseLabels,
          splitArea: {
            show: true,
          },
          axisLabel: {
            fontSize: 9,
            interval: 0,
            rotate: 45,
            color: '#666'
          },
        },
        yAxis: {
          type: "category",
          data: diseaseLabels,
          splitArea: {
            show: true,
          },
          axisLabel: {
            fontSize: 9,
            interval: 0,
            color: '#666'
          },
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: true,
          orient: "horizontal",
          left: "center",
          bottom: 0,
          inRange: {
            color: [
              "#f3f8fe",
              "#deeafe",
              "#c6dbfd",
              "#90c1f9",
              "#5ba3f4",
              "#1e88e5",
              "#0d47a1",
            ],
          },
          textStyle: {
            color: '#666'
          }
        },
        series: [
          {
            name: "Disease Correlation",
            type: "heatmap",
            data: heatmapData,
            label: {
              show: true,
              formatter: function (params) {
                return params.data[2].toFixed(2);
              },
              fontSize: 8,
              color: function (params) {
                return params.data[2] > 0.5 ? "#fff" : "#333";
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },

    // 生成模拟疾病关联热力图选项
    generateMockCorrelationOption() {
      return {
        title: {
          text: 'Disease Correlation (Loading...)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          position: "top",
          formatter: function (params) {
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${
                params.name
              }</div>
              <div style="font-size: 13px;">Correlation: ${params.value[2].toFixed(
                2
              )}</div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText: "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        animation: true,
        grid: {
          left: "2%",
          right: "7%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["ME", "OAG", "CAG", "DM", "RP", "BRVO"],
          splitArea: {
            show: true,
          },
          axisLabel: {
            fontSize: 10,
            interval: 0,
          },
        },
        yAxis: {
          type: "category",
          data: ["ME", "OAG", "CAG", "DM", "RP", "BRVO"],
          splitArea: {
            show: true,
          },
          axisLabel: {
            fontSize: 10,
            interval: 0,
          },
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: true,
          orient: "horizontal",
          left: "center",
          bottom: 0,
          inRange: {
            color: [
              "#f3f8fe",
              "#deeafe",
              "#c6dbfd",
              "#90c1f9",
              "#5ba3f4",
              "#1e88e5",
              "#0d47a1",
            ],
          },
        },
        series: [
          {
            name: "Disease Correlation",
            type: "heatmap",
            data: [
              // ME correlations
              [0, 0, 1.0], [0, 1, 0.3], [0, 2, 0.2], [0, 3, 0.8], [0, 4, 0.4], [0, 5, 0.6],
              // OAG correlations
              [1, 0, 0.3], [1, 1, 1.0], [1, 2, 0.7], [1, 3, 0.4], [1, 4, 0.3], [1, 5, 0.2],
              // CAG correlations
              [2, 0, 0.2], [2, 1, 0.7], [2, 2, 1.0], [2, 3, 0.3], [2, 4, 0.2], [2, 5, 0.1],
              // DM correlations
              [3, 0, 0.8], [3, 1, 0.4], [3, 2, 0.3], [3, 3, 1.0], [3, 4, 0.2], [3, 5, 0.5],
              // RP correlations
              [4, 0, 0.4], [4, 1, 0.3], [4, 2, 0.2], [4, 3, 0.2], [4, 4, 1.0], [4, 5, 0.3],
              // BRVO correlations
              [5, 0, 0.6], [5, 1, 0.2], [5, 2, 0.1], [5, 3, 0.5], [5, 4, 0.3], [5, 5, 1.0],
            ],
            label: {
              show: true,
              formatter: function (params) {
                return params.data[2].toFixed(1);
              },
              fontSize: 10,
              color: function (params) {
                return params.data[2] > 0.5 ? "#fff" : "#333";
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
    },

    // 更新疾病显示
    updateDiseaseDisplay() {
      console.log(
        "Updating disease display:",
        this.selectedDisease1,
        this.selectedDisease2
      );
      // 更新图像
      this.updateDiseaseImages(this.selectedDisease1);

      // 自动选择每个疾病的第一个患者
      const disease1Patients = this.getPatientsForDisease(
        this.selectedDisease1
      );
      const disease2Patients = this.getPatientsForDisease(
        this.selectedDisease2
      );

      if (disease1Patients.length > 0) {
        this.selectedPatient1 = disease1Patients[0];
      }

      if (disease2Patients.length > 0) {
        this.selectedPatient2 = disease2Patients[0];
      }
      
      // 同步患者下拉列表与知识图谱中的患者
      this.syncPatientDropdownWithGraph();

      // 注意：不直接调用updatePatientComparison，而是让选择患者的逻辑触发SHAP图更新
      // 强制重新渲染视图会触发患者数据的更新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 获取单个疾病的数据
    getDiseaseData(diseaseId: string) {
      return {
        id: diseaseId,
        name: this.formatDiseaseName(diseaseId),
        color: this.getDiseaseColor(diseaseId),
      };
    },

    // 获取特定疾病的患者数据
    getPatientsDataForDisease(diseaseId: string) {
      const patients: Array<{ id: string; diseaseId: string }> = [];
      const diseasePatients = this.getPatientsForDisease(diseaseId);

      diseasePatients.forEach((patientId) => {
        patients.push({
          id: patientId,
          diseaseId: diseaseId,
        });
      });

      return patients;
    },

    // 从聚类数据中获取特定疾病的患者数据
    getPatientsFromClustering(diseaseId: string) {
      if (!this.visualizationData || this.visualizationData.length === 0) {
        console.log('聚类数据未加载，使用默认患者数据');
        return this.getPatientsDataForDisease(diseaseId); // 返回默认数据
      }

      // 扩展患者接口以包含诊断日期
      const patients: Array<{ id: string; diseaseId: string; diagnosisDate?: string }> = [];
      
      // 输出调试信息
      console.log(`尝试从聚类数据中查找疾病: ${diseaseId}`);
      console.log(`聚类数据大小: ${this.visualizationData.length} 条记录`);
      console.log(`聚类数据第一条记录:`, this.visualizationData[0]);
      
      // 根据聚类数据筛选出特定疾病的患者
      this.visualizationData.forEach(item => {
        // 尝试匹配多种可能的疾病标识符
        const itemDiagnosis = item.diagnosis || 
                             item.main_diagnosis_name || 
                             item.diseaseId ||
                             (item.categorical && item.categorical.main_diagnosis_name);
                             
        if (itemDiagnosis === diseaseId) {
          const patientId = item.patient_id || item.id || item.patientId;
          
          // 获取诊断日期（用于排序，找出最近的患者）
          const diagnosisDate = item.diagnosis_date || 
                              (item.raw && item.raw.diagnosis_date) || 
                              (item.raw && item.raw['诊断日期']) || 
                              item.visit_date || 
                              '';
          
          console.log(`匹配到疾病患者: ${patientId}, 疾病: ${itemDiagnosis}, 日期: ${diagnosisDate}`);
          
          patients.push({
            id: patientId,
            diseaseId: diseaseId,
            diagnosisDate: diagnosisDate
          });
        }
      });
      
      // 尝试按诊断日期排序（最近的排在前面）
      if (patients.length > 0 && patients[0].diagnosisDate) {
        console.log('按诊断日期排序患者记录...');
        patients.sort((a, b) => {
          if (!a.diagnosisDate) return 1;
          if (!b.diagnosisDate) return -1;
          return new Date(b.diagnosisDate).getTime() - new Date(a.diagnosisDate).getTime();
        });
      }

      console.log(`从聚类中找到 ${patients.length} 个患者属于疾病: ${diseaseId}`);
      
      // 如果没有找到患者，则返回默认数据
      if (patients.length === 0) {
        console.log(`未找到疾病 ${diseaseId} 的患者，使用默认数据`);
        return this.getPatientsDataForDisease(diseaseId);
      }
      
      // 限制只返回最多50条记录
      const limitedPatients = patients.length > 50 ? patients.slice(0, 50) : patients;
      console.log(`返回 ${limitedPatients.length}/${patients.length} 个聚类患者数据用于知识图谱（限制50条）`);
      return limitedPatients;
    },

    handleNodeClick(nodeData: any) {
      console.log("Node clicked:", nodeData);
      if (nodeData.type === "patient") {
        // Handle patient node click
        const patientId = nodeData.id;
        this.selectedPatient1 = patientId;
        this.fetchPatientComparisonData();
      } else if (nodeData.type === "disease") {
        // Handle disease node click
        const diseaseId = nodeData.id;
        this.selectedDisease1 = diseaseId;
        this.updateDiseaseDisplay();
      }
    },
    // 生成随机连接线
    generateRandomLinks(count: number) {
      const links = [];
      const maxIndex = 50; // 连接前50个数据点

      for (let i = 0; i < count; i++) {
        const source = Math.floor(Math.random() * maxIndex);
        let target;
        do {
          target = Math.floor(Math.random() * maxIndex);
        } while (target === source);

        links.push({
          source: source,
          target: target,
        });
      }

      return links;
    },

    // 更新患者对比数据
    updatePatientComparison() {
      const patient1 = this.patientDetails[this.selectedPatient1];
      const patient2 = this.patientDetails[this.selectedPatient2];

      if (!patient1 || !patient2) return;

      // 更新SHAP图表
      this.updateShapCharts(patient1, patient2);

      // 更新患者趋势图表
      this.updatePatientTrendCharts(patient1, patient2);

      // 强制重新渲染视图
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 更新患者趋势图表
    updatePatientTrendCharts(patient1: any, patient2: any) {
      // 根据患者ID生成确定性的趋势数据
      this.patientTrend1Option = this.generatePatientTrendOption(patient1);
      this.patientTrend2Option = this.generatePatientTrendOption(patient2);
    },

    // 生成患者趋势图配置
    generatePatientTrendOption(patient: any) {
      if (!patient) return {};

      // 使用患者ID和年龄生成确定性的伪随机数，以便每个患者的图表保持一致
      const patientSeed = parseInt(
        patient.disease.charCodeAt(0) + String(patient.age)
      );

      // 生成未来6个月的疾病发展趋势数据
      // 对于不同疾病，趋势线会有不同的形态
      let trendData = [];
      const months = ["M1", "M2", "M3", "M4", "M5", "M6"];

      // 根据疾病类型生成不同的趋势曲线
      switch (patient.disease) {
        case "macular_edema":
          // 黄斑水肿：先恶化后改善
          trendData = [60, 70, 75, 72, 65, 60].map(
            (v) => v + Math.sin(patientSeed) * 10
          );
          break;
        case "open_angle_glaucoma":
          // 开角型青光眼：持续恶化
          trendData = [50, 55, 62, 68, 75, 82].map(
            (v) => v + Math.cos(patientSeed) * 8
          );
          break;
        case "closed_angle_glaucoma":
          // 闭角型青光眼：急剧恶化后平稳
          trendData = [55, 75, 85, 87, 88, 89].map(
            (v) => v + Math.sin(patientSeed * 0.5) * 7
          );
          break;
        case "diabetic_maculopathy":
          // 糖尿病性黄斑病变：波动性恶化
          trendData = [60, 68, 65, 72, 70, 76].map(
            (v) => v + Math.tan(patientSeed * 0.3) * 5
          );
          break;
        case "retinitis_pigmentosa":
          // 视网膜色素变性：持续缓慢恶化
          trendData = [62, 65, 67, 70, 72, 75].map(
            (v) => v + Math.cos(patientSeed * 0.7) * 6
          );
          break;
        case "branch_retinal_vein_occlusion":
          // 视网膜分支静脉阻塞：急剧恶化后改善
          trendData = [55, 80, 75, 70, 67, 65].map(
            (v) => v + Math.sin(patientSeed * 0.4) * 9
          );
          break;
        default:
          trendData = [50, 55, 60, 65, 70, 75].map(
            (v) => v + Math.random() * 10
          );
      }

      return {
        tooltip: {
          trigger: "axis",
          formatter: function (params: any) {
            return `<div style="font-size: 12px; padding: 4px 8px;">
                    <div>Month ${params[0].axisValue.substring(1)}</div>
                    <div>
                      <span style="display:inline-block; width:10px; height:10px; border-radius:50%; background-color:${
                        params[0].color
                      };"></span>
                      Risk: ${params[0].data}%
                    </div>
                  </div>`;
          },
        },
        grid: {
          top: 5,
          right: 5,
          bottom: 8,
          left: 5,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: months,
          axisLabel: {
            fontSize: 9,
            color: "#666",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
        },
        yAxis: {
          type: "value",
          min: 40,
          max: 100,
          axisLabel: {
            fontSize: 9,
            color: "#666",
            formatter: "{value}%",
          },
          splitLine: {
            lineStyle: {
              color: "#eee",
            },
          },
        },
        series: [
          {
            name: "Disease Risk",
            type: "line",
            data: trendData,
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: "#ff7675",
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255, 118, 117, 0.4)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 118, 117, 0.1)",
                  },
                ],
              },
            },
            emphasis: {
              focus: "series",
            },
          },
        ],
      };
    },

    // 更新SHAP图表
    updateShapCharts(patient1: any, patient2: any) {
      // 准备SHAP图数据 - 左侧患者 (Patient 1)
      const patient1Indicators = this.prepareIndicatorData(patient1);

      this.shapChartOption1 = {
        title: {
          text: "Patient 1 Indicators",
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#666",
          },
          left: "center",
          top: 0,
        },
        grid: {
          top: 25,
          right: 5,
          bottom: 5,
          left: 5,
          containLabel: false,
        },
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            const value = params.value;
            const color = value > 0 ? "#1976d2" : "#64b5f6";
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${
                params.name
              }</div>
              <div style="color: ${color}; font-size: 13px;">Impact: ${
              value > 0 ? "+" : ""
            }${value}</div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText:
            "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "category",
          data: patient1Indicators.map((item) => item.name),
          show: false,
        },
        series: [
          {
            type: "bar",
            data: patient1Indicators.map((item) => {
              return {
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: item.value > 0 ? "#1976d2" : "#64b5f6",
                  borderRadius: 3,
                },
              };
            }),
            label: {
              show: false,
            },
            barWidth: "60%",
            barGap: "30%",
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "rgba(0,0,0,0.2)",
              },
            },
          },
        ],
      };

      // 准备SHAP图数据 - 右侧患者 (Patient 2)
      const patient2Indicators = this.prepareIndicatorData(patient2);

      this.shapChartOption2 = {
        title: {
          text: "Patient 2 Indicators",
          textStyle: {
            fontSize: 12,
            fontWeight: "normal",
            color: "#666",
          },
          left: "center",
          top: 0,
        },
        grid: {
          top: 25,
          right: 5,
          bottom: 5,
          left: 5,
          containLabel: false,
        },
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            const value = params.value;
            const color = value > 0 ? "#1976d2" : "#64b5f6";
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${
                params.name
              }</div>
              <div style="color: ${color}; font-size: 13px;">Impact: ${
              value > 0 ? "+" : ""
            }${value}</div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          extraCssText:
            "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;",
          enterable: true,
          confine: true,
          hideDelay: 300,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "category",
          data: patient2Indicators.map((item) => item.name),
          show: false,
        },
        series: [
          {
            type: "bar",
            data: patient2Indicators.map((item) => {
              return {
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: item.value > 0 ? "#1976d2" : "#64b5f6",
                  borderRadius: 3,
                },
              };
            }),
            label: {
              show: false,
            },
            barWidth: "60%",
            barGap: "30%",
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "rgba(0,0,0,0.2)",
              },
            },
          },
        ],
      };
    },

    // 准备指标数据
    prepareIndicatorData(patient: any) {
      if (!patient || !patient.indicators) return [];

      const result = [];

      // 使用患者ID的最后4位数字作为随机种子
      const patientIdSeed = parseInt(
        String(patient.disease).charCodeAt(0) + String(patient.age)
      );

      // 将指标数据转换为SHAP图所需格式
      for (const [key, value] of Object.entries(patient.indicators)) {
        // 格式化指标名称
        const formattedName = key
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");

        // 使用伪随机值，但基于患者ID，这样同一个患者的指标影响值会保持一致
        // 不同患者则会有不同的值
        let shapValue;
        if (typeof value === "number") {
          // 根据指标类型确定SHAP值的方向
          if (["visual_acuity"].includes(key)) {
            // 视力值越高越好
            shapValue =
              value > 0.5
                ? 0.6 * (1 + Math.sin(patientIdSeed * key.length * 0.1))
                : -0.6 * (1 + Math.cos(patientIdSeed * key.length * 0.1));
          } else if (
            [
              "intraocular_pressure",
              "central_macular_thickness",
              "cup_to_disc_ratio",
              "hba1c",
            ].includes(key)
          ) {
            // 这些指标值越低越好
            shapValue =
              value > 20
                ? -0.7 * (1 + Math.sin(patientIdSeed * key.length * 0.2))
                : 0.5 * (1 + Math.cos(patientIdSeed * key.length * 0.2));
          } else {
            // 使用患者ID作为随机种子，保证同一患者每次产生相同的"随机"值
            const deterministicRandom =
              Math.sin(patientIdSeed * key.length) * 0.5;
            shapValue = deterministicRandom * 0.8;
          }
        } else {
          // 文本型指标
          const strValue = String(value);
          if (
            ["Present", "Moderate", "Severe", "Numerous", "Sluggish"].includes(
              strValue
            )
          ) {
            // 负面影响，但每个患者有不同程度的差异
            shapValue =
              -0.5 - 0.3 * Math.sin(patientIdSeed * strValue.length * 0.3);
          } else {
            // 正面或中性影响，但每个患者有不同程度的差异
            shapValue =
              0.2 + 0.3 * Math.cos(patientIdSeed * strValue.length * 0.3);
          }
        }

        result.push({
          name: formattedName,
          value: parseFloat(shapValue.toFixed(2)),
        });
      }

      // 按SHAP值大小排序
      return result.sort((a, b) => b.value - a.value);
    },

    // 处理患者选择变化
    async handlePatientSelection(event, patientNum) {
      const selectedValue = event.target.value;
      if (patientNum === 1) {
        this.selectedPatient1 = selectedValue;
      } else {
        this.selectedPatient2 = selectedValue;
      }
      // 当患者选择变化时，获取新的对比数据
      await this.fetchPatientComparisonData();
    },

    // 获取患者疾病名称
    getPatientDiseaseName(patientId: string) {
      const patient = this.patientDetails[patientId];
      return patient ? this.formatDiseaseName(patient.disease) : "";
    },

    // 辅助方法：获取疾病的图像路径
    getImagePathForDisease(diseaseId: string) {
      // 根据疾病ID返回图像路径
      const diseaseImages = {
        macular_edema: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg",
        ],
        open_angle_glaucoma: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg",
        ],
        closed_angle_glaucoma: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-oct.jpg",
        ],
        diabetic_maculopathy: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-oct.jpg",
        ],
        retinitis_pigmentosa: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-oct.jpg",
        ],
        branch_retinal_vein_occlusion: [
          "https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg",
          "https://www.aao.org/images/k-assets/eye-health/diseases/brvo-oct.jpg",
        ],
      };

      // 随机选择这种疾病的一个图像
      const images =
        diseaseImages[diseaseId as keyof typeof diseaseImages] || [];
      if (images.length > 0) {
        return images[Math.floor(Math.random() * images.length)];
      }

      // 如果没有找到，返回默认图像
      return "https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-fundus.jpg";
    },

    // 生成缺失的患者详细信息
    generateMissingPatientDetails() {
      // 遍历每种疾病的患者列表
      this.ophthalmicDiseases.forEach((disease) => {
        disease.patients.forEach((patientId) => {
          // 如果患者详情不存在，则生成一个新的
          if (!this.patientDetails[patientId]) {
            // 从patientId提取数字信息以保持一致性
            const patientSeed = parseInt(patientId.slice(-5));
            const rand = (min: number, max: number) =>
              Math.floor(min + ((patientSeed % 100) / 100) * (max - min));

            // 生成年龄分布，不同疾病年龄段有所不同
            let age;
            if (disease.id === "macular_edema") {
              age = rand(55, 80); // 黄斑水肿多见于老年人
            } else if (disease.id === "open_angle_glaucoma") {
              age = rand(50, 85); // 开角型青光眼多见于老年人
            } else if (disease.id === "closed_angle_glaucoma") {
              age = rand(45, 75); // 闭角型青光眼年龄分布
            } else if (disease.id === "diabetic_maculopathy") {
              age = rand(40, 70); // 糖尿病性黄斑病变
            } else if (disease.id === "retinitis_pigmentosa") {
              age = rand(25, 60); // 视网膜色素变性多见于年轻人
            } else if (disease.id === "branch_retinal_vein_occlusion") {
              age = rand(45, 75); // 视网膜分支静脉阻塞
            } else {
              age = rand(30, 80); // 默认年龄范围
            }

            // 随机生成性别
            const gender = patientSeed % 2 === 0 ? "Male" : "Female";

            // 随机生成就诊次数
            const visits = rand(1, 12);

            // 随机生成住院次数 (通常小于就诊次数)
            const hospitalized = rand(0, Math.min(3, visits));

            // 根据疾病类型生成特定的指标
            let indicators: any = {};
            let textData = "";

            switch (disease.id) {
              case "macular_edema":
                indicators = {
                  visual_acuity: (rand(1, 10) / 10).toFixed(1),
                  intraocular_pressure: rand(14, 25),
                  central_macular_thickness: rand(300, 600),
                  hba1c: (rand(60, 100) / 10).toFixed(1),
                  blood_pressure: `${rand(120, 160)}/${rand(70, 100)}`,
                };
                textData = `Patient presents with ${
                  visits > 6 ? "longstanding" : "recent"
                } vision changes. OCT shows ${
                  indicators.central_macular_thickness > 450
                    ? "significant"
                    : "moderate"
                } macular edema with ${
                  rand(0, 1) ? "intraretinal fluid" : "cystoid changes"
                }. ${gender === "Male" ? "He" : "She"} has a ${
                  rand(0, 1) ? "history of diabetes" : "history of hypertension"
                } for ${rand(5, 20)} years.`;
                break;

              case "open_angle_glaucoma":
                indicators = {
                  visual_acuity: (rand(4, 10) / 10).toFixed(1),
                  intraocular_pressure: rand(20, 35),
                  cup_to_disc_ratio: (rand(6, 9) / 10).toFixed(1),
                  visual_field_md: -rand(3, 20),
                  corneal_thickness: rand(500, 570),
                };
                textData = `Patient with ${
                  indicators.intraocular_pressure > 25 ? "high" : "elevated"
                } intraocular pressure. Examination reveals increased cup-to-disc ratio of ${
                  indicators.cup_to_disc_ratio
                } with ${
                  rand(0, 1)
                    ? "thinning of neuroretinal rim"
                    : "nerve fiber layer loss"
                }. ${gender === "Male" ? "He" : "She"} ${
                  rand(0, 1)
                    ? "reports gradual peripheral vision loss"
                    : "is relatively asymptomatic"
                }.`;
                break;

              case "closed_angle_glaucoma":
                indicators = {
                  visual_acuity: (rand(1, 8) / 10).toFixed(1),
                  intraocular_pressure: rand(25, 60),
                  anterior_chamber_depth: (rand(15, 30) / 10).toFixed(1),
                  corneal_edema: ["Mild", "Moderate", "Severe"][rand(0, 2)],
                  pupil_reaction: ["Normal", "Sluggish", "Fixed"][rand(0, 2)],
                };
                textData = `Patient presented with ${
                  rand(0, 1)
                    ? "acute eye pain and headache"
                    : "blurred vision and halos around lights"
                }. Examination showed ${indicators.corneal_edema.toLowerCase()} corneal edema, ${indicators.pupil_reaction.toLowerCase()} pupillary reaction, and shallow anterior chamber. IOP was markedly elevated at ${
                  indicators.intraocular_pressure
                } mmHg.`;
                break;

              case "diabetic_maculopathy":
                indicators = {
                  visual_acuity: (rand(2, 8) / 10).toFixed(1),
                  hba1c: (rand(70, 120) / 10).toFixed(1),
                  microaneurysms: ["Few", "Moderate", "Numerous"][rand(0, 2)],
                  hard_exudates: ["Absent", "Present", "Extensive"][rand(0, 2)],
                  macular_edema: ["Mild", "Moderate", "Clinically significant"][
                    rand(0, 2)
                  ],
                };
                textData = `Patient with ${rand(
                  5,
                  25
                )}-year history of diabetes presents with gradually ${
                  rand(0, 1) ? "worsening vision" : "decreased visual acuity"
                }. Fundus examination shows ${indicators.microaneurysms.toLowerCase()} microaneurysms, ${indicators.hard_exudates.toLowerCase()} hard exudates, and ${indicators.macular_edema.toLowerCase()} macular edema. HbA1c is ${
                  indicators.hba1c
                }%.`;
                break;

              case "retinitis_pigmentosa":
                indicators = {
                  visual_acuity: (rand(3, 8) / 10).toFixed(1),
                  visual_field: [
                    "Mildly constricted",
                    "Moderately constricted",
                    "Severely constricted",
                  ][rand(0, 2)],
                  erg_amplitude: [
                    "Mildly reduced",
                    "Moderately reduced",
                    "Severely reduced",
                  ][rand(0, 2)],
                  bone_spicules: ["Minimal", "Moderate", "Extensive"][
                    rand(0, 2)
                  ],
                  night_vision: [
                    "Mildly impaired",
                    "Moderately impaired",
                    "Severely impaired",
                  ][rand(0, 2)],
                };
                textData = `Patient reports ${
                  indicators.night_vision
                } and progressive loss of peripheral vision. Fundus examination reveals ${
                  indicators.bone_spicules
                } bone spicule pigmentation, attenuated vessels, and ${
                  rand(0, 1) ? "waxy pallor" : "pale appearance"
                } of the optic disc. ${gender === "Male" ? "He" : "She"} has ${
                  rand(0, 1)
                    ? "family history of RP"
                    : "no known family history"
                }.`;
                break;

              case "branch_retinal_vein_occlusion":
                indicators = {
                  visual_acuity: (rand(2, 8) / 10).toFixed(1),
                  intraocular_pressure: rand(15, 25),
                  hemorrhages: ["Few", "Moderate", "Extensive flame-shaped"][
                    rand(0, 2)
                  ],
                  cotton_wool_spots: ["Absent", "Few", "Multiple"][rand(0, 2)],
                  macular_edema: ["Absent", "Mild", "Significant"][rand(0, 2)],
                };
                textData = `Patient noticed ${
                  rand(0, 1) ? "sudden" : "gradual"
                }, painless vision loss in the ${
                  rand(0, 1) ? "right" : "left"
                } eye ${rand(1, 8)} weeks ago. Fundus examination shows ${
                  indicators.hemorrhages
                } hemorrhages in the ${
                  rand(0, 1) ? "superotemporal" : "inferotemporal"
                } quadrant with ${
                  indicators.cotton_wool_spots
                } cotton wool spots. Patient has history of ${
                  rand(0, 1) ? "hypertension" : "cardiovascular disease"
                }.`;
                break;

              default:
                textData = "No detailed information available.";
            }

            // 生成图像路径，基于疾病类型
            const imagePath = this.getImagePathForDisease(disease.id);

            // 创建并保存患者详情
            this.patientDetails[patientId] = {
              disease: disease.id,
              age: age,
              gender: gender,
              visits: visits,
              hospitalized: hospitalized,
              indicators: indicators,
              textData: textData,
              imagePath: imagePath,
            };
          }
        });
      });
    },

    // 生成聚类中心点
    generateClusterCenters(): any[] {
      const centers = [];

      switch (this.clusteringBy) {
        case "age":
          // 按年龄段聚类
          centers.push(
            { name: "18-40", value: [-3, -3], itemStyle: { color: "#2196f3" } },
            { name: "41-60", value: [0, 0], itemStyle: { color: "#f44336" } },
            { name: "61+", value: [3, 3], itemStyle: { color: "#4caf50" } }
          );
          break;

        case "disease":
          // 按疾病类型聚类
          centers.push(
            {
              name: "Macular Edema",
              value: [-4, 0],
              itemStyle: { color: "#2196f3" },
            },
            {
              name: "Open-Angle Glaucoma",
              value: [-2, 2],
              itemStyle: { color: "#f44336" },
            },
            {
              name: "Closed-Angle Glaucoma",
              value: [0, -2],
              itemStyle: { color: "#4caf50" },
            },
            {
              name: "Diabetic Maculopathy",
              value: [2, 2],
              itemStyle: { color: "#ff9800" },
            },
            {
              name: "Retinitis Pigmentosa",
              value: [4, 0],
              itemStyle: { color: "#9c27b0" },
            },
            {
              name: "Branch Retinal Vein Occlusion",
              value: [0, 3],
              itemStyle: { color: "#607d8b" },
            }
          );
          break;

        case "visits":
          // 按就诊次数聚类
          centers.push(
            {
              name: "First Visit",
              value: [-3, 0],
              itemStyle: { color: "#2196f3" },
            },
            {
              name: "1-3 Visits",
              value: [0, 0],
              itemStyle: { color: "#f44336" },
            },
            {
              name: "4+ Visits",
              value: [3, 0],
              itemStyle: { color: "#4caf50" },
            }
          );
          break;

        case "other":
          // 按其他属性聚类
          centers.push(
            { name: "Male", value: [-2, -2], itemStyle: { color: "#2196f3" } },
            { name: "Female", value: [2, -2], itemStyle: { color: "#e91e63" } },
            { name: "Urban", value: [0, 2], itemStyle: { color: "#ff9800" } },
            { name: "Rural", value: [0, -2], itemStyle: { color: "#4caf50" } }
          );
          break;

        default:
          // 不聚类或默认情况
          break;
      }

      return centers;
    },

    // 生成聚类连接线
    generateClusterEdges(count: number): any[] {
      const edges: any[] = [];

      // 如果不聚类，则不生成连接线
      if (this.clusteringBy === "none") {
        return edges;
      }

      // 根据聚类方式生成连接线
      let centerCount = 0;
      let groupInfo: {
        [key: string]: { index: number; count: number; color: string };
      } = {};

      switch (this.clusteringBy) {
        case "age":
          groupInfo = {
            "18-40": { index: 0, count: 0, color: "#2196f3" },
            "41-60": { index: 1, count: 0, color: "#f44336" },
            "61+": { index: 2, count: 0, color: "#4caf50" },
          };
          centerCount = 3;
          break;

        case "disease":
          groupInfo = {
            "Macular Edema": { index: 0, count: 0, color: "#2196f3" },
            "Open-Angle Glaucoma": { index: 1, count: 0, color: "#f44336" },
            "Closed-Angle Glaucoma": { index: 2, count: 0, color: "#4caf50" },
            "Diabetic Maculopathy": { index: 3, count: 0, color: "#ff9800" },
            "Retinitis Pigmentosa": { index: 4, count: 0, color: "#9c27b0" },
            "Branch Retinal Vein Occlusion": {
              index: 5,
              count: 0,
              color: "#607d8b",
            },
          };
          centerCount = 6;
          break;

        case "visits":
          groupInfo = {
            "First Visit": { index: 0, count: 0, color: "#2196f3" },
            "1-3 Visits": { index: 1, count: 0, color: "#f44336" },
            "4+ Visits": { index: 2, count: 0, color: "#4caf50" },
          };
          centerCount = 3;
          break;

        case "other":
          groupInfo = {
            Male: { index: 0, count: 0, color: "#2196f3" },
            Female: { index: 1, count: 0, color: "#e91e63" },
            Urban: { index: 2, count: 0, color: "#ff9800" },
            Rural: { index: 3, count: 0, color: "#4caf50" },
          };
          centerCount = 4;
          break;
      }

      // 为每个组生成一定数量的连接线
      for (const group in groupInfo) {
        const info = groupInfo[group];
        const pointCount = Math.floor(count / Object.keys(groupInfo).length); // 每个组的点数量大致相等

        for (let i = 0; i < pointCount; i++) {
          // 为每个点创建一个连接到中心点的线
          const sourceIndex = info.index; // 中心点索引
          const targetIndex = centerCount + info.count; // 点的索引从centerCount开始

          edges.push({
            source: sourceIndex,
            target: targetIndex,
            lineStyle: {
              color: info.color,
              width: 0.5,
              opacity: 0.5,
            },
          });

          info.count++;
        }
      }

      return edges;
    },
    async checkBackendConnection() {
      try {
        console.log('检查后端连接状态...');
        this.isLoadingClustering = true;
        this.backendConnectionError = null;

        const connectionStatus = await patientDataProcessor.checkBackendConnection();

        if (connectionStatus.connected) {
          this.backendConnected = true;
          console.log('✅ 后端连接成功');

          // 尝试加载聚类数据
          await this.loadClusteringData();
        } else {
          this.backendConnected = false;
          this.backendConnectionError = connectionStatus.error;
          console.error('❌ 后端连接失败:', connectionStatus.error);
        }
      } catch (error) {
        this.backendConnected = false;
        this.backendConnectionError = error.message;
        console.error('❌ 后端连接检查失败:', error);
      } finally {
        this.isLoadingClustering = false;
      }
    },

    async loadClusteringData() {
      try {
        console.log('加载聚类数据...');
        this.isLoadingClustering = true;

        // 运行完整的聚类管道
        const result = await patientDataProcessor.runFullPipeline({ useDefault: true });

        if (result && result.visualizationData && result.clusterStats) {
          this.clusteringDataLoaded = true;
          console.log('✅ 聚类数据加载成功');
          
          // 保存可视化数据用于知识图谱展示
          this.visualizationData = result.visualizationData;
          console.log(`✅ 已保存 ${this.visualizationData.length} 条患者聚类数据用于知识图谱`);

          // 更新散点图数据
          this.updateScatterPlotWithClusteringData(result);
          
          // 加载后同步患者下拉列表与知识图谱患者
          this.$nextTick(() => {
            this.syncPatientDropdownWithGraph();
          });

          // 同步更新疾病列表以确保与聚类数据一致
          await this.syncDiseasesWithClusteringData();
        } else if (result && result.visualization_data) {
          // 处理不同格式的API返回
          this.clusteringDataLoaded = true;
          console.log('✅ 聚类数据加载成功(使用alternate格式)');
          
          // 保存可视化数据用于知识图谱展示
          this.visualizationData = result.visualization_data;
          console.log(`✅ 已保存 ${this.visualizationData.length} 条患者聚类数据用于知识图谱`);
          
          // 更新散点图数据
          this.updateScatterPlotWithClusteringData({
            visualizationData: result.visualization_data,
            clusterStats: result.cluster_stats || []
          });
          
          // 加载后同步患者下拉列表与知识图谱患者
          this.$nextTick(() => {
            this.syncPatientDropdownWithGraph();
          });
          
          // 同步更新疾病列表
          await this.syncDiseasesWithClusteringData();
        } else {
          throw new Error('聚类数据格式无效');
        }
      } catch (error) {
        this.clusteringDataLoaded = false;
        console.error('❌ 聚类数据加载失败:', error);
        throw error;
      } finally {
        this.isLoadingClustering = false;
      }
    },

    updateScatterPlotWithClusteringData(clusteringResult) {
      try {
        console.log('更新散点图数据...');

        // 转换聚类数据为散点图格式
        const scatterData = clusteringResult.visualizationData.map(point => [
          point.x,
          point.y
        ]);

        // 更新散点图配置
        this.scatterOption.series[0].data = scatterData;

        // 强制更新图表
        this.$nextTick(() => {
          // 触发图表重新渲染
          this.updateClusteringVisualization();
        });

        console.log(`✅ 散点图更新完成，包含 ${scatterData.length} 个数据点`);
      } catch (error) {
        console.error('❌ 更新散点图失败:', error);
      }
    },

    async loadDiseases() {
      if (!this.backendConnected) {
        console.warn('后端未连接，跳过疾病数据加载');
        return;
      }

      try {
        console.log('开始加载疾病数据...');

        // 使用新的API获取诊断列表
        const diagnosisData = await patientDataProcessor.getDiagnosisList();

        // 提取疾病名称列表
        this.diseases = diagnosisData.diagnoses.map(d => d.name);

        console.log(`成功加载 ${this.diseases.length} 种疾病`);

        // 默认选择前两个疾病
        if (this.diseases.length > 0) {
          this.selectedDisease1 = this.diseases[0];

          if (this.diseases.length > 1) {
            this.selectedDisease2 = this.diseases[1];
          } else {
            this.selectedDisease2 = this.diseases[0];
          }

          this.updateDiseaseDisplay();
        }

        // 同时获取数据摘要信息
        const summary = await patientDataProcessor.getDataSummary();
        console.log('数据摘要:', summary);

      } catch (error) {
        console.error("Error loading diseases:", error);
        this.backendConnectionError = error.message;
      }
    },

    // 新增：同步疾病列表与聚类数据（同步主要诊断代码）
    async syncDiseasesWithClusteringData() {
      try {
        console.log('同步疾病列表与聚类数据（主要诊断代码）...');

        // 重新获取最新的诊断列表（主要诊断代码）
        const diagnosisData = await patientDataProcessor.getDiagnosisList();
        const newDiseases = diagnosisData.diagnoses.map(d => d.name);

        // 检查是否有变化
        const diseasesChanged = JSON.stringify(this.diseases) !== JSON.stringify(newDiseases);

        if (diseasesChanged) {
          console.log('检测到主要诊断代码列表变化，更新Case Selection...');

          // 保存当前选择的疾病代码
          const currentDisease1 = this.selectedDisease1;
          const currentDisease2 = this.selectedDisease2;

          // 更新疾病代码列表
          this.diseases = newDiseases;

          // 验证当前选择的疾病代码是否仍然有效
          if (!this.diseases.includes(currentDisease1)) {
            this.selectedDisease1 = this.diseases.length > 0 ? this.diseases[0] : '';
            console.log(`Disease 1 已更新为: ${this.selectedDisease1}`);
          }

          if (!this.diseases.includes(currentDisease2)) {
            this.selectedDisease2 = this.diseases.length > 1 ? this.diseases[1] : this.diseases[0];
            console.log(`Disease 2 已更新为: ${this.selectedDisease2}`);
          }

          // 更新显示
          this.updateDiseaseDisplay();

          console.log('✅ Case Selection与聚类数据（主要诊断代码）同步完成');
        } else {
          console.log('主要诊断代码列表无变化，无需同步');
        }

      } catch (error) {
        console.error('同步主要诊断代码列表失败:', error);
      }
    },

    // 处理聚类方法改变事件
    async handleClusteringMethodChanged(method) {
      console.log(`聚类方法已改变为: ${method}`);

      // 如果是疾病聚类方法，同步疾病列表
      if (method === 'disease' && this.backendConnected) {
        await this.syncDiseasesWithClusteringData();
      }

      // 重新加载季节分布数据和疾病关联数据
      await this.loadRealSeasonalData();
      await this.loadDiseaseCorrelationData();
    },

    // 处理患者选择事件
    handlePatientsSelected(selectedPatients: any[]) {
      console.log(`选中了 ${selectedPatients.length} 个患者`);
      
      // 检查患者数据中是否包含诊断日期
      if (selectedPatients.length > 0) {
        const samplePatient = selectedPatients[0];
        console.log('选中患者示例:', {
          patientId: samplePatient.patientId || samplePatient.id,
          diagnosis: samplePatient.diagnosis || samplePatient.diseaseId,
          diagnosis_date: samplePatient.diagnosis_date || 
                         samplePatient.raw?.diagnosis_date || 
                         samplePatient.raw?.['诊断日期'] || 
                         samplePatient.raw?.visit_date
        });
      }
      
      this.selectedPatients = selectedPatients;

      // 根据选中的患者过滤季节分布数据
      this.filterSeasonalDataByPatients(selectedPatients);
    },

    // 加载真实季节分布数据
    async loadRealSeasonalData() {
      if (!this.backendConnected) {
        console.warn('后端未连接，跳过季节分布数据加载');
        return;
      }

      try {
        console.log('加载真实季节分布数据...');
        const seasonalData = await patientDataProcessor.getSeasonalDistribution();
        
        // 确保数据包含季度和月度系列
        if (seasonalData) {
          // 如果没有月度数据，复制季度数据
          if (!seasonalData.seriesMonth && seasonalData.seriesQuarter) {
            seasonalData.seriesMonth = seasonalData.seriesQuarter;
          }
          // 如果没有季度数据，复制月度数据
          if (!seasonalData.seriesQuarter && seasonalData.seriesMonth) {
            seasonalData.seriesQuarter = seasonalData.seriesMonth;
          }
        }
        
        this.realSeasonalData = seasonalData;
        this.filteredSeasonalData = null; // 重置过滤数据
        console.log('✅ 季节分布数据加载成功');
      } catch (error) {
        console.error('加载季节分布数据失败:', error);
      }
    },

    
    async loadDiseaseCorrelationData() {
      console.log('🔄 开始加载疾病关联分析数据(标准模式)...');
      console.log('  后端连接状态:', this.backendConnected);
      console.log('  当前关联分析模式:', this.correlationMode);

      if (!this.backendConnected) {
        console.warn('❌ 后端未连接，跳过疾病关联分析数据加载');
        return;
      }
      
      // 如果当前模式为Apriori且已经有数据，则跳过标准模式的加载
      if (this.correlationMode === 'apriori' && this.aprioriCorrelationOption) {
        console.log('✅ 当前为Apriori模式且已有数据，跳过标准模式加载');
        return;
      }

      try {
        console.log('📡 调用patientDataProcessor.getDiseaseCorrelation...');
        const correlationData = await patientDataProcessor.getDiseaseCorrelation({
          minSupport: 0.005,  // 更宽松的参数以获得更多关联规则
          minConfidence: 0.05,
          minLift: 0.8,
          topDiseases: 15
        });

        console.log('📊 收到疾病关联数据:', correlationData);

        this.diseaseCorrelationData = correlationData;
        this.correlationMatrix = correlationData.correlation_matrix;
        this.associationRules = correlationData.association_rules;

        console.log('✅ 疾病关联分析数据加载成功');
        console.log(`📈 关联矩阵大小: ${this.correlationMatrix?.size}x${this.correlationMatrix?.size}`);
        console.log(`📋 关联规则数量: ${this.associationRules?.length}`);
        console.log(`🔍 疾病列表:`, this.correlationMatrix?.diseases);

        // 强制更新视图
        this.$forceUpdate();

      } catch (error) {
        console.error('❌ 加载疾病关联分析数据失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
      }
    },

    // 根据选中患者过滤季节分布数据
    filterSeasonalDataByPatients(selectedPatients: any[]) {
      if (!selectedPatients || selectedPatients.length === 0) {
        // 如果没有选中患者，显示完整数据
        this.filteredSeasonalData = null;
        console.log('显示完整季节分布数据');
        return;
      }

      console.log('根据选中患者过滤季节分布数据...');
      console.log(`处理 ${selectedPatients.length} 个选中患者的真实分布`);
      
      // 打印患者样本数据用于调试
      if (selectedPatients.length > 0) {
        const samplePatient = selectedPatients[0];
        console.log('样本患者数据:', JSON.stringify(samplePatient, null, 2));
        if (samplePatient.raw) {
          console.log('样本患者raw数据:', JSON.stringify(samplePatient.raw, null, 2));
        }
      }

      // 初始化季度和月份计数
      const quarterCounts = {
        'Q1': { total: 0, diseases: {} },
        'Q2': { total: 0, diseases: {} },
        'Q3': { total: 0, diseases: {} },
        'Q4': { total: 0, diseases: {} }
      };
      
      const monthCounts = {
        '01': { total: 0, diseases: {} },
        '02': { total: 0, diseases: {} },
        '03': { total: 0, diseases: {} },
        '04': { total: 0, diseases: {} },
        '05': { total: 0, diseases: {} },
        '06': { total: 0, diseases: {} },
        '07': { total: 0, diseases: {} },
        '08': { total: 0, diseases: {} },
        '09': { total: 0, diseases: {} },
        '10': { total: 0, diseases: {} },
        '11': { total: 0, diseases: {} },
        '12': { total: 0, diseases: {} }
      };

      // 统计所有疾病和总患者数
      const allDiseases = new Set();
      const diseaseCount = {};
      let patientsWithDateCount = 0;

      // 遍历选中的患者
      selectedPatients.forEach(patient => {
        // 获取患者原始数据
        const patientData = patient.raw || patient;
        
        // 提取诊断信息
        let diagnosis = patientData.diagnosis || 
                       patientData.main_diagnosis || 
                       patientData['主要诊断'] || 
                       patientData['主要诊断名称'] ||
                       patientData['涓昏璇婃柇'] ||
                       patientData['涓昏璇婃柇鍚嶇О'] ||
                       patient.diagnosis ||
                       patient.diseaseId ||
                       '未知诊断';

        // 记录诊断统计
        if (diagnosis) {
          allDiseases.add(diagnosis);
          diseaseCount[diagnosis] = (diseaseCount[diagnosis] || 0) + 1;
        }

        // 提取日期信息 - 扩展支持的日期字段
        let dateStr = null;
        const dateFields = [
          '诊断日期', 'diagnosis_date', 'visit_date', '就诊日期', 
          '灏辫瘖鏃ユ湡', 'date', 'visitDate', 'diagnosisDate'
        ];
        
        // 检查患者原始数据中的日期字段
        for (const field of dateFields) {
          if (patientData[field]) {
            dateStr = patientData[field];
            console.log(`找到日期字段 ${field}: ${dateStr}`);
            break;
          }
        }

        // 如果在原始数据中没找到，尝试在patient对象中查找
        if (!dateStr) {
          for (const field of dateFields) {
            if (patient[field]) {
              dateStr = patient[field];
              console.log(`在patient对象中找到日期字段 ${field}: ${dateStr}`);
              break;
            }
          }
        }
        
        // 如果仍然没有找到日期，尝试生成随机日期用于演示
        if (!dateStr && this.realSeasonalData) {
          // 为演示目的生成一个随机日期
          const randomMonth = Math.floor(Math.random() * 12) + 1;
          const randomDay = Math.floor(Math.random() * 28) + 1;
          dateStr = `2023-${randomMonth.toString().padStart(2, '0')}-${randomDay.toString().padStart(2, '0')}`;
          console.log(`未找到日期字段，生成随机日期: ${dateStr}`);
        }
        
        if (dateStr) {
          try {
            // 解析日期 - 支持多种格式
            let month;
            
            if (dateStr.includes('-')) {
              const dateParts = dateStr.split('-');
              if (dateParts.length >= 2) {
                month = parseInt(dateParts[1]);
              }
            } else if (dateStr.includes('/')) {
              const dateParts = dateStr.split('/');
              if (dateParts.length >= 2) {
                // 假设格式为 MM/DD/YYYY 或 DD/MM/YYYY
                month = dateParts[0].length === 4 ? parseInt(dateParts[1]) : parseInt(dateParts[0]);
              }
            } else if (dateStr.length === 8) {
              // 假设格式为 YYYYMMDD
              month = parseInt(dateStr.substring(4, 6));
            }
            
            if (month && month >= 1 && month <= 12) {
              const monthStr = month.toString().padStart(2, '0');
              const quarter = `Q${Math.ceil(month / 3)}`;
              
              console.log(`处理月份数据: 月份=${month}, 季度=${quarter}, 诊断=${diagnosis}`);
              
              // 增加季度计数
              quarterCounts[quarter].total += 1;
              quarterCounts[quarter].diseases[diagnosis] = 
                (quarterCounts[quarter].diseases[diagnosis] || 0) + 1;

              // 增加月份计数
              monthCounts[monthStr].total += 1;
              monthCounts[monthStr].diseases[diagnosis] = 
                (monthCounts[monthStr].diseases[diagnosis] || 0) + 1;
              
              patientsWithDateCount++;
            } else {
              console.warn(`无效的月份值: ${month} (来自日期: ${dateStr})`);
            }
          } catch (error) {
            console.warn(`无法解析日期: ${dateStr}`, error);
          }
        } else {
          console.warn(`患者 ID ${patientData.id || patient.id || '未知'} 没有日期信息`);
        }
      });

      console.log('季度分布:', quarterCounts);
      console.log('月份分布:', monthCounts);
      console.log('疾病统计:', diseaseCount);
      console.log('有日期信息的患者数:', patientsWithDateCount);

      // 将疾病列表转换为数组
      const uniqueDiseases = Array.from(allDiseases);

      // 创建季度系列数据
      const quarterSeries = uniqueDiseases.map(disease => {
        return {
          name: disease,
          type: 'bar',
          stack: 'total',
          data: ['Q1', 'Q2', 'Q3', 'Q4'].map(quarter => 
            quarterCounts[quarter].diseases[disease] || 0
          )
        };
      });

      // 创建月份系列数据
      const monthSeries = uniqueDiseases.map(disease => {
        return {
          name: disease,
          type: 'bar',
          stack: 'total',
          data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'].map(month => 
            monthCounts[month].diseases[disease] || 0
          )
        };
      });

      // 创建过滤后的季节分布数据
      this.filteredSeasonalData = {
        xAxisQuarter: ['第一季度', '第二季度', '第三季度', '第四季度'],
        xAxisMonth: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
        seriesQuarter: quarterSeries,
        seriesMonth: monthSeries,
        totalPatients: patientsWithDateCount,
        selectedPatients: selectedPatients.length,
        selectedDiseases: uniqueDiseases,
        diseaseCount: diseaseCount,
        isFiltered: true,
        allDiseases: uniqueDiseases
      };

      console.log('✅ 已生成选中患者的真实季节分布数据');
    },

    // 根据诊断过滤季节数据
    filterSeasonalDataByDiagnoses(diagnoses) {
      if (!this.realSeasonalData || !diagnoses || diagnoses.length === 0) {
        console.log('过滤条件不满足，返回null');
        return null;
      }

      console.log('开始过滤季节数据...');
      console.log('输入诊断列表:', diagnoses);
      console.log('季节数据系列:', this.realSeasonalData.series?.map(s => s.name));

      // 创建诊断集合用于快速查找
      const diagnosisSet = new Set(diagnoses);
      console.log('诊断集合:', Array.from(diagnosisSet));

      // 过滤季节数据 - 使用智能匹配
      const matchedSeries = this.realSeasonalData.series.filter(series => {
        // 精确匹配
        let isMatch = diagnosisSet.has(series.name);

        // 如果精确匹配失败，尝试模糊匹配
        if (!isMatch) {
          for (const diagnosis of diagnoses) {
            // 检查是否包含关系
            if (series.name.includes(diagnosis) || diagnosis.includes(series.name)) {
              isMatch = true;
              console.log(`模糊匹配成功: "${series.name}" 匹配 "${diagnosis}"`);
              break;
            }

            // 检查去除空格和特殊字符后的匹配
            const normalizedSeries = series.name.replace(/[\s\-_\.]/g, '').toLowerCase();
            const normalizedDiagnosis = diagnosis.replace(/[\s\-_\.]/g, '').toLowerCase();
            if (normalizedSeries === normalizedDiagnosis) {
              isMatch = true;
              console.log(`标准化匹配成功: "${series.name}" 匹配 "${diagnosis}"`);
              break;
            }
          }
        }

        console.log(`系列 "${series.name}" 是否匹配: ${isMatch}`);
        return isMatch;
      });

      console.log(`匹配到 ${matchedSeries.length} 个系列:`, matchedSeries.map(s => s.name));

      // 如果没有匹配的系列，创建一个占位符显示
      if (matchedSeries.length === 0) {
        console.log('⚠️ 没有匹配的系列，创建占位符数据');

        // 创建基于选中患者数量的占位符数据
        const patientCount = diagnoses.length;
        const placeholderSeries = [{
          name: `Selected Patients (${patientCount})`,
          type: 'bar',
          stack: 'total',
          data: [
            Math.floor(patientCount * 0.2), // Q1
            Math.floor(patientCount * 0.3), // Q2
            Math.floor(patientCount * 0.3), // Q3
            Math.floor(patientCount * 0.2)  // Q4
          ]
        }];

        const filteredData = {
          ...this.realSeasonalData,
          series: placeholderSeries
        };

        console.log('创建的占位符数据:', filteredData);
        return filteredData;
      }

      const filteredData = {
        ...this.realSeasonalData,
        series: matchedSeries
      };

      console.log('过滤后的数据:', filteredData);
      return filteredData;
    },

    // 生成真实季节分布图表选项
    generateRealSeasonalOption(seasonalData) {
      const isQuarterView = this.seasonalViewMode === "quarter";

      // 聚类一致的颜色方案
      const clusteringColors = [
        '#4682B4', '#5F9EA0', '#6495ED', '#00BFFF', '#1E90FF',
        '#87CEEB', '#87CEFA', '#ADD8E6', '#B0E0E6', '#00CED1',
        '#48D1CC', '#40E0D0', '#7FFFD4', '#66CDAA', '#20B2AA'
      ];

      // 根据视图模式调整数据
      let xAxisData, seriesData;

      if (isQuarterView) {
        // 确保季度标签为大写
        xAxisData = seasonalData.xAxisQuarter || ['Q1', 'Q2', 'Q3', 'Q4'];
        seriesData = seasonalData.seriesQuarter || [];
      } else {
        // 月度视图使用英文缩写
        xAxisData = seasonalData.xAxisMonth || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        seriesData = seasonalData.seriesMonth || [];
      }

      return {
        title: {
          text: seasonalData.isFiltered
            ? `Seasonal Distribution (${seasonalData.selectedPatients} patients selected)`
            : 'Seasonal Distribution (Complete Data)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        // 添加副标题显示疾病种类信息
        subtitle: seasonalData.isFiltered && seasonalData.selectedDiseases ? {
          text: `Disease Types: ${seasonalData.selectedDiseases.length} (${seasonalData.selectedDiseases.slice(0, 3).join(', ')}${seasonalData.selectedDiseases.length > 3 ? '...' : ''})`,
          left: 'center',
          top: 25,
          textStyle: {
            fontSize: 12,
            color: '#666',
            fontStyle: 'italic'
          }
        } : undefined,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = `<strong>${params[0].axisValue}</strong><br/>`;
            params.forEach(param => {
              result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
            });
            return result;
          }
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: seasonalData.isFiltered && seasonalData.selectedDiseases ? 45 : 30,
          itemWidth: 12,
          itemHeight: 8,
          textStyle: {
            fontSize: 10,
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: seasonalData.isFiltered && seasonalData.selectedDiseases ? '25%' : '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            fontSize: 12,
            rotate: isQuarterView ? 0 : 45,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: seriesData.map((series, index) => ({
          ...series,
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: clusteringColors[index % clusteringColors.length],
            borderRadius: [2, 2, 0, 0]
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }))
      };
    },

    // AI诊断相关方法
    openAIDiagnosisModal() {
      this.showAIDiagnosisModal = true;
    },

    closeAIDiagnosisModal() {
      this.showAIDiagnosisModal = false;
    },

    async startAIDiagnosis() {
      if (!this.selectedPatient1) {
        this.diagnosisError = "请先选择一位患者";
        return;
      }

      this.diagnosisLoading = true;
      this.diagnosisError = null;

      try {
        const response = await fetch("http://127.0.0.1:5000/api/ai_diagnosis", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            patient_id: this.selectedPatient1,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("收到的诊断数据:", data);

        if (data.error) {
          throw new Error(data.error);
        }

        this.diagnosisResult = data;
        this.activeTab = "analysis";

        // 调试输出
        console.log("诊断分析:", data.diagnosis_analysis);
        console.log("诊断结果:", data.diagnosis_result);
        console.log("就诊建议:", data.visit_recommendation);

        // 清空聊天记录，准备新的诊断会话
        this.chatMessages = [];
      } catch (error: any) {
        console.error("AI诊断分析失败:", error);
        this.diagnosisError = `诊断分析失败: ${error.message}`;
      } finally {
        this.diagnosisLoading = false;
      }
    },

    // 切换诊断标签页
    switchTab(tabName: string) {
      this.activeTab = tabName;

      // 如果是聊天标签页，滚动到最新消息并添加欢迎消息
      if (tabName === "chat" && this.chatMessages.length === 0) {
        this.$nextTick(() => {
          this.chatMessages = [
            {
              type: "ai",
              text: "您好，我是AI医疗助手。我可以回答您关于眼科疾病的问题，特别是有关黄斑水肿、青光眼等眼科疾病的诊断、治疗和预防方面的问题。请问有什么我可以帮您的吗？",
              time: new Date().toLocaleTimeString(),
            },
          ];
          this.scrollToBottom();
        });
      }
    },

    // 发送聊天消息
    async sendChatMessage() {
      if (!this.chatMessage.trim() || this.chatLoading) return;

      // 添加用户消息
      this.chatMessages.push({
        type: "user",
        text: this.chatMessage.trim(),
        time: new Date().toLocaleTimeString(),
      });

      // 清空输入框
      const message = this.chatMessage.trim();
      this.chatMessage = "";

      // 设置加载状态
      this.chatLoading = true;

      // 滚动到最新消息
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      try {
        const response = await fetch(
          "http://127.0.0.1:5000/api/ai_chat_deepseek",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              patient_id: this.selectedPatient1,
              question: message,
            }),
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        this.chatMessages.push({
          type: "ai",
          text: data.answer || "抱歉，我无法回答这个问题。",
          time: new Date().toLocaleTimeString(),
        });
      } catch (error) {
        console.error("AI聊天请求失败:", error);

        this.chatMessages.push({
          type: "ai",
          text: `抱歉，请求失败: ${error.message}`,
          time: new Date().toLocaleTimeString(),
        });
      } finally {
        this.chatLoading = false;

        // 滚动到最新消息
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 滚动聊天窗口到底部
    scrollToBottom() {
      const chatMessages = this.$refs.chatMessages;
      if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    },
    
    // 切换到Apriori模式
    switchToAprioriMode() {
      console.log('切换到Apriori模式');
      this.correlationMode = 'apriori';
      
      // 设置一个加载中的临时图表选项
      if (!this.aprioriCorrelationOption) {
        this.aprioriCorrelationOption = {
          title: {
            text: 'Disease Correlation (Loading...)',
            left: 'center',
            textStyle: {
              fontSize: 16,
              color: '#333'
            }
          },
          tooltip: {
            position: 'top',
            formatter: (params) => {
              return '加载中...';
            }
          },
          grid: {
            top: 60,
            bottom: 30,
            left: 50,
            right: 10
          }
        };
      }
      
      // 触发Apriori数据加载
      this.fetchAprioriCorrelationData();
    },

    // 格式化文本（处理换行等）
    formatText(text) {
      if (!text) return "";

      // 如果是对象，需要将其转换为格式化的HTML
      if (typeof text === 'object') {
        let html = '';
        for (const [key, value] of Object.entries(text)) {
          if (value) {
            html += `<div class="diagnosis-section">`;
            html += `<h5 class="section-title"><span class="title-highlight">${key}</span>：</h5>`;
            html += `<div class="section-content">${this.highlightKeywords(value.replace(/\n/g, "<br>"))}</div>`;
            html += `</div>`;
          }
        }
        return html;
      }

      // 如果是字符串，处理换行并高亮关键词
      return this.highlightKeywords(text.replace(/\n/g, "<br>"));
    },

    // 高亮关键词
    highlightKeywords(text) {
      if (!text) return "";

      // 定义医学关键词模式
      const medicalKeywords = [
        // 疾病名称
        '糖尿病', '高血压', '冠心病', '心律失常', '心肌梗死', '脑梗死', '脑出血',
        '肺炎', '哮喘', '慢阻肺', '肺结核', '肝炎', '肝硬化', '胃炎', '胃溃疡',
        '肾炎', '肾衰竭', '白内障', '青光眼', '黄斑水肿', '视网膜病变', '角膜炎',
        '结膜炎', '近视', '远视', '散光', '弱视', '斜视', '眼底出血', '玻璃体混浊',

        // 症状描述
        '头痛', '头晕', '胸痛', '胸闷', '心悸', '气短', '呼吸困难', '咳嗽', '咳痰',
        '发热', '乏力', '恶心', '呕吐', '腹痛', '腹泻', '便秘', '水肿', '尿频',
        '尿急', '尿痛', '血尿', '蛋白尿', '视力下降', '视物模糊', '眼痛', '眼红',
        '眼干', '流泪', '畏光', '复视', '视野缺损', '夜盲', '色觉异常',

        // 检查项目
        '血常规', '尿常规', '肝功能', '肾功能', '血糖', '血脂', '心电图', 'X线',
        'CT', 'MRI', 'B超', '眼底检查', '视力检查', '眼压检查', '裂隙灯检查',
        '眼底造影', 'OCT', '视野检查', '角膜地形图', '泪液分泌试验',

        // 治疗方法
        '手术', '药物治疗', '物理治疗', '康复训练', '激光治疗', '注射治疗',
        '白内障手术', '青光眼手术', '视网膜手术', '玻璃体切除术', '角膜移植',
        '准分子激光', '飞秒激光', '眼内注射', '光动力疗法',

        // 药物名称
        '胰岛素', '二甲双胍', '阿司匹林', '他汀类', 'ACEI', 'ARB', '利尿剂',
        'β受体阻滞剂', '钙通道阻滞剂', '滴眼液', '眼膏', '人工泪液', '散瞳剂',
        '缩瞳剂', '降眼压药', '抗生素', '激素', '非甾体抗炎药',

        // 数值和单位
        'mmHg', 'mg/dl', 'mmol/L', '%', '岁', '年', '月', '天', '小时', '分钟',
        '正常', '异常', '偏高', '偏低', '阴性', '阳性', '轻度', '中度', '重度',

        // 解剖部位
        '心脏', '肺部', '肝脏', '肾脏', '胃', '肠道', '眼球', '角膜', '晶状体',
        '玻璃体', '视网膜', '黄斑', '视神经', '眼压', '眼底', '前房', '后房',
        '睫状体', '虹膜', '巩膜', '结膜', '泪腺', '眼睑'
      ];

      // 数字模式（包括小数、百分比、范围等）
      const numberPatterns = [
        /\d+\.?\d*\s*(?:mmHg|mg\/dl|mmol\/L|%|岁|年|月|天|小时|分钟)/g,
        /\d+\.?\d*\s*[-~]\s*\d+\.?\d*/g,  // 范围值
        /\d+\.?\d*/g  // 普通数字
      ];

      // 特殊标记模式（如 **文字**）
      const boldPattern = /\*\*([^*]+)\*\*/g;

      let formattedText = text;

      // 1. 处理加粗标记
      formattedText = formattedText.replace(boldPattern, '<span class="text-bold">$1</span>');

      // 2. 高亮医学关键词
      medicalKeywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        formattedText = formattedText.replace(regex, '<span class="keyword-highlight">$1</span>');
      });

      // 3. 高亮数字和数值
      numberPatterns.forEach(pattern => {
        formattedText = formattedText.replace(pattern, (match) => {
          return `<span class="number-highlight">${match}</span>`;
        });
      });

      // 4. 高亮异常值标识
      const statusPatterns = {
        normal: ['正常', '阴性'],
        abnormal: ['异常', '偏高', '偏低', '阳性'],
        mild: ['轻度'],
        moderate: ['中度'],
        severe: ['重度']
      };

      Object.entries(statusPatterns).forEach(([statusClass, patterns]) => {
        patterns.forEach(pattern => {
          const regex = new RegExp(`(${pattern})`, 'gi');
          formattedText = formattedText.replace(regex, `<span class="status-highlight status-${statusClass}">$1</span>`);
        });
      });

      return formattedText;
    },

    // 统一的tooltip位置计算函数 - 优先向左显示，确保在最上层
    calculateTooltipPosition(pos, params, dom, rect, size) {
      console.log("Calculating tooltip position for:", pos);
      console.log("DOM dimensions:", { width: dom.offsetWidth, height: dom.offsetHeight });

      const viewHeight = window.innerHeight || document.documentElement.clientHeight;
      const viewWidth = window.innerWidth || document.documentElement.clientWidth;
      const domHeight = dom.offsetHeight || 120; // 增加默认高度
      const domWidth = dom.offsetWidth || 250; // 增加默认宽度

      // 获取页面滚动位置
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || 0;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft || 0;

      // 计算相对于页面的绝对位置
      let posX = pos[0] + scrollLeft;
      let posY = pos[1] + scrollTop;

      // 优先向左显示tooltip
      posX = posX - domWidth - 15; // 向左偏移
      posY = posY - 10; // 默认位置上移10px

      // 如果左侧空间不够，则向右显示
      if (posX < scrollLeft + 10) {
        posX = pos[0] + scrollLeft + 15; // 向右偏移

        // 如果右侧也不够，则尽量靠右显示
        if (posX + domWidth + 20 > scrollLeft + viewWidth) {
          posX = scrollLeft + viewWidth - domWidth - 20;
        }
      }

      // 垂直位置调整
      if (posY + domHeight + 20 > scrollTop + viewHeight) {
        posY = pos[1] + scrollTop - domHeight - 20; // 显示在鼠标上方
      }

      // 确保不超出顶部
      if (posY < scrollTop + 10) {
        posY = scrollTop + 10;
      }

      // 确保不超出左侧
      if (posX < scrollLeft + 10) {
        posX = scrollLeft + 10;
      }

      // 转换回相对位置（因为使用了position: fixed）
      const finalPosX = posX - scrollLeft;
      const finalPosY = posY - scrollTop;

      console.log("Final tooltip position:", [finalPosX, finalPosY]);
      return [finalPosX, finalPosY];
    },

    // 生成正常值范围热力图
    generateNormalRangeHeatmap(patientData) {
      if (!patientData || patientData.length === 0) {
        return {
          title: {
            text: "正常参考值",
            textStyle: { fontSize: 12, color: "#1976d2" },
            left: "center",
            top: 5,
          },
          grid: { top: 30, right: 20, bottom: 60, left: 20 },
          xAxis: { show: false },
          yAxis: { show: false },
          series: [],
        };
      }

      // 从患者数据中提取检测项目数据
      const normalRangeData = patientData.map(item => {
        return {
          name: item.itemCName || '未知项目',
          value: 10, // 统一柱高
          reference: item.ItemRange,
          unit: item.Itemunit || '',
          device: item.instrument || '',
          method: item.method || '',
          itemStyle: { color: "#e3f2fd" } // 统一使用淡蓝色
        };
      });
      
      return {
        title: {
          text: "正常参考值",
          textStyle: { fontSize: 12, color: "#1976d2", fontWeight: "bold" },
          left: "center",
          top: 5,
        },
        tooltip: {
          trigger: "item",
          show: true,
          confine: false, // 允许提示框超出图表区域显示
          enterable: true, // 鼠标可进入提示框
          appendToBody: true, // 将tooltip添加到body元素，避免被容器遮挡
          backgroundColor: "rgba(255, 255, 255, 0.98)",
          borderColor: "#bbdefb",
          borderWidth: 1,
          padding: [8, 12],
          textStyle: {
            color: "#333",
            fontSize: 12,
            lineHeight: 18
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15); z-index: 99999 !important; border-radius: 4px; position: fixed !important; pointer-events: auto !important;',
          formatter: (params) => {
            console.log("Left heatmap tooltip formatter called with params:", params);

            const data = params.data;
            console.log("Left heatmap tooltip data:", data);

            if (!data) {
              console.log("No data in left heatmap tooltip params");
              return "无数据";
            }

            try {
              let html = `<div style="min-width: 200px; max-width: 300px; line-height: 1.5;">`;

              // 检测名称
              html += `<div style="font-weight: bold; color: #1976d2; margin-bottom: 8px; font-size: 13px;">${data.name}</div>`;

              // 参考值范围
              html += `<div style="margin-bottom: 6px; font-size: 12px;">参考值范围: ${data.reference || '未提供'} ${data.unit}</div>`;

              // 检测仪器
              if (data.device) {
                html += `<div style="margin-bottom: 6px; font-size: 12px;">检测仪器: ${data.device}</div>`;
              }

              // 检测方法
              if (data.method) {
                html += `<div style="margin-bottom: 6px; font-size: 12px;">检测方法: ${data.method}</div>`;
              }

              html += `</div>`;

              console.log("Generated left heatmap tooltip HTML:", html);
              return html;

            } catch (e) {
              console.error("Error formatting left heatmap tooltip:", e, data);
              return `
                <div style="padding: 8px; min-width: 180px;">
                  <div style="font-weight: bold; color: #1976d2; margin-bottom: 5px;">${data.name || '未知'}</div>
                  <div style="margin-bottom: 5px;">参考值: ${data.reference || '未提供'}</div>
                </div>
              `;
            }
          },
          position: this.calculateTooltipPosition
        },
        grid: {
          top: 20,
          right: 20,
          bottom: 0,
          left: 20,
          containLabel: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            start: 0,
            end: 35,  // 默认显示前50%的数据
            height: 20,
            bottom: 10,
            borderColor: '#bbdefb',
            textStyle: {
              color: '#1976d2'
            },
            handleSize: 20,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleStyle: {
              color: '#1976d2',
              borderColor: '#1976d2'
            },
            dataBackground: {
              lineStyle: {
                color: '#bbdefb'
              },
              areaStyle: {
                color: '#e3f2fd'
              }
            },
            fillerColor: 'rgba(25, 118, 210, 0.1)',
            emphasis: {
              handleStyle: {
                color: '#2196f3',
                borderColor: '#2196f3'
              }
            }
          }
        ],
        xAxis: {
          type: "category",
          data: normalRangeData.map(item => item.name),
          axisLabel: {
            rotate: 45,
            fontSize: 10,
            color: "#666",
            interval: 0,
          },
          axisLine: {
            lineStyle: { color: "#bbdefb" },
          },
        },
        yAxis: {
          type: "value",
          show: false,
          min: 0,
          max: 12,
        },
        series: [
          {
            type: "bar",
            data: normalRangeData,
            barWidth: "80%",
            animation: true,
            animationDuration: 800,
            animationEasing: "cubicOut",
            itemStyle: {
              borderRadius: [3, 3, 0, 0] // 添加圆角效果
            },
            label: {
              
              position: "top",
              formatter: function(params) {
                return params.name.length > 5 ? params.name.substring(0, 5) + "..." : params.name;
              },
              fontSize: 10,
              color: "#1976d2",
              rotate: 90,
              align: "left"
            }
          }
        ],
      };
    },

    // 获取基于Apriori算法的疾病关联数据（主要诊断→次要诊断）
    async fetchAprioriCorrelationData() {
      try {
        // 如果已经有数据且处于Apriori模式，不重复加载
        if (this.aprioriCorrelationOption && this.correlationMode === 'apriori') {
          console.log('✅ 已有Apriori关联分析数据，无需重新加载');
          return;
        }
        
        this.isLoadingCorrelation = true;
        console.log('🔄 获取基于Apriori算法的疾病关联数据...');
        
        // 设置Apriori参数
        const options = {
          minSupport: 0.005,  // 更宽松的参数以获得更多关联规则
          minConfidence: 0.05,
          minLift: 0.8,
          topDiseases: 15
        };
        
        console.log('📊 Apriori参数:', options);
        
        // 导入API
        const { ClusteringAPI } = await import('@/api/clustering');
        console.log('✅ API导入成功');
        
        // 调用API获取数据
        console.log('📡 开始调用API: getDiseaseCorrelationApriori');
        const correlationData = await ClusteringAPI.getDiseaseCorrelationApriori(options);
        console.log('✅ API调用完成，返回数据:', correlationData);
        
        if (correlationData) {
          console.log('✅ 获取Apriori疾病关联数据成功');
          console.log('📋 数据结构:', JSON.stringify(correlationData).slice(0, 200) + '...');
          this.updateAprioriCorrelationChart(correlationData);
          
          // 显式设置为Apriori模式
          this.correlationMode = 'apriori';
        } else {
          console.error('❌ 获取Apriori疾病关联数据失败：返回空数据');
        }
      } catch (error) {
        console.error('❌ 获取Apriori疾病关联数据出错:', error);
        console.error('❌ 错误详情:', error.message);
        if (error.response) {
          console.error('❌ 响应状态:', error.response.status);
          console.error('❌ 响应数据:', error.response.data);
        }
      } finally {
        this.isLoadingCorrelation = false;
      }
    },
    
    // 更新Apriori关联图表为晴雨图
    updateAprioriCorrelationChart(correlationData) {
      console.log('开始更新Apriori关联晴雨图...');
      console.log('correlationData:', correlationData);
      
      // 检查数据是否有效
      if (!correlationData || !correlationData.xAxis || !correlationData.yAxis || !correlationData.heatmap_data) {
        console.error('无效的相关性数据:', correlationData);
        return;
      }
      
      // 获取数据
      const xAxisData = correlationData.xAxis.data;
      const yAxisData = correlationData.yAxis.data;
      let heatmapData = correlationData.heatmap_data;
      const xFullData = correlationData.xAxis.full_data;
      const yFullData = correlationData.yAxis.full_data;

      // 数据预处理：获取非零值以确定范围
      const nonZeroValues = heatmapData.filter(d => d[2] > 0).map(d => d[2]);
      const maxValue = nonZeroValues.length > 0 ? Math.max(...nonZeroValues) : 1;
      const minValue = nonZeroValues.length > 0 ? Math.min(...nonZeroValues) : 0;

      // 转换为气泡图数据
      const bubbleData = [];
      
      // 将热力图数据转换为气泡图数据
      heatmapData.forEach(item => {
        const [xIndex, yIndex, value] = item;
        
        // 只显示有关联的数据点
        if (value > 0) {
          const xName = xAxisData[xIndex];
          const yName = yAxisData[yIndex];
          const fullXName = xFullData[xIndex];
          const fullYName = yFullData[yIndex];
          
          // 计算气泡大小，最小为10，最大为50
          const symbolSize = Math.max(12, Math.min(60, 12 + value / maxValue * 48));
          
          // 确定气泡颜色 - 基于值的强度分级
          let itemColor;
          if (value > maxValue * 0.7) {
            itemColor = '#1565C0'; // 深蓝 - 强关联
          } else if (value > maxValue * 0.4) {
            itemColor = '#1976D2'; // 中蓝 - 中度关联
          } else if (value > maxValue * 0.2) {
            itemColor = '#42A5F5'; // 浅蓝 - 弱关联
          } else {
            itemColor = '#90CAF9'; // 极浅蓝 - 极弱关联
          }
          
          bubbleData.push({
            name: `${fullYName} → ${fullXName}`,
            value: [xIndex, yIndex, value],
            symbolSize: symbolSize,
            itemStyle: {
              color: itemColor,
              borderColor: '#fff',
              borderWidth: 1,
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            emphasis: {
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          });
        }
      });

      console.log(`转换后的晴雨图数据: ${bubbleData.length} 个气泡`);
      
      // 创建晴雨图配置
      this.aprioriCorrelationOption = {
        title: {
          text: '疾病关联晴雨图 (主要诊断 → 次要诊断)',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const xIndex = params.value[0];
            const yIndex = params.value[1];
            const correlation = params.value[2];
            
            const mainDiagnosis = yFullData[yIndex];
            const secondaryDiagnosis = xFullData[xIndex];

            // 关联强度描述
            let strengthDesc = '';
            if (correlation > maxValue * 0.7) {
              strengthDesc = '<span style="color:#1565C0;font-weight:bold">强关联</span>';
            } else if (correlation > maxValue * 0.4) {
              strengthDesc = '<span style="color:#1976D2;font-weight:bold">中度关联</span>';
            } else if (correlation > maxValue * 0.2) {
              strengthDesc = '<span style="color:#42A5F5">弱关联</span>';
            } else {
              strengthDesc = '<span style="color:#90CAF9">极弱关联</span>';
            }

            return `<div style="padding: 10px 14px; min-width: 220px;">
              <div style="font-weight: bold; margin-bottom: 8px; font-size: 14px;">
                ${mainDiagnosis} → ${secondaryDiagnosis}
              </div>
              <div style="font-size: 13px; margin-top: 5px;">关联强度: <b>${correlation.toFixed(3)}</b></div>
              <div style="font-size: 13px; margin-top: 5px;">关联类型: ${strengthDesc}</div>
              <div style="font-size: 11px; color: #666; margin-top: 8px; border-top: 1px solid #eee; padding-top: 5px;">
                基于Apriori算法分析，表示患者同时出现这两种疾病的概率关联
              </div>
            </div>`;
          },
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: "#333",
            fontSize: 13
          },
          extraCssText: "box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;",
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        animation: true,
        grid: {
          left: "5%",
          right: "5%",
          bottom: "10%",
          top: "15%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: xAxisData,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          },
          axisLabel: {
            fontSize: 9,
            interval: 0,
            rotate: 45,
            color: '#666'
          }
        },
        yAxis: {
          type: "category",
          data: yAxisData,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#eee'
            }
          },
          axisLabel: {
            fontSize: 9,
            interval: 0,
            color: '#666'
          }
        },
        series: [{
            name: "Disease Correlation",
          type: "scatter",
          data: bubbleData,
          symbolSize: function(val) {
            return val[2] > 0 ? Math.max(10, Math.min(50, val[2] / maxValue * 50)) : 0;
          },
          animationDelay: function(idx) {
            return idx * 15;
              }
        }]
      };
      
      // 更新图表
      if (this.$refs.correlationChart) {
        const echarts = require('echarts');
        const chart = echarts.init(this.$refs.correlationChart);
        // 添加渐入动画效果
        this.aprioriCorrelationOption.animationDuration = 1000;
        this.aprioriCorrelationOption.animationEasing = 'cubicOut';
        chart.setOption(this.aprioriCorrelationOption);
        this.correlationChartInstance = chart;
        
        // 注册图表点击事件
        chart.on('click', (params) => {
          if (params.componentType === 'series') {
            const xIndex = params.value[0];
            const yIndex = params.value[1];
            const xName = xFullData[xIndex];
            const yName = yFullData[yIndex];
            console.log(`用户点击了关联: ${yName} → ${xName}, 关联强度: ${params.value[2]}`);
            // 这里可以添加点击后的操作，例如筛选相关患者等
          }
        });
      }
    },

    // 获取患者ID列表
    async fetchPatientIds() {
      try {
        console.log("正在获取患者ID列表...");
        const response = await fetch("http://127.0.0.1:5000/api/patient_ids");

        if (!response.ok) {
          throw new Error(`HTTP错误! 状态: ${response.status}`);
        }

        const data = await response.json();
        console.log(`获取到${data.length}个患者ID`);
        this.realPatientIds = data;

        // 设置默认选中的患者
        if (data.length >= 1) {
          this.selectedPatient1 = data[0];
          console.log(`默认选择患者ID: ${this.selectedPatient1}`);
          // 获取患者数据
          await this.fetchPatientComparisonData();
        } else {
          console.warn("没有获取到任何患者ID");
        }
      } catch (error) {
        console.error("获取患者ID失败:", error);
      }
    },

    // 获取患者数据
    async fetchPatientComparisonData() {
      if (!this.selectedPatient1) {
        return;
      }

      try {
        const response = await fetch(
          "http://127.0.0.1:5000/api/clinical_comparison",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              patient_ids: [this.selectedPatient1], // 只发送一个患者ID
            }),
          }
        );

        const data = await response.json();
        if (data.status === "success") {
          this.patientComparisonData.patient1 = data.patient1;
          // 清空患者2的数据，因为现在只处理单个患者
          this.patientComparisonData.patient2 = [];

          // 更新热力图
          this.updateHeatmaps();

          // 同时获取患者概况和关键词
          await this.fetchPatientSummary();
          await this.fetchPatientKeywords();
        } else {
          console.error("获取患者数据失败:", data.msg);
        }
      } catch (error) {
        console.error("获取患者数据失败:", error);
      }
    },

    // 更新热力图
    updateHeatmaps() {
      // 更新患者数据热力图
      this.heatmapOption1 = this.generateHeatmapOption(
        this.patientComparisonData.patient1,
        "患者检查项目"
      );

      // 生成正常值范围热力图
      this.normalRangeHeatmapOption = this.generateNormalRangeHeatmap(
        this.patientComparisonData.patient1
      );
    },

    // 获取患者病情概况总结
    async fetchPatientSummary() {
      if (!this.selectedPatient1) {
        return;
      }

      this.summaryLoading = true;
      try {
        const response = await fetch(
          `http://127.0.0.1:5000/api/patient_summary?medical_record_number=${this.selectedPatient1}`
        );

        const data = await response.json();
        if (data.status === "success") {
          this.patientSummary = data.summary;
        } else {
          console.error("获取患者概况失败:", data.error);
          this.patientSummary = "获取患者概况失败";
        }
      } catch (error) {
        console.error("获取患者概况失败:", error);
        this.patientSummary = "获取患者概况失败";
      } finally {
        this.summaryLoading = false;
      }
    },

    // 获取患者关键词并生成词云图
    async fetchPatientKeywords() {
      if (!this.selectedPatient1) {
        return;
      }

      this.keywordsLoading = true;
      try {
        console.log("正在获取患者关键词，患者ID:", this.selectedPatient1);
        const response = await fetch(
          `http://127.0.0.1:5000/api/patient_keywords?medical_record_number=${this.selectedPatient1}`
        );

        const data = await response.json();
        console.log("获取到的关键词数据:", data);

        if (data.status === "success") {
          this.patientKeywords = data.keywords;
          console.log("设置关键词数据:", this.patientKeywords);

          // 确保在DOM更新后生成词云图
          this.$nextTick(() => {
            this.generateKeywordsChart();
          });
        } else {
          console.error("获取患者关键词失败:", data.error);
          // 使用默认关键词
          this.renderDefaultKeywordsChart();
        }
      } catch (error) {
        console.error("获取患者关键词失败:", error);
        // 使用默认关键词
        this.renderDefaultKeywordsChart();
      } finally {
        this.keywordsLoading = false;
      }
    },

    // 生成关键词云图
    generateKeywordsChart() {
      if (!this.patientKeywords || this.patientKeywords.length === 0) {
        console.log("没有关键词数据，使用默认关键词");
        // 如果没有关键词数据，使用默认关键词
        this.renderDefaultKeywordsChart();
        return;
      }

      // 转换关键词数据为ECharts词云格式
      const keywordsData = [];

      // 获取关键词对象，确保解除Proxy
      const keywords = JSON.parse(JSON.stringify(this.patientKeywords[0] || {}));
      console.log("关键词数据(解除Proxy):", keywords);

      // 检查关键词数据的格式
      if (Object.keys(keywords).length === 0) {
        console.log("关键词数据为空对象，使用默认关键词");
        this.renderDefaultKeywordsChart();
        return;
      }

      Object.values(keywords).forEach((keyword, index) => {
        if (keyword && typeof keyword === 'string') {
          keywordsData.push({
            name: keyword,
            value: Math.max(100 - index * 8, 20), // 根据顺序设置权重
          });
        }
      });

      console.log("转换后的关键词数据:", keywordsData);

      if (keywordsData.length === 0) {
        console.log("转换后的关键词数据为空，使用默认关键词");
        this.renderDefaultKeywordsChart();
        return;
      }

      const option = {
        tooltip: {
          show: true,
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <strong>${params.name}</strong><br/>
              权重: ${params.value}
            </div>`;
          }
        },
        series: [{
          type: 'wordCloud',
          gridSize: 8,
          sizeRange: [12, 50],
          rotationRange: [-45, 45],
          rotationStep: 45,
          shape: 'circle',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              const colors = ['#1976d2', '#388e3c', '#f57c00', '#d32f2f', '#7b1fa2', '#0288d1', '#689f38', '#fbc02d'];
              return colors[Math.floor(Math.random() * colors.length)];
            }
          },
          emphasis: {
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333'
            }
          },
          data: keywordsData
        }]
      };

      // 渲染词云图
      this.$nextTick(() => {
        if (this.$refs.keywordsChart) {
          const chart = echarts.init(this.$refs.keywordsChart);
          chart.setOption(option);

          // 响应式调整
          window.addEventListener('resize', () => {
            chart.resize();
          });
        }
      });
    },

    // 渲染默认关键词云图
    renderDefaultKeywordsChart() {
      const defaultKeywords = [
        { name: "眼科疾病", value: 100 },
        { name: "视网膜病变", value: 90 },
        { name: "黄斑水肿", value: 80 },
        { name: "糖尿病", value: 70 },
        { name: "高血压", value: 60 },
        { name: "视力下降", value: 50 },
        { name: "眼底检查", value: 40 },
        { name: "治疗方案", value: 30 }
      ];

      const option = {
        tooltip: {
          show: true,
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <strong>${params.name}</strong><br/>
              权重: ${params.value}
            </div>`;
          }
        },
        series: [{
          type: 'wordCloud',
          gridSize: 8,
          sizeRange: [12, 50],
          rotationRange: [-45, 45],
          rotationStep: 45,
          shape: 'circle',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              const colors = ['#1976d2', '#388e3c', '#f57c00', '#d32f2f', '#7b1fa2', '#0288d1', '#689f38', '#fbc02d'];
              return colors[Math.floor(Math.random() * colors.length)];
            }
          },
          emphasis: {
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333'
            }
          },
          data: defaultKeywords
        }]
      };

      // 渲染词云图
      this.$nextTick(() => {
        if (this.$refs.keywordsChart) {
          const chart = echarts.init(this.$refs.keywordsChart);
          chart.setOption(option);

          // 响应式调整
          window.addEventListener('resize', () => {
            chart.resize();
          });
        }
      });
    },

    // 生成热力图配置（横向柱状图形式）
    generateHeatmapOption(patientData, title) {
      console.log("generateHeatmapOption called with:", patientData, title); // 调试输出

      if (!patientData || patientData.length === 0) {
        console.log("No patient data available"); // 调试输出
        return {
          title: {
            text: title,
            textStyle: { fontSize: 12, color: "#666" },
            left: "center",
            top: 5,
          },
          grid: { top: 40, right: 20, bottom: 60, left: 20 },
          xAxis: { show: false },
          yAxis: { show: false },
          series: [],
        };
      }

      // 处理患者检测项目数据
      console.log("Processing patient data:", patientData); // 调试输出
      const barData = patientData.map((item, index) => {
        console.log(`Processing item ${index}:`, item); // 调试每个项目的数据

        // 从项目中获取颜色类型和强度
        const colorType = item.colorType || "normal";
        const colorIntensity = item.colorIntensity || 0;
        let color = "#e3f2fd"; // 默认淡蓝色（正常范围内）

        // 根据颜色类型和强度设置颜色
        if (colorType === "high") {
          // 高于范围值：从淡蓝到深蓝
          const intensity = Math.min(1, colorIntensity);
          color = intensity > 0.7 ? "#0d47a1" : intensity > 0.4 ? "#1976d2" : "#2196f3";
        } else if (colorType === "low") {
          // 低于范围值：从淡绿到深绿
          const intensity = Math.min(1, colorIntensity);
          color = intensity > 0.7 ? "#1b5e20" : intensity > 0.4 ? "#2e7d32" : "#4caf50";
        }

        // 构建tooltip信息，检查所有变量是否存在
        let tooltipInfo = "";
        try {
          if (colorType === "high") {
            tooltipInfo = `高于检测范围:${item.ItemValue} ${item.Itemunit || ''} > ${item.ItemRange ? item.ItemRange.split('-')[1] : ''} ${item.Itemunit || ''}`;
          } else if (colorType === "low") {
            tooltipInfo = `低于检测范围:${item.ItemValue} ${item.Itemunit || ''} < ${item.ItemRange ? item.ItemRange.split('-')[0] : ''} ${item.Itemunit || ''}`;
          } else {
            // 正常范围内
            if (item.ItemRange) {
              const [min, max] = item.ItemRange.split('-');
              tooltipInfo = `介于检测范围:${min} ${item.Itemunit || ''} < ${item.ItemValue} ${item.Itemunit || ''} < ${max} ${item.Itemunit || ''}`;
            } else {
              tooltipInfo = `检测值:${item.ItemValue} ${item.Itemunit || ''}`;
            }
          }
        } catch (e) {
          console.error("Error creating tooltip info:", e, item);
          tooltipInfo = `检测值:${item.ItemValue} ${item.Itemunit || ''}`;
        }

        // 创建最终对象 - 确保所有必要的字段都存在
        const result = {
          name: item.itemCName || "未知项目",
          value: 10, // 统一柱状图高度
          // 保留原始数据用于tooltip
          itemCName: item.itemCName,
          ItemValue: item.ItemValue,
          Itemunit: item.Itemunit,
          ItemRange: item.ItemRange,
          instrument: item.instrument,
          method: item.method,
          colorType: colorType,
          tooltipInfo: tooltipInfo,
          itemStyle: {
            color: color
          },
        };

        console.log(`Created bar item ${index}:`, result); // 调试创建的柱状图项
        return result;
      });

      console.log("Final barData for chart:", barData); // 调试最终数据

      return {
        title: {
          text: title || "患者检测项目数值",
          textStyle: { fontSize: 12, color: "#1976d2", fontWeight: "bold" },
          left: "center",
          top: 5,
        },
        tooltip: {
          trigger: "item",
          show: true, // 确保tooltip显示
          confine: false, // 允许提示框超出图表区域显示
          enterable: true, // 鼠标可进入提示框
          appendToBody: true, // 将tooltip添加到body元素，避免被容器遮挡
          backgroundColor: "rgba(255, 255, 255, 0.98)",
          borderColor: "#bbdefb",
          borderWidth: 1,
          padding: [8, 12],
          textStyle: {
            color: "#333",
            fontSize: 12,
            lineHeight: 18
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15); z-index: 99999 !important; border-radius: 4px; position: fixed !important; pointer-events: auto !important;',
          formatter: (params) => {
            console.log("Tooltip formatter called with params:", params); // 调试输出

            const data = params.data;
            console.log("Tooltip data:", data); // 添加调试输出，查看数据结构

            if (!data) {
              console.log("No data in tooltip params");
              return "无数据";
            }

            try {
              // 构建完整的提示信息
              let html = `<div style="min-width: 200px; max-width: 300px; line-height: 1.5;">`;

              // 检测名称
              html += `<div style="font-weight: bold; color: #1976d2; margin-bottom: 8px; font-size: 13px;">检测名: ${data.itemCName || data.name || '未知项目'}</div>`;

              // 检测值
              html += `<div style="margin-bottom: 6px; font-size: 12px;">检测值: ${data.ItemValue !== undefined ? data.ItemValue : '无数据'} ${data.Itemunit || ''}</div>`;

              // 范围信息
              if (data.tooltipInfo) {
                html += `<div style="margin-bottom: 6px; font-size: 12px; color: #555;">${data.tooltipInfo}</div>`;
              }

              // 检测仪器
              if (data.instrument) {
                html += `<div style="margin-bottom: 6px; font-size: 12px;">检测仪器: ${data.instrument}</div>`;
              }

              // 检测方法
              if (data.method) {
                html += `<div style="margin-bottom: 6px; font-size: 12px;">检测方法: ${data.method}</div>`;
              }

              html += `</div>`;

              console.log("Generated tooltip HTML:", html);
              return html;

            } catch (e) {
              console.error("Error formatting tooltip:", e, data);
              // 提供备用格式，确保总是有内容显示
              return `
                <div style="padding: 8px; min-width: 180px;">
                  <div style="font-weight: bold; color: #1976d2; margin-bottom: 5px;">项目: ${data.name || data.itemCName || '未知'}</div>
                  <div style="margin-bottom: 5px;">数值: ${data.ItemValue !== undefined ? data.ItemValue : '无数据'}</div>
                </div>
              `;
            }
          },
          position: this.calculateTooltipPosition
        },
        grid: {
          top: 20,
          right: 20,
          bottom: 0,
          left: 20,
          containLabel: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            start: 0,
            end: 35,
            height: 20,
            bottom: 10,
            borderColor: '#bbdefb',
            textStyle: {
              color: '#1976d2'
            },
            handleSize: 20,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleStyle: {
              color: '#1976d2',
              borderColor: '#1976d2'
            },
            dataBackground: {
              lineStyle: {
                color: '#bbdefb'
              },
              areaStyle: {
                color: '#e3f2fd'
              }
            },
            fillerColor: 'rgba(25, 118, 210, 0.1)',
            emphasis: {
              handleStyle: {
                color: '#2196f3',
                borderColor: '#2196f3'
              }
            }
          }
        ],
        xAxis: {
          type: "category",
          data: barData.map((item) => item.name),
          axisLabel: {
            rotate: 45,
            fontSize: 10,
            color: "#666",
            interval: 0,
          },
          axisLine: {
            lineStyle: { color: "#bbdefb" },
          }
        },
        yAxis: {
          type: "value",
          show: false,
          min: 0,
          max: 12,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        },
        series: [
          {
            type: "bar",
            data: barData,
            barWidth: "80%",
            animation: true,
            animationDuration: 800,
            animationEasing: "cubicOut",
            itemStyle: {
              borderRadius: [3, 3, 0, 0] // 添加圆角效果
            },
            emphasis: {
              focus: 'self',
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(25, 118, 210, 0.3)'
              }
            },
            // 确保tooltip能正确触发
            tooltip: {
              show: true
            }
          },
        ],
      };
    },
  },
  async mounted() {
    // 首先检查后端连接状态
    await this.checkBackendConnection();

    // 只有在后端连接成功后才加载疾病数据、季节分布数据和疾病关联数据
    if (this.backendConnected) {
      await this.loadDiseases();
      await this.loadRealSeasonalData();
      // 默认加载Apriori关联分析数据
      await this.fetchAprioriCorrelationData();
      this.loadDiseaseCorrelationData();
      // 不要在挂载时加载Apriori数据，等用户切换到Apriori模式时再加载
      // this.fetchAprioriCorrelationData();
    }

    // 首先生成缺失的患者数据
    this.generateMissingPatientDetails();

    // 监听聊天消息变化，自动滚动到底部
    this.$watch(
      "chatMessages",
      () => {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },
      { deep: true }
    );

    // 初始化患者对比数据
    this.updatePatientComparison();

    // 在组件挂载后，初始化散点图的聚类视图
    this.$nextTick(() => {
      this.updateScatterChart();
    });

    // 监听图表点击事件
    this.$nextTick(() => {
      const charts = document.querySelectorAll(".chart");
      charts.forEach((chart) => {
        const instance = echarts.getInstanceByDom(chart as HTMLElement);
        if (instance) {
          instance.on("click", (params: any) => {
            if (
              params.data &&
              typeof params.data === "object" &&
              "patientId" in params.data
            ) {
              // 当点击患者节点时，选择该患者
              const patientId = params.data.patientId;
              const diseaseId = params.data.diseaseId;

              // 根据疾病决定选择Patient1还是Patient2
              if (diseaseId === this.selectedDisease1) {
                this.selectedPatient1 = patientId;
              } else if (diseaseId === this.selectedDisease2) {
                this.selectedPatient2 = patientId;
              } else {
                // 如果疾病与当前选择的两个疾病不匹配，选择Patient1
                this.selectedPatient1 = patientId;
                this.selectedDisease1 = diseaseId;
              }

              // 更新患者比较数据
              this.updatePatientComparison();
            }
          });
        }
      });
    });

    // 获取患者ID列表
    this.fetchPatientIds();

    // 添加窗口resize监听器
    this.handleResize = this.debounce(() => {
      this.resizeAllCharts();
    }, 300);

    window.addEventListener('resize', this.handleResize);
  },

  beforeUnmount() {
    // 清理resize监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },

  activated() {
    // 当组件被激活时（从其他视图返回），重新调整所有图表大小
    console.log('Component activated, resizing charts...');
    this.$nextTick(() => {
      setTimeout(() => {
        this.resizeAllCharts();
      }, 100);
    });
  },

  updated() {
    // 当组件更新后，确保图表正确渲染
    this.$nextTick(() => {
      if (this.currentScreen === 'overview') {
        setTimeout(() => {
          this.resizeAllCharts();
        }, 50);
      }
    });
  },
};
</script>

<style scoped>
.diagnosis-assistant {
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  color: #333;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.header {
  background-color: #222;
  color: white;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  height: 45px;
  z-index: 10;
}

.header h1 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: bold;
  margin-right: 10px;
}

.subtitle {
  font-size: 0.85rem;
  font-style: italic;
}

.main-content {
  display: flex;
  flex: 1;
  width: 100%;
  height: calc(100vh - 45px);
  overflow: hidden;
  padding-bottom: 0;
}

.left-panel {
  width: 190px;
  background-color: #f0f0f0;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 5px; /* 减小水平内边距 */
  box-sizing: border-box;
  min-width: 0;
  height: 100%;
  overflow: hidden;
}

/* 第一屏和第二屏的通用样式 */
.first-screen,
.detail-section {
  min-height: calc(100vh - 46px);
  margin: 0;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
}

/* 第一屏特有样式 */
.first-screen {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 46px); /* 设置固定高度为可视区域高度减去header高度 */
  overflow-y: hidden; /* 禁止滚动 */
  padding: 5px; /* 减小内边距 */
}

.embedding-section {
  flex: 0 0 calc(50vh - 25px); /* 进一步微调高度 */
  height: calc(50vh - 25px);
  min-height: 270px;
  margin-bottom: 3px; /* 减小底部间距 */
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
}

.visualization-section {
  flex: 0 0 calc(50vh - 25px); /* 进一步微调高度 */
  height: calc(50vh - 25px);
  min-height: 270px;
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
}

/* 调整嵌入式可视化的高度 */
.visualization-canvas {
  flex: 1; /* 填充所有可用空间 */
  min-height: 200px;
  background-color: #f5f5f5;
  margin-bottom: 5px; /* 减小边距 */
  border-radius: 8px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  display: flex;
}

.visualization-canvas .chart {
  width: 100%;
  height: 100%;
}

/* 调整可视化面板容器 */
.visualization-panels {
  flex: 1;
  display: flex;
  margin-bottom: 0; /* 移除底部边距 */
  height: 90%; /* 设置面板高度占据C区域的大部分 */
  gap: 5px; /* 减小间隙 */
}

/* 调整面板内容区域填充整个可用空间 */
.panel-content {
  flex: 1;
  height: 90%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 5px; /* 减小填充 */
}

/* 确保图表填充可用空间 */
.chart {
  width: 100%;
  height: 100%;
  min-height: 150px;
}

/* 第二屏特有样式 */
.detail-section {
  min-height: calc(100vh - 46px);
  margin: 0;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  height: 100%;
  overflow-y: auto;
  z-index: 5;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0 5px;
  margin-bottom: 20px;
}

.knowledge-graph-container {
  flex: 3;
  margin-bottom: 15px;
  min-height: 400px;
  max-height: 60vh;
  overflow: hidden;
}

.knowledge-graphs {
  display: flex;
  gap: 15px;
  height: 100%;
  width: 100%;
}

.graph-section {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.image-comparison-enhanced {
  flex: 2;
  display: flex;
  margin-bottom: 10px;
  min-height: 250px;
  max-height: 35vh;
}

.right-panel {
  width: 500px;
  background-color: #f0f0f0;
  border-left: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
  height: 100%;
 
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
  flex-shrink: 0;
  position: relative; /* Add position relative */
}

/* 压缩左侧面板中的标题和内容 */
.panel-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 5px; /* 减小边距 */
}

.panel-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 3px 0 10px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #1976d2;
}

.modality-item {
  padding: 3px 0;
  cursor: pointer;
  font-size: 0.85rem;
}

.modality-item.selected {
  font-weight: bold;
  color: #1976d2;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* 减小边距 */
}

.section-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  font-weight: bold;
  margin-right: 8px;
  font-size: 0.9rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1rem;
}

.save-button {
  background-color: #54af4c;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  margin-left: auto;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-button:hover {
  background-color: #388e3c;
}

.case-selection {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* 减小边距 */
  flex-wrap: wrap; /* 允许换行，避免拥挤 */
}

.case {
  margin: 0 5px;
  padding: 0 5px;
  border-bottom: 2px solid;
}

.case.normal {
  border-color: #2196f3;
}

.case.herniated {
  border-color: #f44336;
}

.case.bulging {
  border-color: #4caf50;
}

.current-case {
  font-weight: bold;
}

.embedding-visualization {
  flex: 1; /* 填充所有可用空间 */
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  padding: 8px; /* 减小填充 */
  margin-bottom: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.embedding-visualization:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.visualization-canvas::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    transparent 70%,
    rgba(0, 0, 0, 0.03) 100%
  );
  pointer-events: none;
}

.embedding-types {
  display: flex;
  justify-content: space-between;
}

.embedding-type {
  flex: 1;
  text-align: center;
}

.embedding-bars {
  height: 35px; /* 稍微减小高度 */
  background-color: #f0f0f0;
  margin: 0 5px;
}

.embedding-name {
  font-size: 0.75rem;
  margin-top: 4px;
}

.visualization-section {
  display: flex;
  flex-direction: column;
}

.visualization-panels {
  flex: 1; /* 填充所有可用空间 */
  display: flex;
  margin-bottom: 0; /* 移除底部边距 */
  min-height: 200px;
  gap: 5px; /* 面板之间的间距 */
}

.panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
  z-index: 10;
  height: 40px; /* 固定高度 */
}

.panel-controls {
  display: flex;
  margin-top: 5px; /* 减小上边距 */
  height: 30px; /* 减小高度 */
}

.control {
  padding: 1px 5px;
  font-size: 0.75rem;
  cursor: pointer;
}
.control.active {
  background-color: #1976d2;
  color: white;
  border-radius: 3px;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 5px; /* 减小填充 */
}

.detail-header {
  font-weight: bold;
  margin-bottom: 15px;
  font-size: 1rem;
  width: 100%;
  background-color: #f5f5f5;
  padding: 5px 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.detail-header .section-header {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.card-details {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.card-header {
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.card-id,
.card-label {
  font-weight: bold;
  margin-right: 8px;
}

.card-label.bulging {
  color: #4caf50;
}

/* 新增：完全移除灰色背景的图像比较部分样式 */
.image-comparison-enhanced {
  flex: 4; /* 占据第二屏的4/10 */
  display: flex;
  margin-bottom: 8px;
  height: 30vh;
}

.raw-image-section,
.cam-image-section {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  margin: 0 5px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-content-container {
  display: flex;
  flex-direction: column;
}

/* 修改：使用CSS背景图片而不是img标签 */
.medical-image {
  width: 100%;
  height: 20vh; /* 图像高度占据视口的20% */
  border-radius: 4px;
  margin-bottom: 6px;
  background-size: cover;
  background-position: center;
  border: 1px solid #e0e0e0;
  background-color: #f8f8f8;
}

.raw-placeholder {
  background-color: #f2f2f2;
  background-image: linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0
    ),
    linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0
    );
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.cam-placeholder {
  background-color: #f8f8f8;
  background-image: linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0
    ),
    linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0
    );
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.image-chart {
  width: 100%;
  flex: 1; /* 图表占据剩余空间 */
  min-height: 60px;
}

.comparison-section {
  display: flex;
  flex-direction: column;
}

.comparison-item {
  background-color: white;
  border-radius: 4px;
  padding: 10px 10px 10px 25px; /* Reduced left padding from 35px to 25px */
  margin-bottom: 12px;
  position: relative;
}

.item-number {
  position: absolute;
  top: 10px;
  left: 5px; /* Moved left from 10px to 5px */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.comparison-item h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  padding-left: 0; /* Removed padding-left (was 5px) */
}

.patient-comparison {
  display: flex;
  padding-left: 0; /* Removed padding-left (was 20px) */
}

.patient {
  flex: 1;
  padding: 0 5px; /* Reduced horizontal padding from 8px to 5px */
}

.patient-header {
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.indicator-section {
  margin-bottom: 12px;
}

.indicator-header {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.card-info {
  padding-left: 25px; /* 为数字留出空间 */
}

.info-row {
  display: flex;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.info-row span {
  flex: 1;
}

/* 图表样式 */
.chart {
  width: 100%;
  height: 100%;
}

.small-chart {
  width: 100%;
  height: 100%;
  overflow: visible !important; /* 允许图表内容溢出容器，确保tooltip能够完全显示 */
}

.pie-comparison {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.pie-chart-container {
  width: 45%;
}

.pie-chart {
  width: 100%;
  height: 140px;
}

/* 图像网格样式 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 4px;
  height: 100%;
  overflow-y: auto;
}

.image-item {
  background-color: #ddd;
  aspect-ratio: 1;
  border-radius: 2px;
}

/* 新增：按钮样式 */
.panel-button {
  display: block;
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 6px;
  background-color: #f8f8f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  text-align: left;
  transition: all 0.2s;
}

.panel-button:hover {
  background-color: #e8e8e8;
}

.panel-button.primary {
  background-color: #1976d2;
  color: white;
  border-color: #1565c0;
}

.panel-button.primary:hover {
  background-color: #1565c0;
}

.button-icon {
  margin-right: 5px;
}

/* 新增：疾病选择下拉框 */
.disease-dropdown {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.85rem;
}

/* 新增：助诊功能样式 */
.diagnostic-assistant-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 0; /* Remove bottom margin */
  flex: 1;

  max-height: calc(100vh - 210px); /* Adjust max height */
  padding: 0 5px;
  position: relative; /* Add position relative */
}

.diagnosis-card {
  background-color: white;
  border-radius: 4px;
  padding: 10px 10px 15px 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.toggle-button {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.toggle-button:hover {
  background-color: #e3f2fd;
}

.diagnosis-card.collapsed {
  padding-bottom: 5px;
}

.diagnosis-card h4 {
  margin: 0 0 8px 0;
  font-size: 0.95rem;
  color: #1976d2;
}

.diagnosis-description {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 10px;
}

.patient-selector {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selector-group {
  margin-bottom: 0;
}

.selector-group label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 4px;
}

.patient-dropdown {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
}

.action-button {
  width: 100%;
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.compare-button {
  background-color: #1976d2;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: normal;
  margin-top: 10px;
}

.compare-button:hover {
  background-color: #1565c0;
}

.ai-controls {
  margin-bottom: 10px;
}

.ai-options {
  margin-bottom: 10px;
}

.checkbox-container {
  display: block;
  position: relative;
  padding-left: 25px;
  margin-bottom: 6px;
  font-size: 0.8rem;
  cursor: pointer;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #eee;
  border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #2196f3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.insights-container {
  margin-bottom: 12px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 6px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.insight-icon {
  margin-right: 8px;
  font-size: 1rem;
}

.insight-content {
  flex: 1;
  font-size: 0.8rem;
}

.insight-text {
  color: #555;
}

.results-card {
  border-left: 4px solid #ff9800;
}

/* 新增：AI对话框样式 */
.ai-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.ai-dialog {
  background-color: white;
  border-radius: 6px;
  width: 80%;
  max-width: 700px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  color: #1976d2;
}

.close-button {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.dialog-content {
  padding: 0 20px;
  overflow-y: auto;
  flex: 1;
  max-height: 60vh;
}

.dialog-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin: 0 -20px 15px;
  padding: 0 20px;
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
}

.tab.active {
  border-bottom: 2px solid #1976d2;
  color: #1976d2;
}

.tab-panel {
  padding: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.dialog-footer button {
  margin-left: 10px;
  padding: 8px 16px;
}

.primary-button {
  background-color: #1976d2;
}

.primary-button:hover {
  background-color: #1565c0;
}

/* 新增：小按钮样式 */
.mini-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 5px;
  font-size: 0.7rem;
  cursor: pointer;
  margin-top: 4px;
}

.mini-button:hover {
  background-color: #e0e0e0;
}

/* 新增：次要按钮样式 */
.secondary-button {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  margin-bottom: 8px;
}

.secondary-button:hover {
  background-color: #e0e0e0;
}

/* 新增：临床应用按钮组 */
.clinical-actions {
  display: flex;
  flex-direction: column;
}

/* 改进：复选框标签样式 */
.checkbox-label {
  font-size: 0.8rem;
}

/* 新增：聊天界面样式 */
.chat-panel {
  padding: 0 !important;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Fill the entire height */
  min-height: 280px; /* Reduced from 300px */
  overflow: hidden;
  padding-bottom: 0; /* Remove bottom padding */
  position: relative; /* Add position relative */
}

.chat-messages-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 180px; /* Reduced from 200px */
  max-height: calc(100% - 60px); /* Leave room for input */
  padding-bottom: 10px; /* Add padding at the bottom */
}

/* 删除了有问题的absolute定位的chat-messages样式 */

/* 删除了重复的message样式定义 */

/* 删除了重复的ai-message和user-message样式定义 */

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  margin: 0 8px;
}

/* 删除了重复的message-content样式定义 */

/* 删除了重复的chat-input样式定义 */

.chat-send-button {
  margin-left: 8px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 15px; /* Reduced padding */
  font-size: 0.9rem;
  cursor: pointer;
  height: 36px; /* Reduced from 40px */
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-send-button:hover {
  background-color: #1565c0;
}

.typing-indicator {
  padding: 5px 0;
}

.typing-dots {
  display: flex;
  align-items: center;
  gap: 5px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #aaa;
  display: inline-block;
  animation: typing 1.4s infinite both;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    opacity: 0.3;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px);
  }
  100% {
    opacity: 0.3;
    transform: translateY(0);
  }
}

/* 确保AI聊天面板在比较面板收缩时扩展 */
.collapsed + .ai-chat-panel {
  max-height: calc(100vh - 80px);
}

/* 修改AI聊天面板样式，确保正确显示 */
.ai-chat-panel {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 400px; /* Increase min height */
  max-height: calc(100vh - 180px); /* Ensure there's room at the bottom */
  margin-top: 5px;
  margin-bottom: 0; /* Remove bottom margin */
  padding-bottom: 0; /* Remove bottom padding */
  position: relative; /* Add position relative for absolute positioning */
}

/* 添加图像标题样式 */
.image-header {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 8px;
  color: #1976d2;
}

/* 添加图像说明样式 */
.image-description {
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
  padding: 0 5px;
}

/* 添加疾病特征列表样式 */
.disease-features {
  margin-top: 10px;
  padding: 0 5px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;
  font-size: 0.8rem;
}

.feature-icon {
  margin-right: 5px;
  color: #1976d2;
}

.feature-text {
  flex: 1;
}

.node-label {
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  text-align: center;
}

.patient-connections {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 1;
}

.disease1-connections {
  top: 60px;
  left: 50px;
}

.disease2-connections {
  top: 60px;
  right: 50px;
}

.connection-line {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.line {
  width: 2px;
  height: 30px;
  background-color: #90caf9;
}

.patient-node {
  width: 100px;
  height: 30px;
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.patient-id {
  font-weight: bold;
  color: #333;
}

/* 修改疾病选择样式 */
.disease-comparison {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selector-group {
  margin-bottom: 8px;
}

.selector-group label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 4px;
  font-weight: bold;
}

.left-panel h2 {
  font-size: 1.1rem;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #1976d2;
  color: #333;
}

.graph-header {
  font-weight: bold;
  font-size: 1rem;
  color: #1976d2;
  margin-bottom: 15px;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  border-radius: 4px 4px 0 0;
}

.knowledge-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  width: 100%;
  flex: 1;
  min-height: 300px;
  overflow: hidden;
}

/* 删除了patient-eye-image样式 */

/* 删除了text-content样式 */

.comparison-item.full-height {
  flex: 1;
  height: calc(100vh - 350px);
  overflow-y: auto;
}

.indicator-visualization {
  height: 180px;
  overflow-y: hidden;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 10px;
}

/* 删除了重复的text-content样式 */

.indicator-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 0.9rem;
}

.patient-info {
  font-size: 0.85rem;
  color: #666;
  margin-left: auto;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 5px;
}

.indicator-visualization {
  height: 180px;
  overflow-y: hidden;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 10px;
}

/* Add specific styles for the analysis container */
.analysis-container {
  padding: 10px;
  overflow-y: auto;
  max-height: 350px; /* Match the chat-messages-wrapper max height */
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 0;
}

.analysis-section {
  background-color: white;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 5px;
}

.analysis-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 0.95rem;
  color: #1976d2;
}

/* Adjust panel header with better tab spacing */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
}

.tab-buttons {
  display: flex;
  gap: 5px;
}

.tab-button {
  background: none;
  border: none;
  padding: 5px 10px;
  font-size: 0.9rem;
  cursor: pointer;
  border-radius: 4px;
}

.tab-button.active {
  background-color: #1976d2;
  color: white;
}

.disease-node {
  width: 120px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  margin: 10px 0;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.disease-node.disease1 {
  position: absolute;
  top: 20px;
  left: 50px;
}

.disease-node.disease2 {
  position: absolute;
  top: 20px;
  right: 50px;
}

.disease-node.macular_edema {
  background-color: #e53935;
}

.disease-node.open_angle_glaucoma {
  background-color: #ff9800;
}

.disease-node.closed_angle_glaucoma {
  background-color: #7b1fa2;
}

.disease-node.diabetic_maculopathy {
  background-color: #4caf50;
}

.disease-node.retinitis_pigmentosa {
  background-color: #9c27b0;
}

.disease-node.branch_retinal_vein_occlusion {
  background-color: #795548;
}

/* Add styles for the patient info summary */
.patient-info-summary {
  display: flex;
  justify-content: space-between;
  background-color: rgba(240, 240, 240, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  margin: 5px 0 10px 0;
  font-size: 0.9rem;
  border-left: 3px solid #1976d2;
}

.patient-info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: bold;
  margin-right: 5px;
  color: #333;
}

.info-value {
  color: #1976d2;
}

/* Add these CSS styles at the end of the style section */
.clustering-controls {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.clustering-title {
  font-size: 0.85rem;
  font-weight: bold;
  color: #555;
  margin-bottom: 8px;
}

.clustering-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.clustering-option {
  padding: 5px 12px;
  background-color: #e0e0e0;
  border-radius: 15px;
  font-size: 0.8rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.clustering-option:hover {
  background-color: #d0d0d0;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

/* Add this CSS to the <style> section */
.patient-visits-section {
  display: flex;
  background-color: #f5f9ff;
  border-radius: 4px;
  margin-bottom: 10px;
  border-left: 3px solid #ff7675;
  overflow: hidden;
}

.visits-stats {
  flex: 2;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
}

.visit-stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-label {
  font-size: 0.85rem;
  font-weight: bold;
  color: #555;
}

.stat-value {
  font-size: 1rem;
  color: #ff7675;
  font-weight: bold;
}

.trend-chart-container {
  width: 100%;
  height: 250px !important; /* 增加高度并使用!important确保应用 */
  margin-bottom: 15px;
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.patient-trend-chart {
  width: 100%;
  height: 100% !important;
}

/* 调整图表容器布局 */
.charts-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 15px;
  height: 270px !important; /* 增加整体高度 */
}

/* 添加患者统计行样式 */
.patient-stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  padding: 5px;
  border-radius: 4px;
}

.patient-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 8px;
}

.stat-label {
  font-size: 0.7rem;
  color: #666;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: bold;
  color: #333;
}

/* 调整图像内容容器 */
.image-content-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  padding: 8px;
}

/* 调整图像比较部分 */
.image-comparison-enhanced {
  flex: 2;
  display: flex;
  margin-bottom: 10px;
  min-height: 350px !important; /* 增加最小高度 */
  max-height: 40vh !important; /* 增加最大高度 */
}

.raw-image-section,
.cam-image-section {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  margin: 0 5px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.image-header {
  font-size: 0.85rem;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1976d2;
}

/* 调整panel头部样式使其更紧凑 */
.panel-header {
  font-weight: bold;
  padding: 5px 8px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

/* 调整面板控件样式使其更紧凑 */
.panel-controls {
  display: flex;
  padding: 3px 8px;
  border-bottom: 1px solid #eee;
  background-color: #fafafa;
  font-size: 12px;
}

/* 调整控件样式 */
.control,
.filter-control,
.label-control {
  padding: 2px 6px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 3px;
}

.control.active {
  background-color: #1976d2;
  color: white;
}

/* 精简聚类控件样式 */
.clustering-controls {
  display: flex;
  align-items: center;
  margin-top: 3px;
  height: 25px;
}

.clustering-title {
  font-size: 12px;
  margin-right: 5px;
  white-space: nowrap;
}

.clustering-options {
  display: flex;
  flex-wrap: wrap;
}

.clustering-option {
  font-size: 12px;
  padding: 2px 5px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 3px;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

/* 调整指标部分的样式 */
.indicator-section {
  margin-bottom: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  padding: 0 3px;
}

.indicator-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 0.85rem;
}

.patient-info {
  font-size: 0.75rem;
  color: #666;
  margin-left: auto;
}

.indicator-visualization {
  height: 180px;
  background-color: white;
  border-radius: 4px;
  padding: 5px;
  overflow: visible; /* 修改为visible以允许tooltip完全显示 */
  border: 1px solid #eee;
}

/* 优化小型图表样式 */
.small-chart {
  width: 100%;
  height: 100%;
  min-height: 160px;
  z-index: 1; /* 增加z-index值 */
}

/* 热力图对比区样式 */
.heatmap-comparison {
  display: flex;
  flex-direction: row;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: visible !important; /* 修改为visible，确保tooltip不会被裁剪 */
  position: relative; /* 添加相对定位 */
  z-index: 50; /* 提高z-index值，确保在页面元素上层 */
  min-height: 250px; /* 设置最小高度，确保有足够空间显示图表和提示 */
}

.heatmap-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e3f2fd;
  padding: 5px;
  position: relative; /* 相对定位 */
  background-color: #fafafa;
  border-radius: 4px;
  overflow: visible; /* 确保内容可以溢出，tooltip不会被截断 */
  z-index: 10; /* 确保层级足够高 */
  height: 260px; /* 固定高度确保两个图表一致 */
}

.heatmap-section:first-child {
  margin-right: 10px;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid #e3f2fd;
  background-color: #f5f9ff;
}

.indicator-title {
  font-size: 14px;
  font-weight: bold;
  color: #1976d2;
}

.patient-info {
  font-size: 12px;
  color: #1976d2;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
}

.indicator-visualization {
  flex: 1;
  padding: 5px;
  min-height: 250px; /* 统一高度以确保对齐 */
  height: 250px; /* 固定高度以确保对齐 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative; /* 相对定位 */
  overflow: visible !important; /* 确保tooltip可以完全显示 */
}

/* 确保tooltip显示在最上层 */
.indicator-visualization :deep(.echarts-tooltip) {
  z-index: 9999 !important;
}

/* 确保热力图可以显示超出容器的内容 */
.heatmap-section .echarts-container,
.heatmap-section .echarts-container canvas {
  overflow: visible !important;
  z-index: auto !important;
}

.small-chart {
  width: 100%;
  height: 100%;
  min-height: 200px;
  z-index: 1; /* 增加z-index值 */
}

/* 自定义滑动条样式 */
.indicator-visualization :deep(.echarts-dataZoom-slider) {
  height: 20px !important;
  bottom: 5px !important;
  background-color: #f5f9ff !important;
}

/* 主题颜色覆盖 */
.indicator-visualization :deep(.echarts-dataZoom-handle) {
  fill: #1976d2 !important;
}

/* AI诊断弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.ai-diagnosis-modal {
  background: white;
  border-radius: 20px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1001;
}

.modal-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #1ea9ee, #4391eb);
  color: white;
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.4rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  border-top: 1px solid #e1e8ed;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
}

/* 患者信息样式 */
.patient-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.patient-info h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-weight: bold;
  color: #495057;
}

.info-value {
  color: #1976d2;
}

.diagnosis-name {
  font-weight: bold;
  color: #28a745;
}

/* 诊断标签页样式 */
.diagnosis-tabs {
  display: flex;
  border-bottom: 2px solid #e1e8ed;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-weight: 600;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  border-radius: 8px 8px 0 0;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  color: #1976d2;
  border-bottom-color: #1976d2;
  background: #f8f9fa;
}

/* 标签页内容样式 */
.tab-content {
  min-height: 300px;
}

.content-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.header-icon {
  font-size: 1.5rem;
}

.content-header h4 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2rem;
}

.diagnostic-content-panel {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 5px solid #1976d2;
}

.content-wrapper {
  line-height: 1.6;
}

/* 诊断部分样式 */
.diagnosis-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

.diagnosis-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.section-title {
  color: #1976d2;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: "▶";
  color: #1976d2;
  font-size: 12px;
}

.section-content {
  color: #495057;
  line-height: 1.8;
  font-size: 15px;
  padding-left: 16px;
  border-left: 2px solid #e3f2fd;
  margin-left: 8px;
}

.formatted-content {
  color: #495057;
  line-height: 1.8;
  font-size: 15px;
}

/* 文本高亮样式 */
.title-highlight {
  color: #1976d2;
  font-weight: 600;
  font-size: 16px;
}

.text-bold {
  font-weight: 600;
  color: #2c3e50;
}

.keyword-highlight {
  background: linear-gradient(120deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
  border: 1px solid #90caf9;
  margin: 0 1px;
}

.number-highlight {
  background: linear-gradient(120deg, #f3e5f5 0%, #e1bee7 100%);
  color: #7b1fa2;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 500;
  font-family: 'Consolas', 'Monaco', monospace;
  border: 1px solid #ce93d8;
}

.status-highlight {
  padding: 2px 6px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 13px;
  margin: 0 2px;
}

/* 不同状态的颜色 - 通过JavaScript动态添加类名 */
.status-normal {
  background: #e8f5e8 !important;
  color: #2e7d32 !important;
  border: 1px solid #a5d6a7 !important;
}

.status-abnormal {
  background: #ffebee !important;
  color: #c62828 !important;
  border: 1px solid #ef9a9a !important;
}

.status-mild {
  background: #fff3e0 !important;
  color: #ef6c00 !important;
  border: 1px solid #ffcc02 !important;
}

.status-moderate {
  background: #fce4ec !important;
  color: #ad1457 !important;
  border: 1px solid #f48fb1 !important;
}

.status-severe {
  background: #ffebee !important;
  color: #b71c1c !important;
  border: 1px solid #ef5350 !important;
}

/* 聊天内容样式 */
.chat-content {
  display: flex;
  flex-direction: column;
  height: 400px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.chat-content h4 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.chat-intro {
  color: #6c757d;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

/* 聊天消息容器 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 1rem;
  max-height: 300px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* AI消息容器 - 左侧对齐 */
.ai-message-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.ai-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 75%;
}

.ai-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid #90caf9;
}

.avatar-icon {
  font-size: 18px;
}

.ai-content {
  flex: 1;
}

.ai-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px 18px 18px 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e3f2fd;
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.ai-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  margin-left: 8px;
}

/* 用户消息容器 - 右侧对齐 */
.user-message-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.user-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 75%;
  flex-direction: row-reverse;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #1565c0);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
  border: 2px solid #1976d2;
}

.user-content {
  flex: 1;
  text-align: right;
}

.user-text {
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
  line-height: 1.6;
  font-size: 14px;
  text-align: left;
}

.user-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  margin-right: 8px;
}

/* 聊天消息滚动条美化 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-message,
  .user-message {
    max-width: 85%;
  }

  .ai-avatar,
  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-icon {
    font-size: 16px;
  }

  .ai-text,
  .user-text {
    padding: 10px 14px;
    font-size: 13px;
  }

  .chat-messages {
    max-height: 250px;
    padding: 0.8rem;
    gap: 12px;
  }
}

.chat-input-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chat-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.chat-input:focus {
  outline: none;
  border-color: #1976d2;
}

.chat-input:disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.chat-send-button {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.chat-send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.chat-send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 按钮样式 */
.action-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.primary-button {
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
}

.primary-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.secondary-button {
  background: #6c757d;
  color: white;
}

.secondary-button:hover {
  background: #5a6268;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.analyze-button {
  background: linear-gradient(135deg, #2c95d2, #2074c9);
  color: white;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.analyze-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.no-result {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6c757d;
  font-size: 1.1rem;
}

/* 患者病情智能概况样式 - 学术简洁风格 */
.patient-overview-section {
  margin-top: 20px;
  padding: 0;
  background: transparent;
}

.overview-header {
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 2px solid #1976d2;
  padding-bottom: 15px;
}

.main-title {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 500;
  color: #1976d2;
  letter-spacing: 1px;
}

/* 病情概况总结区域 */
.summary-section {
  margin-bottom: 40px;
  border: 1px solid #e3f2fd;
  background: #fafafa;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #1976d2;
  color: white;
  margin: 0;
  border-bottom: 1px solid #1565c0;
}

.title-icon {
  font-size: 16px;
}

.section-title h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.summary-content {
  padding: 25px;
  min-height: 150px;
  position: relative;
  background: white;
}

.summary-text {
  line-height: 1.8;
  color: #333;
  font-size: 15px;
  text-align: justify;
  padding: 20px;
  background: #f8f9fa;
  border-left: 4px solid #1976d2;
  border-radius: 0;
  margin: 0;
  font-family: 'Microsoft YaHei', sans-serif;
}

.no-summary {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 50px 0;
  font-size: 14px;
}

/* 病情关键词区域 */
.keywords-section {
  border: 1px solid #e3f2fd;
  background: #fafafa;
}

.keywords-chart-container {
  padding: 25px;
  min-height: 350px;
  position: relative;
  background: white;
}

.keywords-chart {
  width: 100%;
  height: 350px;
}

/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e3f2fd;
  border-top: 3px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 1.4rem;
  }

  .section-title {
    padding: 12px 15px;
  }

  .section-title h4 {
    font-size: 1rem;
  }

  .summary-content,
  .keywords-chart-container {
    padding: 20px 15px;
  }

  .summary-text {
    padding: 15px;
    font-size: 14px;
  }
}
</style>

