<template>
  <div class="diagnosis-assistant">
    <header class="header">
      <h1>DiagnosisAssistant</h1>
      <span class="subtitle">A learning Tool for Medical Diagnosis</span>
    </header>
    
    <div class="main-content" :class="{ 'right-panel-collapsed': isRightPanelCollapsed }">
      <!-- Left Panel -->
      <div class="left-panel">
        <h2>Panel</h2>
        
        <!-- 新增：导入功能区域 -->
        <div class="panel-section import-section">
          <h3>Import/Export:</h3>
          <div class="button-group">
            <button class="panel-button primary">
              <span class="button-icon">📂</span> Import Data
            </button>
            <button class="panel-button">
              <span class="button-icon">📊</span> Import Image
            </button>
            <button class="panel-button">
              <span class="button-icon">💾</span> Export Results
            </button>
          </div>
        </div>
        
        <!-- 新增：疾病选择 -->
        <div class="panel-section disease-section">
          <h3>Case Selection:</h3>
          <div class="disease-comparison">
            <div class="selector-group">
              <label>Disease 1:</label>
              <select class="disease-dropdown" v-model="selectedDisease1" @change="updateDiseaseDisplay">
                <option value="macular_edema">Macular Edema</option>
                <option value="open_angle_glaucoma">Open-Angle Glaucoma</option>
                <option value="closed_angle_glaucoma">Closed-Angle Glaucoma</option>
                <option value="diabetic_maculopathy">Diabetic Maculopathy</option>
                <option value="retinitis_pigmentosa">Retinitis Pigmentosa</option>
                <option value="branch_retinal_vein_occlusion">Branch Retinal Vein Occlusion</option>
              </select>
            </div>
            <div class="selector-group">
              <label>Disease 2:</label>
              <select class="disease-dropdown" v-model="selectedDisease2" @change="updateDiseaseDisplay">
                <option value="open_angle_glaucoma">Open-Angle Glaucoma</option>
                <option value="macular_edema">Macular Edema</option>
                <option value="closed_angle_glaucoma">Closed-Angle Glaucoma</option>
                <option value="diabetic_maculopathy">Diabetic Maculopathy</option>
                <option value="retinitis_pigmentosa">Retinitis Pigmentosa</option>
                <option value="branch_retinal_vein_occlusion">Branch Retinal Vein Occlusion</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="panel-section">
          <h3>Dataset:</h3>
          <div class="dataset-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Datainfo:</h3>
          <div class="datainfo-item">Diagnostic Task:</div>
          <div class="datainfo-value"></div>
          
          <div class="datainfo-item">Diagnostic Entry:</div>
          <div class="datainfo-value"></div>
          
          <div class="datainfo-item">No. of cases:</div>
          <div class="datainfo-value"></div>
        </div>
        
        <div class="panel-section">
          <h3>Modality:</h3>
          <div class="modality-item selected"></div>
          <div class="modality-item"></div>
          <div class="modality-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Composition:</h3>
        </div>
        
        <div class="panel-section">
          <h3>Body Part:</h3>
          <div class="body-part-item"></div>
          <div class="body-part-item">Cross-section:</div>
          <div class="body-part-item"></div>
          <div class="body-part-item">Resolution:</div>
          <div class="body-part-item"></div>
        </div>
        
        <div class="panel-section">
          <h3>Model Performance:</h3>
        </div>
      </div>
      
      <!-- Center Content Area -->
      <div class="center-content">
        <!-- 第一屏：嵌入过渡和可视化面板 -->
        <div class="first-screen" v-show="currentScreen === 'first'">
        <!-- Section A: Embedding Transition -->
        <div class="embedding-section">
          <div class="section-header">
            <span class="section-label">A</span>
            <h3>Embedding Transition</h3>
              <div class="save-button" @click="toggleScreen">{{ currentScreen === 'first' ? 'View Detail' : 'Back' }}</div>
          </div>
          
          <div class="case-selection">
            <span>Selected Cases:</span>
            <span class="case normal"></span>
            <span class="case herniated"></span>
            <span class="case bulging"></span>
            <span>Current ID: </span>
            <span class="current-case bulging"></span>
          </div>
          
          <div class="embedding-visualization">
            <div class="visualization-canvas">
              <!-- 嵌入式可视化散点图 -->
              <v-chart class="chart" :option="scatterOption" autoresize />
            </div>
            
              <!-- 添加聚类控制面板 -->
              <div class="clustering-controls">
                <div class="clustering-title">Clustering By:</div>
                <div class="clustering-options">
                  <div class="clustering-option" :class="{ active: clusteringBy === 'none' }" @click="setClusteringBy('none')">None</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'age' }" @click="setClusteringBy('age')">Age</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'disease' }" @click="setClusteringBy('disease')">Disease</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'visits' }" @click="setClusteringBy('visits')">Visit Count</div>
                  <div class="clustering-option" :class="{ active: clusteringBy === 'other' }" @click="setClusteringBy('other')">Other</div>
                </div>
              </div>
          </div>
        </div>
        
        <!-- Section C: Visualization Panels -->
        <div class="visualization-section">
            <div class="section-header">
              <span class="section-label">C</span>
              <h3>Visualization Panels</h3>
            </div>
          
          <div class="visualization-panels">
            <div class="panel">
                <div class="panel-header">Disease Statistics</div>
              <div class="panel-controls">
                  <span class="control" :class="{ active: activeVisualizationMode === 'count' }" @click="activeVisualizationMode = 'count'">Case Count</span>
                  <span class="control" :class="{ active: activeVisualizationMode === 'trend' }" @click="activeVisualizationMode = 'trend'">Incidence Trend</span>
              </div>
              <div class="panel-content indicator-content">
                  <!-- 融合图表：根据模式切换显示 -->
                  <v-chart class="chart" :option="activeCombinedChartOption" autoresize />
              </div>
            </div>
            
            <div class="panel">
                <div class="panel-header">Disease Correlation</div>
              <div class="panel-controls">
                  <span class="filter-control">Filter</span>
                  <span class="label-control">Label</span>
              </div>
              <div class="panel-content text-distribution-content">
                  <!-- 热力图：疾病关联 -->
                  <v-chart class="chart" :option="diseaseCorrelationOption" autoresize />
              </div>
            </div>
            
            <div class="panel">
                <div class="panel-header">Seasonal Distribution</div>
              <div class="panel-controls">
                  <span class="control" :class="{ active: seasonalViewMode === 'quarter' }" @click="seasonalViewMode = 'quarter'">Quarter</span>
                  <span class="control" :class="{ active: seasonalViewMode === 'month' }" @click="seasonalViewMode = 'month'">Month</span>
              </div>
              <div class="panel-content image-gallery-content">
                  <!-- 季度/月度分布图表 -->
                  <v-chart class="chart" :option="seasonalDistributionOption" autoresize />
                </div>
                </div>
              </div>
            </div>
          </div>
          
        <!-- 第二屏：Detail区域 -->
        <div class="detail-section" v-show="currentScreen === 'detail'">
          <div class="detail-header">
            <div class="section-header">
              <span class="section-label">D</span>
              <h3>Detail</h3>
              <div class="save-button" @click="toggleScreen">{{ currentScreen === 'first' ? 'View Detail' : 'Back' }}</div>
            </div>
          </div>
            
          <div class="detail-content">
            <!-- 知识图谱展示区域 -->
            <div class="knowledge-graph-container">
            <div class="knowledge-graphs">
              <div class="graph-section">
                <div class="graph-header">{{ formatDiseaseName(selectedDisease1) }} Knowledge Graph</div>
                <div class="knowledge-graph">
                  <DiseasePatientNetwork 
                    :diseases="[getDiseaseData(selectedDisease1)]" 
                    :patients="getPatientsDataForDisease(selectedDisease1)" 
                    @node-click="handleNodeClick"
                  />
                  </div>
                      </div>
                  
              <div class="graph-section">
                <div class="graph-header">{{ formatDiseaseName(selectedDisease2) }} Knowledge Graph</div>
                <div class="knowledge-graph">
                  <DiseasePatientNetwork 
                    :diseases="[getDiseaseData(selectedDisease2)]" 
                    :patients="getPatientsDataForDisease(selectedDisease2)" 
                    @node-click="handleNodeClick"
                  />
                    </div>
                  </div>
                  </div>
                      </div>
            
            <!-- 修改图像比较部分，将图表移动到灰色框内 -->
            <div class="image-comparison-enhanced">
              <div class="raw-image-section">
                <div class="image-header">Patient 1: {{ selectedPatient1 }} ({{ formatDiseaseName(selectedDisease1) }})</div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.visits || '5' }}</span>
                    </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.hospitalized || '1' }}</span>
                  </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.age }}</span>
                </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient1]?.gender }}</span>
              </div>
            </div>
            
                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart class="patient-trend-chart" :option="patientTrend1Option" autoresize />
                  </div>
                    <!-- 移除指标图表 -->
                </div>
              </div>
              </div>
              
              <div class="cam-image-section">
                <div class="image-header">Patient 2: {{ selectedPatient2 }} ({{ formatDiseaseName(selectedDisease2) }})</div>
                <div class="image-content-container">
                  <!-- 简化布局，只显示关键指标图表 -->
                  <div class="patient-stats-row">
                    <div class="patient-stat-item">
                      <span class="stat-label">Visits:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.visits || '3' }}</span>
                  </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Hospitalized:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.hospitalized || '0' }}</span>
                </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Age:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.age }}</span>
              </div>
                    <div class="patient-stat-item">
                      <span class="stat-label">Gender:</span>
                      <span class="stat-value">{{ patientDetails[selectedPatient2]?.gender }}</span>
            </div>
          </div>
                  
                  <!-- 显示趋势图和指标图表 -->
                  <div class="charts-container">
                    <div class="trend-chart-container">
                      <v-chart class="patient-trend-chart" :option="patientTrend2Option" autoresize />
                    </div>
                    <!-- 移除指标图表 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Right Panel -->
      <div class="right-panel">
        <CollapsiblePanel 
          :initialCollapsed="isRightPanelCollapsed"
          @toggle="handlePanelToggle"
        >
          <div class="section-header">
            <span class="section-label">B</span>
            <h3>Comparison</h3>
          </div>
          
          <div class="comparison-section">
            <div class="comparison-item full-height">
              <span class="item-number">1</span>
              <h4>Patient Comparison</h4>
              
              <div class="patient-comparison">
                <div class="patient">
                  <div class="patient-header">Patient 1</div>
                  <div class="patient-selector">
                    <select class="patient-dropdown" v-model="selectedPatient1" @change="handlePatientSelection($event, 1)">
                      <option v-for="patientId in getPatientsForDisease(selectedDisease1)" :key="patientId" :value="patientId">
                        ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease1) }})
                      </option>
                    </select>
                  </div>
                  
                  <div class="indicator-section">
                    <div class="indicator-header">
                      <span class="indicator-title">Key Indicators</span>
                      <span class="patient-info">{{ patientDetails[selectedPatient1]?.age }}y, {{ patientDetails[selectedPatient1]?.gender }}</span>
                    </div>
                    <div class="indicator-visualization">
                      <!-- SHAP图表 -->
                      <v-chart class="small-chart" :option="shapChartOption1" autoresize />
                    </div>
                  </div>
                  
                  <div class="text-section">
                    <div class="text-header">Text</div>
                    <div class="text-content">
                      {{ patientDetails[selectedPatient1]?.textData }}
                    </div>
                  </div>
                  
                  <div class="image-section">
                    <div class="image-header">Image</div>
                    <div class="image-content">
                      <img 
                        :src="patientDetails[selectedPatient1]?.imagePath" 
                        alt="Patient 1 Eye Image" 
                        class="patient-eye-image" 
                      />
                    </div>
                  </div>
                </div>
                
                <div class="patient">
                  <div class="patient-header">Patient 2</div>
                  <div class="patient-selector">
                    <select class="patient-dropdown" v-model="selectedPatient2" @change="handlePatientSelection($event, 2)">
                      <option v-for="patientId in getPatientsForDisease(selectedDisease2)" :key="patientId" :value="patientId">
                        ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease2) }})
                      </option>
                    </select>
                  </div>
                  
                  <div class="indicator-section">
                    <div class="indicator-header">
                      <span class="indicator-title">Key Indicators</span>
                      <span class="patient-info">{{ patientDetails[selectedPatient2]?.age }}y, {{ patientDetails[selectedPatient2]?.gender }}</span>
                    </div>
                    <div class="indicator-visualization">
                      <!-- SHAP图表 -->
                      <v-chart class="small-chart" :option="shapChartOption2" autoresize />
                    </div>
                  </div>
                  
                  <div class="text-section">
                    <div class="text-header">Text</div>
                    <div class="text-content">
                      {{ patientDetails[selectedPatient2]?.textData }}
                    </div>
                  </div>
                  
                  <div class="image-section">
                    <div class="image-header">Image</div>
                    <div class="image-content">
                      <img 
                        :src="patientDetails[selectedPatient2]?.imagePath" 
                        alt="Patient 2 Eye Image" 
                        class="patient-eye-image" 
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="section-header">
            <span class="section-label">D</span>
            <h3>AI Diagnostic Assistant</h3>
          </div>
          
          <!-- 修改：简化AI诊断功能，使用弹出对话框 -->
          <div class="diagnostic-assistant-section">
            <div class="diagnosis-card" :class="{ 'collapsed': isComparisonCollapsed }">
              <div class="card-header-row">
              <h4>AI-Powered Diagnostic Comparison</h4>
                <button class="toggle-button" @click="toggleComparisonPanel" v-if="showAIChat">
                  {{ isComparisonCollapsed ? '▼' : '▲' }}
                </button>
              </div>
              
              <div class="card-content" v-show="!isComparisonCollapsed">
              <p class="diagnosis-description">Use AI to analyze and compare diagnostic information in real-time.</p>
              
              <div class="patient-selector">
                <div class="selector-group">
                  <label>Patient 1:</label>
                  <select class="patient-dropdown" v-model="selectedPatient1" @change="handlePatientSelection($event, 1)">
                    <option v-for="patientId in getPatientsForDisease(selectedDisease1)" :key="patientId" :value="patientId">
                      ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease1) }})
                    </option>
                  </select>
                </div>
                <div class="selector-group">
                  <label>Patient 2:</label>
                  <select class="patient-dropdown" v-model="selectedPatient2" @change="handlePatientSelection($event, 2)">
                    <option v-for="patientId in getPatientsForDisease(selectedDisease2)" :key="patientId" :value="patientId">
                      ID: {{ patientId }} ({{ formatDiseaseName(selectedDisease2) }})
                    </option>
                  </select>
                </div>
              </div>
              
              <div class="ai-controls">
                <div class="ai-options">
                  <label class="checkbox-container">
                    <input type="checkbox" checked>
                    <span class="checkmark"></span>
                    <span class="checkbox-label">Realtime analysis</span>
                  </label>
                  <label class="checkbox-container">
                    <input type="checkbox" checked>
                    <span class="checkmark"></span>
                    <span class="checkbox-label">Include clinical data</span>
                  </label>
                  </div>
                </div>
              </div>
              
              <button class="action-button compare-button" @click="startComparison" v-if="!showAIChat">
                <span class="button-icon">🔍</span> Run AI Comparison
              </button>
            </div>
            
            <!-- 修改AI聊天面板结构，修复布局问题 -->
            <div class="ai-chat-panel" v-if="showAIChat">
              <div class="panel-header">
                <h4>AI Diagnostic Chat</h4>
                <div class="tab-buttons">
                  <button class="tab-button" 
                          :class="{ active: activeChatTab === 'chat' }" 
                          @click="activeChatTab = 'chat'">Chat</button>
                  <button class="tab-button" 
                          :class="{ active: activeChatTab === 'analysis' }" 
                          @click="activeChatTab = 'analysis'">Analysis</button>
              </div>
            </div>
              
              <!-- 聊天面板 -->
              <div v-if="activeChatTab === 'chat'" class="chat-container">
                <div class="chat-messages-wrapper">
                  <div class="chat-messages" ref="chatMessages">
                    <div v-for="(message, index) in chatMessages" :key="index" 
                         :class="['message', message.sender === 'ai' ? 'ai-message' : 'user-message']">
                      <div class="message-avatar">
                        <span v-if="message.sender === 'ai'">🤖</span>
                        <span v-else>👨‍⚕️</span>
            </div>
                        <div class="message-content">
                          <div class="message-text" v-html="message.text"></div>
                          <div class="message-time">{{ message.time }}</div>
          </div>
        </div>
                      <div v-if="isAiTyping" class="message ai-message typing-indicator">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                          <div class="typing-dots">
                            <span></span><span></span><span></span>
                          </div>
                        </div>
                      </div>
                    </div>
            </div>
            
                  <!-- 移除建议问题区域，只保留输入框 -->
                  <div class="chat-input-container">
                    <input type="text" class="chat-input" v-model="userMessage" 
                           @keyup.enter="sendMessage" placeholder="Ask AI about the comparison...">
                    <button class="chat-send-button" @click="sendMessage">
                      <span>Send</span>
                    </button>
                  </div>
              </div>
              
                <!-- 分析结果面板 -->
                <div v-if="activeChatTab === 'analysis'" class="analysis-container">
                  <div class="analysis-section">
                    <h5>Key Differences</h5>
                    <div class="insight-item">
                      <span class="insight-icon">🔍</span>
                      <div class="insight-content">
                        <strong>Structural Difference:</strong> 
                        <span class="insight-text">Patient 1 shows disc protrusion at C5-C6 level, while Patient 2 has normal alignment.</span>
                      </div>
                    </div>
                    <div class="insight-item">
                      <span class="insight-icon">📊</span>
                      <div class="insight-content">
                        <strong>Signal Intensity:</strong>
                        <span class="insight-text">Higher T2 signal intensity in Patient 1 indicates potential inflammation.</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="analysis-section">
                    <h5>Similarities</h5>
                    <div class="insight-item">
                      <span class="insight-icon">🔄</span>
                      <div class="insight-content">
                        <strong>Vertebral Alignment:</strong>
                        <span class="insight-text">Both patients maintain normal cervical lordosis.</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="analysis-section">
                    <h5>Recommendations</h5>
                    <div class="insight-item">
                      <span class="insight-icon">💡</span>
                      <div class="insight-content">
                        <strong>Learning Point:</strong>
                        <span class="insight-text">Compare the disc margin contour to differentiate between bulging and herniation.</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="analysis-actions">
                    <button class="action-button secondary-button">
                      <span class="button-icon">📥</span> Download Report
                    </button>
                    <button class="action-button secondary-button" @click="activeChatTab = 'chat'">
                      <span class="button-icon">💬</span> Ask Follow-up Questions
                    </button>
                  </div>
                </div>
            </div>
          </div>
        </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-ignore
import DiseasePatientNetwork from './DiseasePatientNetwork.vue';
// @ts-ignore
import CollapsiblePanel from './CollapsiblePanel.vue';
import * as echarts from 'echarts';

// Add data type definitions
interface DiseaseData {
  macular_edema: number;
  open_angle_glaucoma: number;
  closed_angle_glaucoma: number;
  diabetic_maculopathy: number;
  retinitis_pigmentosa: number;
  branch_retinal_vein_occlusion: number;
}

// 2. Add interfaces for patient data
interface PatientDetail {
  disease: string;
  age: number;
  gender: string;
  visits: number;
  hospitalized: number;
  indicators: Record<string, any>;
  textData: string;
  imagePath: string;
}

interface PatientData {
  id: string;
  disease: string;
  details: PatientDetail;
}

interface QuarterlyData {
  Q1: DiseaseData;
  Q2: DiseaseData;
  Q3: DiseaseData;
  Q4: DiseaseData;
}

interface MonthlyData {
  Jan: DiseaseData;
  Feb: DiseaseData;
  Mar: DiseaseData;
  Apr: DiseaseData;
  May: DiseaseData;
  Jun: DiseaseData;
  Jul: DiseaseData;
  Aug: DiseaseData;
  Sep: DiseaseData;
  Oct: DiseaseData;
  Nov: DiseaseData;
  Dec: DiseaseData;
}

export default {
  name: 'DiagnosisAssistant',
  components: {
    DiseasePatientNetwork,
    CollapsiblePanel
  },
  data() {
    return {
      // Add new property for right panel collapse state
      isRightPanelCollapsed: false,
      
      selectedCases: {
        normal: 0,
        herniated: 0,
        bulging: 0
      },
      currentCase: {
        id: '',
        label: '',
        description: ''
      },
      comparisonData: [
        {
          cardId: '',
          label: '',
          notes: ''
        },
        {
          cardId: '',
          label: '',
          notes: ''
        }
      ],
      // 添加当前屏幕状态
      currentScreen: 'first',
      // AI对话相关状态
      showAIDialog: false,
      showAIChat: false,
      activeChatTab: 'chat',
      // 聊天相关数据
      chatMessages: [
        {
          sender: 'ai',
          text: 'Hello! I am your AI diagnostic assistant. How can I help you analyze the comparison between these patients?',
          time: '12:30'
        }
      ],
      userMessage: '',
      isAiTyping: false,
      // 添加聚类选项，修改默认值为 'none'
      clusteringBy: 'none' as 'none' | 'age' | 'disease' | 'visits' | 'other', // 默认为不聚类
      // 散点图配置
      scatterOption: {
        animation: true,
        animationDuration: 1500,
        animationEasing: 'cubicOut',
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        grid: {
          left: '2%',
          right: '2%',
          top: '2%',
          bottom: '2%',
          containLabel: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            return `<div style="padding: 8px 12px; min-width: 180px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">ID: ${params.dataIndex}</div>
              <div style="margin-bottom: 3px;">Type: ${params.seriesIndex === 0 ? 'Patient' : params.seriesIndex === 1 ? 'Group B' : 'Group C'}</div>
              <div>Value: (${params.data[0].toFixed(2)}, ${params.data[1].toFixed(2)})</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100; min-width: 180px;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        series: [
          {
            type: 'scatter',
            symbolSize: function() {
              return Math.random() * 6 + 6; // 6-12 的随机大小
            },
            itemStyle: {
              opacity: 0.85,
              shadowBlur: 8,
              shadowColor: 'rgba(33, 150, 243, 0.4)',
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [{
                  offset: 0, color: '#2196f3' // 浅蓝色
                }, {
                  offset: 1, color: '#1565c0' // 深蓝色
                }]
              }
            },
            emphasis: {
              scale: true,
              itemStyle: {
                shadowBlur: 15,
                borderColor: '#fff',
                borderWidth: 2,
                opacity: 1
              }
            },
            data: this.generateRandomScatterData(400, null), // 增加点的数量
            z: 10
          },
          // 添加连接线效果
          {
            type: 'graph',
            layout: 'none',
            coordinateSystem: 'cartesian2d',
            symbolSize: [10, 20], // 添加不同大小的中心点
            symbol: 'circle',
            itemStyle: {
              color: function(params: any) {
                // 为中心点设置颜色
                if (params.dataIndex < 6) { // 前6个点是中心点
                  const colors = ['#2196f3', '#f44336', '#4caf50', '#ff9800', '#9c27b0', '#607d8b'];
                  return colors[params.dataIndex % 6];
                }
                return '#ddd';
              },
              opacity: 0.8,
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 1,
              color: '#aaa',
              opacity: 0.6,
              curveness: 0
            },
            emphasis: {
              lineStyle: {
                width: 2,
                color: '#555'
              }
            },
            data: this.generateClusterCenters(),
            edges: this.generateClusterEdges(400),
            z: 1
          }
        ]
      },
      // 柱状图配置
      barOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Value',
            type: 'bar',
            barWidth: '60%',
            data: [10, 52, 200, 334, 390, 330, 220]
          }
        ]
      },
      // 折线图配置
      lineOption: {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      // 小型柱状图配置1
      smallBarOption: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'bar',
            data: [12, 24, 36, 48, 60],
            itemStyle: {
              color: '#2196f3'
            }
          }
        ]
      },
      // 小型柱状图配置2
      smallBarOption2: {
        grid: {
          top: 10,
          right: 10,
          bottom: 20,
          left: 30
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'bar',
            data: [60, 48, 36, 24, 12],
            itemStyle: {
              color: '#4caf50'
            }
          }
        ]
      },
      // 饼图配置1
      pieOption1: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: 'Normal' },
              { value: 735, name: 'Bulging' },
              { value: 580, name: 'Herniated' }
            ]
          }
        ]
      },
      // 饼图配置2
      pieOption2: {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 500, name: 'Normal' },
              { value: 900, name: 'Bulging' },
              { value: 600, name: 'Herniated' }
            ]
          }
        ]
      },
      // 修改：Raw图像下方的图表，移除背景相关设置
      rawImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7', '8'],
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: [28, 32, 36, 34, 45, 33, 28, 24],
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            itemStyle: {
              color: '#2196f3'
            },
            lineStyle: {
              width: 2,
              color: '#2196f3'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(33, 150, 243, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(33, 150, 243, 0.05)'
                  }
                ]
              }
            }
          }
        ]
      },
      // 修改：CAM图像下方的图表，移除背景相关设置
      camImageChartOption: {
        grid: {
          top: 5,
          right: 10,
          bottom: 10,
          left: 10,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['A', 'B', 'C', 'D', 'E'],
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 8,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: '50%',
            data: [
              {
                value: 60,
                itemStyle: {
                  color: '#f44336'
                }
              },
              {
                value: 45,
                itemStyle: {
                  color: '#f44336'
                }
              },
              {
                value: 30,
                itemStyle: {
                  color: '#2196f3'
                }
              },
              {
                value: 20,
                itemStyle: {
                  color: '#2196f3'
                }
              },
              {
                value: 15,
                itemStyle: {
                  color: '#4caf50'
                }
              }
            ]
          }
        ]
      },
      // 添加新的状态变量
      isComparisonCollapsed: false,
      // 眼科疾病数据
      ophthalmicDiseases: [
        { 
          id: 'macular_edema', 
          name: 'Macular Edema', 
          patients: [
            '80145987', '80156560', '80136152', '80145988', '80156561', 
            '80136153', '80145989', '80156562', '80136154', '80145990',
            '80156563', '80136155', '80145991', '80156564', '80136156',
            '80156565', '80136157', '80145992', '80156566', '80136158',
            '80145993', '80156567', '80136159', '80145994', '80156568',
            '80136160', '80145995', '80156569', '80136161', '80145996',
            '80156570', '80136162', '80145997', '80156571', '80136163',
            '80145998', '80156572', '80136164', '80145999', '80156573'
          ],
          incidence: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
          correlations: {
            'open_angle_glaucoma': 0.3,
            'closed_angle_glaucoma': 0.2,
            'diabetic_maculopathy': 0.8,
            'retinitis_pigmentosa': 0.4,
            'branch_retinal_vein_occlusion': 0.6
          }
        },
        { 
          id: 'open_angle_glaucoma', 
          name: 'Open-Angle Glaucoma', 
          patients: [
            '80145123', '80156789', '80136234', '80145124', '80156790', 
            '80136235', '80145125', '80156791', '80136236', '80145126',
            '80156792', '80136237', '80145127', '80156793', '80136238',
            '80145128', '80156794', '80136239', '80145129', '80156795',
            '80136240', '80145130', '80156796', '80136241', '80145131',
            '80156797', '80136242', '80145132', '80156798', '80136243',
            '80145133', '80156799', '80136244', '80145134', '80156800',
            '80136245', '80145135', '80156801', '80136246', '80145136'
          ],
          incidence: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
          correlations: {
            'macular_edema': 0.3,
            'closed_angle_glaucoma': 0.7,
            'diabetic_maculopathy': 0.4,
            'retinitis_pigmentosa': 0.3,
            'branch_retinal_vein_occlusion': 0.2
          }
        },
        { 
          id: 'closed_angle_glaucoma', 
          name: 'Closed-Angle Glaucoma', 
          patients: [
            '80145111', '80156222', '80136333', '80145112', '80156223', 
            '80136334', '80145113', '80156224', '80136335', '80145114',
            '80156225', '80136336', '80145115', '80156226', '80136337',
            '80145116', '80156227', '80136338', '80145117', '80156228',
            '80136339', '80145118', '80156229', '80136340', '80145119',
            '80156230', '80136341', '80145120', '80156231', '80136342',
            '80145121', '80156232', '80136343', '80145122', '80156233'
          ],
          incidence: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
          correlations: {
            'macular_edema': 0.2,
            'open_angle_glaucoma': 0.7,
            'diabetic_maculopathy': 0.3,
            'retinitis_pigmentosa': 0.2,
            'branch_retinal_vein_occlusion': 0.1
          }
        },
        { 
          id: 'diabetic_maculopathy', 
          name: 'Diabetic Maculopathy', 
          patients: [
            '80145444', '80156555', '80136666', '80145445', '80156556', 
            '80136667', '80145446', '80156557', '80136668', '80145447',
            '80156558', '80136669', '80145448', '80156559', '80136670',
            '80145449', '80156550', '80136671', '80145450', '80156551',
            '80145451', '80156552', '80136672', '80145452', '80156553',
            '80136673', '80145453', '80156554', '80136674', '80145454',
            '80156560', '80136675', '80145455', '80156561', '80136676',
            '80145456', '80156562', '80136677', '80145457', '80156563'
          ],
          incidence: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
          correlations: {
            'macular_edema': 0.8,
            'open_angle_glaucoma': 0.4,
            'closed_angle_glaucoma': 0.3,
            'retinitis_pigmentosa': 0.2,
            'branch_retinal_vein_occlusion': 0.5
          }
        },
        { 
          id: 'retinitis_pigmentosa', 
          name: 'Retinitis Pigmentosa', 
          patients: [
            '80145777', '80156888', '80136999', '80145778', '80156889', 
            '80137000', '80145779', '80156890', '80137001', '80145780',
            '80156891', '80137002', '80145781', '80156892', '80137003',
            '80145782', '80156893', '80137004', '80145783', '80156894',
            '80137005', '80145784', '80156895', '80137006', '80145785',
            '80156896', '80137007', '80145786', '80156897', '80137008'
          ],
          incidence: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
          correlations: {
            'macular_edema': 0.4,
            'open_angle_glaucoma': 0.3,
            'closed_angle_glaucoma': 0.2,
            'diabetic_maculopathy': 0.2,
            'branch_retinal_vein_occlusion': 0.3
          }
        },
        { 
          id: 'branch_retinal_vein_occlusion', 
          name: 'Branch Retinal Vein Occlusion', 
          patients: [
            '80146001', '80157001', '80137101', '80146002', '80157002', 
            '80137102', '80146003', '80157003', '80137103', '80146004',
            '80157004', '80137104', '80146005', '80157005', '80137105',
            '80146006', '80157006', '80137106', '80146007', '80157007',
            '80137107', '80146008', '80157008', '80137108', '80146009',
            '80157009', '80137109', '80146010', '80157010', '80137110',
            '80146011', '80157011', '80137111', '80146012', '80157012'
          ],
          incidence: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
          correlations: {
            'macular_edema': 0.6,
            'open_angle_glaucoma': 0.2,
            'closed_angle_glaucoma': 0.1,
            'diabetic_maculopathy': 0.5,
            'retinitis_pigmentosa': 0.3
          }
        }
      ],
      
      // 选中的疾病用于比较
      selectedLeftDisease: 'macular_edema',
      selectedRightDisease: 'open_angle_glaucoma',
      
      // 眼科疾病图像数据
      diseaseImages: {
        macular_edema: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg'
        },
        open_angle_glaucoma: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg'
        },
        closed_angle_glaucoma: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-oct.jpg'
        },
        diabetic_maculopathy: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-oct.jpg'
        },
        retinitis_pigmentosa: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-oct.jpg'
        },
        branch_retinal_vein_occlusion: {
          fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg',
          oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-oct.jpg'
        }
      },
      
      // 当前选中的疾病图像
      currentDiseaseImages: {
        fundus: 'https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-fundus.jpg',
        oct: 'https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-oct.jpg'
      },
      
      // 选中的疾病
      selectedDisease1: 'macular_edema',
      selectedDisease2: 'open_angle_glaucoma',
      
      // 柱状图：疾病数量
      diseaseCaseCountOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: [
              'Macular\nEdema', 
              'Open-Angle\nGlaucoma', 
              'Closed-Angle\nGlaucoma',
              'Diabetic\nMaculopathy',
              'Retinitis\nPigmentosa', 
              'Branch Retinal\nVein Occlusion'
            ],
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'Number of Cases',
            nameLocation: 'middle',
            nameGap: 40
          }
        ],
        series: [
          {
            name: 'Cases',
            type: 'bar',
            barWidth: '60%',
            data: [
              {value: 20, itemStyle: {color: '#1976d2'}},
              {value: 24, itemStyle: {color: '#2196f3'}},
              {value: 17, itemStyle: {color: '#42a5f5'}},
              {value: 20, itemStyle: {color: '#64b5f6'}},
              {value: 14, itemStyle: {color: '#90caf9'}},
              {value: 17, itemStyle: {color: '#bbdefb'}}
            ],
            label: {
              show: true,
              position: 'top',
              color: '#333'
            }
          }
        ]
      },
      
      // 折线图：疾病发病趋势
      diseaseIncidenceTrendOption: {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: [
            'Macular Edema', 
            'Open-Angle Glaucoma', 
            'Closed-Angle Glaucoma',
            'Diabetic Maculopathy',
            'Retinitis Pigmentosa', 
            'Branch Retinal Vein Occlusion'
          ],
          type: 'scroll',
          orient: 'horizontal',
          bottom: 0,
          textStyle: {
            fontSize: 10
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        },
        yAxis: {
          type: 'value',
          name: 'Incidence per 100,000',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [
          {
            name: 'Macular Edema',
            type: 'line',
            data: [12, 15, 18, 22, 25, 28, 32, 30, 26, 24, 20, 18],
            itemStyle: {color: '#0d47a1'},
            lineStyle: {width: 2, color: '#0d47a1'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Open-Angle Glaucoma',
            type: 'line',
            data: [45, 48, 50, 53, 56, 60, 64, 68, 70, 72, 75, 78],
            itemStyle: {color: '#1565c0'},
            lineStyle: {width: 2, color: '#1565c0'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Closed-Angle Glaucoma',
            type: 'line',
            data: [15, 18, 20, 22, 24, 25, 26, 28, 27, 25, 23, 21],
            itemStyle: {color: '#1976d2'},
            lineStyle: {width: 2, color: '#1976d2'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Diabetic Maculopathy',
            type: 'line',
            data: [30, 32, 35, 40, 45, 50, 55, 58, 60, 62, 65, 68],
            itemStyle: {color: '#2196f3'},
            lineStyle: {width: 2, color: '#2196f3'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Retinitis Pigmentosa',
            type: 'line',
            data: [10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
            itemStyle: {color: '#64b5f6'},
            lineStyle: {width: 2, color: '#64b5f6'},
            symbol: 'circle',
            symbolSize: 8
          },
          {
            name: 'Branch Retinal Vein Occlusion',
            type: 'line',
            data: [18, 20, 22, 25, 28, 30, 32, 35, 33, 30, 28, 25],
            itemStyle: {color: '#90caf9'},
            lineStyle: {width: 2, color: '#90caf9'},
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      },
      
      // 热力图：疾病关联
      diseaseCorrelationOption: {
        tooltip: {
          position: 'top',
          formatter: function(params: any) {
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${params.name}</div>
              <div style="font-size: 13px;">Correlation: ${params.value[2].toFixed(2)}</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        animation: true,
        grid: {
          left: '2%',
          right: '7%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            'ME', 'OAG', 'CAG', 'DM', 'RP', 'BRVO'
          ],
          splitArea: {
            show: true
          },
          axisLabel: {
            fontSize: 10,
            interval: 0
          }
        },
        yAxis: {
          type: 'category',
          data: [
            'ME', 'OAG', 'CAG', 'DM', 'RP', 'BRVO'
          ],
          splitArea: {
            show: true
          },
          axisLabel: {
            fontSize: 10,
            interval: 0
          }
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: 0,
          inRange: {
            color: ['#f3f8fe', '#deeafe', '#c6dbfd', '#90c1f9', '#5ba3f4', '#1e88e5', '#0d47a1']
          }
        },
        series: [{
          name: 'Disease Correlation',
          type: 'heatmap',
          data: [
            // ME correlations
            [0, 0, 1.0], [0, 1, 0.3], [0, 2, 0.2], [0, 3, 0.8], [0, 4, 0.4], [0, 5, 0.6],
            // OAG correlations
            [1, 0, 0.3], [1, 1, 1.0], [1, 2, 0.7], [1, 3, 0.4], [1, 4, 0.3], [1, 5, 0.2],
            // CAG correlations
            [2, 0, 0.2], [2, 1, 0.7], [2, 2, 1.0], [2, 3, 0.3], [2, 4, 0.2], [2, 5, 0.1],
            // DM correlations
            [3, 0, 0.8], [3, 1, 0.4], [3, 2, 0.3], [3, 3, 1.0], [3, 4, 0.2], [3, 5, 0.5],
            // RP correlations
            [4, 0, 0.4], [4, 1, 0.3], [4, 2, 0.2], [4, 3, 0.2], [4, 4, 1.0], [4, 5, 0.3],
            // BRVO correlations
            [5, 0, 0.6], [5, 1, 0.2], [5, 2, 0.1], [5, 3, 0.5], [5, 4, 0.3], [5, 5, 1.0]
          ],
          label: {
            show: true,
            formatter: function(params: any) {
              return params.data[2].toFixed(1);
            },
            fontSize: 10,
            color: function(params: any) {
              return params.data[2] > 0.5 ? '#fff' : '#333';
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      },
      
      // 添加患者详细数据
      patientDetails: {
        // 黄斑水肿患者
        '80145987': {
          disease: 'macular_edema',
          age: 65,
          gender: 'Male',
          visits: 5,
          hospitalized: 1,
          indicators: {
            visual_acuity: 0.4,
            intraocular_pressure: 18,
            central_macular_thickness: 450,
            hba1c: 7.8,
            blood_pressure: '145/90'
          },
          textData: 'Patient presents with decreased visual acuity and central vision distortion. OCT shows increased retinal thickness with intraretinal cysts. History of type 2 diabetes for 12 years with suboptimal glycemic control.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg'
        },
        '80156560': {
          disease: 'macular_edema',
          age: 58,
          gender: 'Female',
          visits: 7,
          hospitalized: 2,
          indicators: {
            visual_acuity: 0.5,
            intraocular_pressure: 17,
            central_macular_thickness: 380,
            hba1c: 8.2,
            blood_pressure: '138/85'
          },
          textData: 'Patient complains of blurry vision in both eyes, worse in the morning. Fundus examination reveals hard exudates and microaneurysms. Patient has hypertension and poorly controlled diabetes.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg'
        },
        
        // 开角型青光眼患者
        '80145123': {
          disease: 'open_angle_glaucoma',
          age: 72,
          gender: 'Male',
          visits: 3,
          hospitalized: 0,
          indicators: {
            visual_acuity: 0.7,
            intraocular_pressure: 28,
            cup_to_disc_ratio: 0.8,
            visual_field_md: -12.5,
            corneal_thickness: 520
          },
          textData: 'Patient reports gradual peripheral vision loss. Examination shows increased intraocular pressure and optic nerve cupping. Family history of glaucoma in father and paternal grandmother.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg'
        },
        '80156789': {
          disease: 'open_angle_glaucoma',
          age: 68,
          gender: 'Female',
          visits: 4,
          hospitalized: 0,
          indicators: {
            visual_acuity: 0.8,
            intraocular_pressure: 25,
            cup_to_disc_ratio: 0.7,
            visual_field_md: -8.3,
            corneal_thickness: 535
          },
          textData: 'Patient is asymptomatic but was referred after routine eye exam showed elevated intraocular pressure. Gonioscopy confirms open angles. OCT shows thinning of the retinal nerve fiber layer.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg'
        },
        
        // 闭角型青光眼患者
        '80145111': {
          disease: 'closed_angle_glaucoma',
          age: 62,
          gender: 'Female',
          visits: 6,
          hospitalized: 3,
          indicators: {
            visual_acuity: 0.3,
            intraocular_pressure: 42,
            anterior_chamber_depth: 2.1,
            corneal_edema: 'Moderate',
            pupil_reaction: 'Sluggish'
          },
          textData: 'Patient presented with acute eye pain, headache, and nausea. Examination showed mid-dilated pupil, corneal edema, and shallow anterior chamber. Emergency iridotomy was performed.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg'
        },
        
        // 糖尿病性黄斑病变患者
        '80145444': {
          disease: 'diabetic_maculopathy',
          age: 55,
          gender: 'Male',
          visits: 8,
          hospitalized: 2,
          indicators: {
            visual_acuity: 0.4,
            hba1c: 9.2,
            microaneurysms: 'Numerous',
            hard_exudates: 'Present',
            macular_edema: 'Clinically significant'
          },
          textData: 'Patient with 15-year history of type 2 diabetes presents with gradually worsening vision. Fundus photography shows multiple microaneurysms, dot and blot hemorrhages, and hard exudates in a circinate pattern.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg'
        },
        
        // 视网膜色素变性患者
        '80145777': {
          disease: 'retinitis_pigmentosa',
          age: 42,
          gender: 'Male',
          visits: 12,
          hospitalized: 1,
          indicators: {
            visual_acuity: 0.6,
            visual_field: 'Constricted',
            erg_amplitude: 'Reduced',
            bone_spicules: 'Present',
            night_vision: 'Severely impaired'
          },
          textData: 'Patient reports difficulty seeing in dim light and progressive loss of peripheral vision. Fundus examination reveals bone spicule pigmentation, attenuated vessels, and waxy pallor of the optic disc.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg'
        },
        
        // 视网膜分支静脉阻塞患者
        '80146001': {
          disease: 'branch_retinal_vein_occlusion',
          age: 68,
          gender: 'Female',
          visits: 9,
          hospitalized: 4,
          indicators: {
            visual_acuity: 0.5,
            intraocular_pressure: 19,
            hemorrhages: 'Flame-shaped',
            cotton_wool_spots: 'Present',
            macular_edema: 'Present'
          },
          textData: 'Patient noticed sudden, painless vision loss in the right eye 2 weeks ago. Fundus examination shows flame-shaped hemorrhages in the superotemporal quadrant with cotton wool spots. Patient has history of hypertension.',
          imagePath: 'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg'
        }
      } as { [key: string]: any },
      
      // 当前选择的患者用于对比
      selectedPatient1: '80145987', // 默认为第一个黄斑水肿患者
      selectedPatient2: '80145123', // 默认为第一个开角型青光眼患者
      
      // SHAP图配置
      shapChartOption1: null as any,
      shapChartOption2: null as any,
      
      // 添加可视化模式切换状态
      activeVisualizationMode: 'count',
      
      // 添加季度数据
      quarterlyData: {
        'Q1': {
          'macular_edema': 15,
          'open_angle_glaucoma': 49,
          'closed_angle_glaucoma': 17,
          'diabetic_maculopathy': 32,
          'retinitis_pigmentosa': 11,
          'branch_retinal_vein_occlusion': 20
        },
        'Q2': {
          'macular_edema': 25,
          'open_angle_glaucoma': 56,
          'closed_angle_glaucoma': 25,
          'diabetic_maculopathy': 45,
          'retinitis_pigmentosa': 15,
          'branch_retinal_vein_occlusion': 28
        },
        'Q3': {
          'macular_edema': 30,
          'open_angle_glaucoma': 68,
          'closed_angle_glaucoma': 27,
          'diabetic_maculopathy': 58,
          'retinitis_pigmentosa': 19,
          'branch_retinal_vein_occlusion': 33
        },
        'Q4': {
          'macular_edema': 18,
          'open_angle_glaucoma': 75,
          'closed_angle_glaucoma': 21,
          'diabetic_maculopathy': 65,
          'retinitis_pigmentosa': 22,
          'branch_retinal_vein_occlusion': 25
        }
      } as QuarterlyData,
      
      // 添加月度数据
      monthlyData: {
        'Jan': {
          'macular_edema': 12,
          'open_angle_glaucoma': 45,
          'closed_angle_glaucoma': 15,
          'diabetic_maculopathy': 30,
          'retinitis_pigmentosa': 10,
          'branch_retinal_vein_occlusion': 18
        },
        'Feb': {
          'macular_edema': 15,
          'open_angle_glaucoma': 48,
          'closed_angle_glaucoma': 18,
          'diabetic_maculopathy': 32,
          'retinitis_pigmentosa': 12,
          'branch_retinal_vein_occlusion': 20
        },
        'Mar': {
          'macular_edema': 18,
          'open_angle_glaucoma': 50,
          'closed_angle_glaucoma': 20,
          'diabetic_maculopathy': 35,
          'retinitis_pigmentosa': 13,
          'branch_retinal_vein_occlusion': 22
        },
        'Apr': {
          'macular_edema': 22,
          'open_angle_glaucoma': 53,
          'closed_angle_glaucoma': 22,
          'diabetic_maculopathy': 40,
          'retinitis_pigmentosa': 14,
          'branch_retinal_vein_occlusion': 25
        },
        'May': {
          'macular_edema': 25,
          'open_angle_glaucoma': 56,
          'closed_angle_glaucoma': 24,
          'diabetic_maculopathy': 45,
          'retinitis_pigmentosa': 15,
          'branch_retinal_vein_occlusion': 28
        },
        'Jun': {
          'macular_edema': 28,
          'open_angle_glaucoma': 60,
          'closed_angle_glaucoma': 25,
          'diabetic_maculopathy': 50,
          'retinitis_pigmentosa': 16,
          'branch_retinal_vein_occlusion': 30
        },
        'Jul': {
          'macular_edema': 32,
          'open_angle_glaucoma': 64,
          'closed_angle_glaucoma': 26,
          'diabetic_maculopathy': 55,
          'retinitis_pigmentosa': 17,
          'branch_retinal_vein_occlusion': 32
        },
        'Aug': {
          'macular_edema': 30,
          'open_angle_glaucoma': 68,
          'closed_angle_glaucoma': 28,
          'diabetic_maculopathy': 58,
          'retinitis_pigmentosa': 18,
          'branch_retinal_vein_occlusion': 35
        },
        'Sep': {
          'macular_edema': 26,
          'open_angle_glaucoma': 70,
          'closed_angle_glaucoma': 27,
          'diabetic_maculopathy': 60,
          'retinitis_pigmentosa': 19,
          'branch_retinal_vein_occlusion': 33
        },
        'Oct': {
          'macular_edema': 24,
          'open_angle_glaucoma': 72,
          'closed_angle_glaucoma': 25,
          'diabetic_maculopathy': 62,
          'retinitis_pigmentosa': 20,
          'branch_retinal_vein_occlusion': 30
        },
        'Nov': {
          'macular_edema': 20,
          'open_angle_glaucoma': 75,
          'closed_angle_glaucoma': 23,
          'diabetic_maculopathy': 65,
          'retinitis_pigmentosa': 21,
          'branch_retinal_vein_occlusion': 28
        },
        'Dec': {
          'macular_edema': 18,
          'open_angle_glaucoma': 78,
          'closed_angle_glaucoma': 21,
          'diabetic_maculopathy': 68,
          'retinitis_pigmentosa': 22,
          'branch_retinal_vein_occlusion': 25
        }
      } as MonthlyData,
      
      seasonalViewMode: 'quarter',
      
      // 患者趋势图配置
      patientTrend1Option: {},
      patientTrend2Option: {},
    }
  },
  computed: {
    // 处理用于知识图谱的疾病数据
    selectedDiseases() {
      return [
        { 
          id: this.selectedDisease1, 
          name: this.formatDiseaseName(this.selectedDisease1), 
          color: this.getDiseaseColor(this.selectedDisease1)
        },
        { 
          id: this.selectedDisease2, 
          name: this.formatDiseaseName(this.selectedDisease2), 
          color: this.getDiseaseColor(this.selectedDisease2)
        }
      ];
    },
    // 处理用于知识图谱的患者数据
    selectedPatients() {
      const patients: Array<{id: string, diseaseId: string}> = [];
      
      // 添加疾病1的患者
      const disease1Patients = this.getPatientsForDisease(this.selectedDisease1);
      disease1Patients.forEach(patientId => {
        patients.push({
          id: patientId,
          diseaseId: this.selectedDisease1
        });
      });
      
      // 添加疾病2的患者
      const disease2Patients = this.getPatientsForDisease(this.selectedDisease2);
      disease2Patients.forEach(patientId => {
        patients.push({
          id: patientId,
          diseaseId: this.selectedDisease2
        });
      });
      
      return patients;
    },
    // 添加组合图表选项
    activeCombinedChartOption(): any {
      // 根据当前模式返回不同的图表配置
      return this.activeVisualizationMode === 'count' 
        ? this.diseaseCaseCountOption 
        : this.diseaseIncidenceTrendOption;
    },
    // 添加季节分布图表选项
    seasonalDistributionOption(): any {
      // 根据视图模式确定数据源和X轴类别
      const isQuarterView = this.seasonalViewMode === 'quarter';
      const categories = isQuarterView 
        ? ['Q1', 'Q2', 'Q3', 'Q4'] 
        : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      // 准备数据源
      const dataSource = isQuarterView ? this.quarterlyData : this.monthlyData;
      
      // 创建安全的数据访问函数，处理类型问题
      const getDiseaseValue = (category: string, disease: string): number => {
        if (isQuarterView) {
          const quarterData = this.quarterlyData[category as keyof QuarterlyData];
          return quarterData?.[disease as keyof DiseaseData] || 0;
        } else {
          const monthData = this.monthlyData[category as keyof MonthlyData];
          return monthData?.[disease as keyof DiseaseData] || 0;
        }
      };
      
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: [
            'Macular Edema', 
            'Open-Angle Glaucoma', 
            'Closed-Angle Glaucoma',
            'Diabetic Maculopathy',
            'Retinitis Pigmentosa',
            'Branch Retinal Vein Occlusion'
          ],
          textStyle: {
            fontSize: 10
          },
          top: 'bottom',
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '20%',
          top: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: categories,
            axisLabel: {
              interval: 0,
              rotate: isQuarterView ? 0 : 30,
              fontSize: isQuarterView ? 12 : 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'Cases',
            nameLocation: 'middle',
            nameGap: 30
          }
        ],
        series: [
          {
            name: 'Macular Edema',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'macular_edema')),
            itemStyle: {color: '#0d47a1'}
          },
          {
            name: 'Open-Angle Glaucoma',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'open_angle_glaucoma')),
            itemStyle: {color: '#1565c0'}
          },
          {
            name: 'Closed-Angle Glaucoma',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'closed_angle_glaucoma')),
            itemStyle: {color: '#1976d2'}
          },
          {
            name: 'Diabetic Maculopathy',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'diabetic_maculopathy')),
            itemStyle: {color: '#2196f3'}
          },
          {
            name: 'Retinitis Pigmentosa',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'retinitis_pigmentosa')),
            itemStyle: {color: '#64b5f6'}
          },
          {
            name: 'Branch Retinal Vein Occlusion',
            type: 'bar',
            stack: 'total',
            emphasis: {
              focus: 'series'
            },
            data: categories.map(cat => getDiseaseValue(cat, 'branch_retinal_vein_occlusion')),
            itemStyle: {color: '#90caf9'}
          }
        ]
      };
    }
  },
  methods: {
    // 添加切换屏幕方法
    toggleScreen() {
      this.currentScreen = this.currentScreen === 'first' ? 'detail' : 'first';
      // 滚动到顶部
      this.$nextTick(() => {
        window.scrollTo(0, 0);
        
        // 如果切换到detail视图，确保知识图谱正确渲染
        if (this.currentScreen === 'detail') {
          // 给图表一点时间来渲染
          setTimeout(() => {
            // 触发窗口resize事件，让echarts图表自动调整大小
            window.dispatchEvent(new Event('resize'));
          }, 300);
        }
      });
    },
    // 生成随机散点数据
    generateRandomScatterData(count: number) {
      const data = [];
      
      // 检查聚类方式，根据不同的聚类方式生成不同的数据
      switch (this.clusteringBy) {
        case 'none':
          // 默认不聚类，所有点都是蓝色并分布在整个图表上
      for (let i = 0; i < count; i++) {
            // 为了创建更均匀的分布，使用随机角度和距离从中心点向外分散
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 4; // 0到4的距离
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: '#2196f3' // 所有点都是蓝色
              }
            });
          }
          break;
        
        case 'age':
          // 按年龄段聚类: 青年(18-40)，中年(41-60)，老年(61+)
          const ageGroups = [
            { name: '18-40', x: -3, y: -3, radius: 2, color: '#2196f3' },
            { name: '41-60', x: 0, y: 0, radius: 2.5, color: '#f44336' },
            { name: '61+', x: 3, y: 3, radius: 2, color: '#4caf50' }
          ];
          
          for (let i = 0; i < count; i++) {
            const group = ageGroups[Math.floor(Math.random() * ageGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y]  // 保存簇中心点坐标
            });
          }
          break;
          
        case 'disease':
          // 按疾病类型聚类
          const diseaseGroups = [
            { name: 'Macular Edema', x: -4, y: 0, radius: 1.5, color: '#2196f3' },
            { name: 'Open-Angle Glaucoma', x: -2, y: 2, radius: 1.5, color: '#f44336' },
            { name: 'Closed-Angle Glaucoma', x: 0, y: -2, radius: 1.5, color: '#4caf50' },
            { name: 'Diabetic Maculopathy', x: 2, y: 2, radius: 1.5, color: '#ff9800' },
            { name: 'Retinitis Pigmentosa', x: 4, y: 0, radius: 1.5, color: '#9c27b0' },
            { name: 'Branch Retinal Vein Occlusion', x: 0, y: 3, radius: 1.5, color: '#607d8b' }
          ];
          
          for (let i = 0; i < count; i++) {
            const group = diseaseGroups[Math.floor(Math.random() * diseaseGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y]  // 保存簇中心点坐标
            });
          }
          break;
          
        case 'visits':
          // 按就诊次数聚类: 首诊, 随访1-3次, 随访4次以上
          const visitGroups = [
            { name: 'First Visit', x: -3, y: 0, radius: 2, color: '#2196f3' },
            { name: '1-3 Visits', x: 0, y: 0, radius: 2, color: '#f44336' },
            { name: '4+ Visits', x: 3, y: 0, radius: 2, color: '#4caf50' }
          ];
          
          for (let i = 0; i < count; i++) {
            const group = visitGroups[Math.floor(Math.random() * visitGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y]  // 保存簇中心点坐标
            });
          }
          break;
          
        case 'other':
          // 按其他属性聚类：性别、地理区域
          const otherGroups = [
            { name: 'Male', x: -2, y: -2, radius: 1.5, color: '#2196f3' },
            { name: 'Female', x: 2, y: -2, radius: 1.5, color: '#e91e63' },
            { name: 'Urban', x: 0, y: 2, radius: 1.5, color: '#ff9800' },
            { name: 'Rural', x: 0, y: -2, radius: 1.5, color: '#4caf50' }
          ];
          
          for (let i = 0; i < count; i++) {
            const group = otherGroups[Math.floor(Math.random() * otherGroups.length)];
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * group.radius;
            const x = group.x + Math.cos(angle) * radius;
            const y = group.y + Math.sin(angle) * radius;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color
              },
              groupName: group.name,
              clusterCenter: [group.x, group.y]  // 保存簇中心点坐标
            });
          }
          break;
          
        default:
          // 创建默认簇
          const clusters = [
            { x: -3, y: -3, radius: 2 },
            { x: 0, y: 0, radius: 3 },
            { x: 3, y: 3, radius: 2.5 }
          ];
          
          for (let i = 0; i < count; i++) {
            // 随机选择一个簇
            const cluster = clusters[Math.floor(Math.random() * clusters.length)];
            // 在簇周围生成点
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * cluster.radius;
            const x = cluster.x + Math.cos(angle) * radius;
            const y = cluster.y + Math.sin(angle) * radius;
            
            data.push([x, y]);
          }
          break;
      }
      
      return data;
    },
    
    // 生成从聚类中心到各点的连接线
    generateClusterLinks(scatterData: any[]): any[] {
      const links: any[] = [];
      
      // 如果是'none'聚类方式，则不生成连接线
      if (this.clusteringBy === 'none') {
        return links;
      }
      
      // 为每个数据点创建一个指向其聚类中心的连接线
      scatterData.forEach((point: any, index: number) => {
        // 检查点是否有聚类中心信息
        if (point.clusterCenter) {
          // 创建一个虚拟的中心点（用于连线，不实际显示）
          const centerPointIndex = -10000 - index; // 使用负值确保不与实际点冲突
          
          // 添加中心点
          links.push({
            source: centerPointIndex,
            target: index,
            lineStyle: {
              color: point.itemStyle.color, // 使用与点相同的颜色
              width: 0.8,
              opacity: 0.5,
              curveness: 0 // 直线连接
            }
          });
        }
      });
      
      return links;
    },
    
    // 添加设置聚类方式的方法
    setClusteringBy(clusterType: 'none' | 'age' | 'disease' | 'visits' | 'other') {
      this.clusteringBy = clusterType;
      
      // 更新散点图和连接线
      this.updateScatterChart();
      
      // 重新生成主散点图数据
      this.scatterOption.series[0].data = this.generateRandomScatterData(400);
      
      // 更新连接线
      if (this.scatterOption && this.scatterOption.series && this.scatterOption.series.length >= 2) {
        this.scatterOption.series[1].data = this.generateClusterCenters();
        this.scatterOption.series[1].edges = this.generateClusterEdges(400);
        
        // 根据是否聚类显示或隐藏中心点和连接线
        if (clusterType === 'none') {
          // 不聚类时隐藏中心点和连接线
          this.scatterOption.series[1].symbolSize = [0, 0];
          if (this.scatterOption.series[1].lineStyle) {
            this.scatterOption.series[1].lineStyle.opacity = 0;
          }
        } else {
          // 聚类时显示中心点和连接线
          this.scatterOption.series[1].symbolSize = [10, 20]; 
          if (this.scatterOption.series[1].lineStyle) {
            this.scatterOption.series[1].lineStyle.opacity = 0.6;
          }
        }
      }
    },
    
    // 更新散点图
    updateScatterChart() {
      // 根据不同的聚类方式更新图表的数据和图例
      const newData: any[] = [];
      const legendData: string[] = [];
      
      // 生成实际数据点
      const scatterData = this.generateScatterData();
      
      // 生成连接线
      const clusterLinks = this.generateClusterLinks(scatterData);
      
      // 将中心点添加到数据中（用于连接线，但不显示）
      const centerPoints: any[] = [];
      scatterData.forEach((point, index) => {
        if (point.clusterCenter) {
          centerPoints.push({
            id: -10000 - index, // 与links中的source对应
            value: point.clusterCenter,
            symbolSize: 0, // 大小为0，不显示
          });
        }
      });
      
      // 根据聚类方式添加图例
      switch (this.clusteringBy) {
        case 'none':
          // 不聚类，所有点都是蓝色
          newData.push({
            name: 'All Patients',
            type: 'scatter',
            symbolSize: 10,
            itemStyle: {
              opacity: 0.85,
              shadowBlur: 8,
              shadowColor: 'rgba(33, 150, 243, 0.4)'
            },
            emphasis: {
              scale: true,
              itemStyle: {
                shadowBlur: 15,
                borderColor: '#fff',
                borderWidth: 2,
                opacity: 1
              }
            },
            data: scatterData,
            z: 10
          });
          break;
          
        case 'age':
          // 按年龄段聚类
          const ageGroups = [
            { name: '18-40', color: '#2196f3' },
            { name: '41-60', color: '#f44336' },
            { name: '61+', color: '#4caf50' }
          ];
          
          ageGroups.forEach(group => {
            legendData.push(group.name);
            
            // 筛选属于该年龄组的点
            const groupData = scatterData.filter(item => item.groupName === group.name);
            
            newData.push({
              name: group.name,
              type: 'scatter',
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: '#fff',
                  borderWidth: 2,
                  opacity: 1
                }
              },
              data: groupData,
              z: 10
            });
          });
          break;
          
        case 'disease':
          // 按疾病类型聚类
          const diseaseGroups = [
            { name: 'Macular Edema', color: '#2196f3' },
            { name: 'Open-Angle Glaucoma', color: '#f44336' },
            { name: 'Closed-Angle Glaucoma', color: '#4caf50' },
            { name: 'Diabetic Maculopathy', color: '#ff9800' },
            { name: 'Retinitis Pigmentosa', color: '#9c27b0' },
            { name: 'Branch Retinal Vein Occlusion', color: '#607d8b' }
          ];
          
          diseaseGroups.forEach(group => {
            legendData.push(group.name);
            
            // 筛选属于该疾病组的点
            const groupData = scatterData.filter(item => item.groupName === group.name);
            
            newData.push({
              name: group.name,
              type: 'scatter',
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: '#fff',
                  borderWidth: 2,
                  opacity: 1
                }
              },
              data: groupData,
              z: 10
            });
          });
          break;
          
        case 'visits':
          // 按就诊次数聚类
          const visitGroups = [
            { name: 'First Visit', color: '#2196f3' },
            { name: '1-3 Visits', color: '#f44336' },
            { name: '4+ Visits', color: '#4caf50' }
          ];
          
          visitGroups.forEach(group => {
            legendData.push(group.name);
            
            // 筛选属于该就诊组的点
            const groupData = scatterData.filter(item => item.groupName === group.name);
            
            newData.push({
              name: group.name,
              type: 'scatter',
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: '#fff',
                  borderWidth: 2,
                  opacity: 1
                }
              },
              data: groupData,
              z: 10
            });
          });
          break;
          
        case 'other':
          // 按性别聚类
          const genderGroups = [
            { name: 'Male', color: '#2196f3' },
            { name: 'Female', color: '#e91e63' }
          ];
          
          genderGroups.forEach(group => {
            legendData.push(group.name);
            
            // 筛选属于该性别组的点
            const groupData = scatterData.filter(item => item.groupName === group.name);
            
            newData.push({
              name: group.name,
              type: 'scatter',
              symbolSize: 10,
              itemStyle: {
                opacity: 0.85,
                shadowBlur: 8,
                shadowColor: `rgba(${this.hexToRgb(group.color)}, 0.4)`
              },
              emphasis: {
                scale: true,
                itemStyle: {
                  shadowBlur: 15,
                  borderColor: '#fff',
                  borderWidth: 2,
                  opacity: 1
                }
              },
              data: groupData,
              z: 10
            });
          });
          break;
      }
      
      // 更新图表选项，正确处理legend属性
      const options: any = {
        animation: true,
        animationDuration: 1500,
        animationEasing: 'cubicOut',
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        grid: {
          left: '2%',
          right: '2%',
          top: '2%',
          bottom: '2%',
          containLabel: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            // 检查是否有patientId字段
            if (params.data && params.data.patientId) {
              return `<div style="padding: 8px 12px; min-width: 180px;">
                <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">Patient ID: ${params.data.patientId}</div>
                <div style="margin-bottom: 3px;">Disease: ${params.data.groupName || params.seriesName}</div>
                <div>Click to view patient details</div>
              </div>`;
            }
            
            return `<div style="padding: 8px 12px; min-width: 180px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">ID: ${params.dataIndex}</div>
              <div>Value: (${params.data[0].toFixed(2)}, ${params.data[1].toFixed(2)})</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100; min-width: 180px;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        series: newData
      };
      
      // 只有在非'none'模式下才添加legend
      if (this.clusteringBy !== 'none') {
        options.legend = {
          data: legendData,
          type: 'scroll',
          orient: 'horizontal',
          bottom: 5,
          textStyle: {
            fontSize: 10
          },
          itemWidth: 10,
          itemHeight: 10,
          pageIconSize: 12
        };
        
        // 添加连接线的图表
        options.series.push({
          type: 'graph',
          layout: 'none',
          coordinateSystem: 'cartesian2d',
          symbolSize: 0,
          // 添加中心点数据（但不显示）
          data: centerPoints,
          // 添加从中心点到各数据点的连接
          edges: clusterLinks,
          z: 5,
          tooltip: {
            show: false
          }
        });
      }
      
      this.scatterOption = options;
    },
    
    // 生成真实患者的散点数据
    generateScatterData() {
      const data: any[] = [];
      
      // 从疾病数据中获取所有患者，优先考虑选中的疾病
      const allPatients: PatientData[] = [];
      
      // 首先添加选中的疾病患者
      const selectedDiseases = [this.selectedDisease1, this.selectedDisease2].filter(d => d);
      
      // 如果有选中的疾病，优先显示这些疾病的患者
      if (selectedDiseases.length > 0) {
        selectedDiseases.forEach(diseaseId => {
          const disease = this.ophthalmicDiseases.find(d => d.id === diseaseId);
          if (disease) {
            disease.patients.forEach(patientId => {
              if (this.patientDetails[patientId]) {
                allPatients.push({
                  id: patientId,
                  disease: disease.id,
                  details: this.patientDetails[patientId]
                });
              }
            });
          }
        });
      } 
      
      // 如果没有选中疾病或者需要显示更多患者，添加其他疾病的患者
      if (allPatients.length < 50) {
        this.ophthalmicDiseases.forEach(disease => {
          // 如果不是已选中的疾病，添加其患者
          if (!selectedDiseases.includes(disease.id)) {
            disease.patients.forEach(patientId => {
              if (this.patientDetails[patientId] && allPatients.length < 100) {
                allPatients.push({
                  id: patientId,
                  disease: disease.id,
                  details: this.patientDetails[patientId]
                });
              }
            });
          }
        });
      }
      
      // 检查聚类方式，根据不同的聚类方式生成不同的数据
      switch (this.clusteringBy) {
        case 'none':
          // 默认不聚类，所有点都是蓝色随机分布
          allPatients.forEach((patient: PatientData) => {
            // 使用更自然的随机分布
            // 使用高斯分布而不是均匀分布，使点更自然地聚集
            // Box-Muller变换生成正态分布的随机数
            const u = 1 - Math.random(); // 避免对数中的0
            const v = 1 - Math.random();
            const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
            
            // 缩放和偏移以适应可视区域
            const x = z * 1.5;
            const y = (Math.sqrt(-2.0 * Math.log(u)) * Math.sin(2.0 * Math.PI * v)) * 1.5;
            
            // 所有点都使用蓝色
            const color = '#2196f3'; // 统一蓝色
            
            data.push({
              value: [x, y],
            itemStyle: {
              color: color
              },
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease
            });
          });
          break;
        
        case 'age':
          // 按年龄段聚类: 青年(18-40)，中年(41-60)，老年(61+)
          const ageGroups = [
            { name: '18-40', x: -3, y: -3, radius: 2, color: '#2196f3' },
            { name: '41-60', x: 0, y: 3, radius: 2, color: '#f44336' },
            { name: '61+', x: 3, y: -3, radius: 2, color: '#4caf50' }
          ];
          
          allPatients.forEach((patient: PatientData) => {
            const age = patient.details.age;
            let groupIndex;
            
            if (age <= 40) {
              groupIndex = 0; // 青年
            } else if (age <= 60) {
              groupIndex = 1; // 中年
            } else {
              groupIndex = 2; // 老年
            }
            
            const group = ageGroups[groupIndex];
            
            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;
            
            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;
            
            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;
            
            // 如果是选中的疾病，使用不同的标记样式
            let symbolSize = 10;
            if (patient.disease === this.selectedDisease1 || patient.disease === this.selectedDisease2) {
              symbolSize = 14;
            }
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease
            });
          });
          break;
          
        case 'disease':
          // 按疾病类型聚类，使用六边形布局避免直线
          const diseaseGroups = [
            { id: 'macular_edema', name: 'Macular Edema', x: -3, y: 0, radius: 2, color: '#2196f3' },
            { id: 'open_angle_glaucoma', name: 'Open-Angle Glaucoma', x: -1.5, y: 2.6, radius: 2, color: '#f44336' },
            { id: 'closed_angle_glaucoma', name: 'Closed-Angle Glaucoma', x: 1.5, y: 2.6, radius: 2, color: '#4caf50' },
            { id: 'diabetic_maculopathy', name: 'Diabetic Maculopathy', x: 3, y: 0, radius: 2, color: '#ff9800' },
            { id: 'retinitis_pigmentosa', name: 'Retinitis Pigmentosa', x: 1.5, y: -2.6, radius: 2, color: '#9c27b0' },
            { id: 'branch_retinal_vein_occlusion', name: 'Branch Retinal Vein Occlusion', x: -1.5, y: -2.6, radius: 2, color: '#607d8b' }
          ];
          
          // 按疾病分组
          const patientsByDisease: Record<string, PatientData[]> = {};
          allPatients.forEach(patient => {
            if (!patientsByDisease[patient.disease]) {
              patientsByDisease[patient.disease] = [];
            }
            patientsByDisease[patient.disease].push(patient);
          });
          
          // 为每种疾病的患者创建点
          diseaseGroups.forEach(diseaseGroup => {
            const diseasePatients = patientsByDisease[diseaseGroup.id] || [];
            
            // 高亮显示选中的疾病
            const isSelected = diseaseGroup.id === this.selectedDisease1 || diseaseGroup.id === this.selectedDisease2;
            const groupRadius = isSelected ? diseaseGroup.radius * 1.2 : diseaseGroup.radius;
            
            diseasePatients.forEach((patient: PatientData) => {
              // 使用高斯分布创建更自然的聚类
              const u = 1 - Math.random();
              const v = 1 - Math.random();
              const r = Math.sqrt(-2.0 * Math.log(u));
              const theta = 2.0 * Math.PI * v;
              
              // 缩放随机值以适应聚类半径
              const xOffset = r * Math.cos(theta) * groupRadius * 0.5;
              const yOffset = r * Math.sin(theta) * groupRadius * 0.5;
              
              // 添加一些随机抖动以打破任何可能的模式
              const jitter = 0.3;
              const x = diseaseGroup.x + xOffset + (Math.random() - 0.5) * jitter;
              const y = diseaseGroup.y + yOffset + (Math.random() - 0.5) * jitter;
              
              // 如果是选中的疾病，使用更大的点
              const symbolSize = isSelected ? 14 : 10;
              
              data.push({
                value: [x, y],
                itemStyle: {
                  color: diseaseGroup.color,
                  opacity: isSelected ? 1 : 0.8
                },
                symbolSize: symbolSize,
                groupName: diseaseGroup.name,
                name: `Patient ${patient.id}`,
                patientId: patient.id,
                diseaseId: patient.disease
              });
            });
          });
          break;
          
        case 'visits':
          // 按就诊次数聚类: 首诊, 随访1-3次, 随访4次以上
          const visitGroups = [
            { name: 'First Visit', x: -3, y: 0, radius: 2, color: '#2196f3' },
            { name: '1-3 Visits', x: 0, y: 2.6, radius: 2, color: '#f44336' },
            { name: '4+ Visits', x: 3, y: 0, radius: 2, color: '#4caf50' }
          ];
          
          allPatients.forEach((patient: PatientData) => {
            const visits = patient.details.visits || 1;
            let groupIndex;
            
            if (visits === 1) {
              groupIndex = 0; // 首诊
            } else if (visits <= 3) {
              groupIndex = 1; // 随访1-3次
            } else {
              groupIndex = 2; // 随访4次以上
            }
            
            const group = visitGroups[groupIndex];
            
            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;
            
            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;
            
            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;
            
            // 如果是选中的疾病，使用不同的标记样式
            const isSelectedDisease = patient.disease === this.selectedDisease1 || patient.disease === this.selectedDisease2;
            const symbolSize = isSelectedDisease ? 14 : 10;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
                borderColor: isSelectedDisease ? '#000' : 'transparent',
                borderWidth: isSelectedDisease ? 1 : 0
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease
            });
          });
          break;
          
        case 'other':
          // 按性别聚类
          const genderGroups = [
            { name: 'Male', x: -2, y: 0, radius: 2, color: '#2196f3' },
            { name: 'Female', x: 2, y: 0, radius: 2, color: '#e91e63' }
          ];
          
          allPatients.forEach((patient: PatientData) => {
            const gender = patient.details.gender;
            const groupIndex = gender === 'Male' ? 0 : 1;
            const group = genderGroups[groupIndex];
            
            // 使用高斯分布创建更自然的聚类
            const u = 1 - Math.random();
            const v = 1 - Math.random();
            const r = Math.sqrt(-2.0 * Math.log(u));
            const theta = 2.0 * Math.PI * v;
            
            // 缩放随机值以适应聚类半径
            const xOffset = r * Math.cos(theta) * group.radius * 0.5;
            const yOffset = r * Math.sin(theta) * group.radius * 0.5;
            
            // 添加一些随机抖动以打破任何可能的模式
            const jitter = 0.3;
            const x = group.x + xOffset + (Math.random() - 0.5) * jitter;
            const y = group.y + yOffset + (Math.random() - 0.5) * jitter;
            
            // 如果是选中的疾病，使用不同的标记样式
            const isSelectedDisease = patient.disease === this.selectedDisease1 || patient.disease === this.selectedDisease2;
            const symbolSize = isSelectedDisease ? 14 : 10;
            
            data.push({
              value: [x, y],
              itemStyle: {
                color: group.color,
                borderColor: isSelectedDisease ? '#000' : 'transparent',
                borderWidth: isSelectedDisease ? 1 : 0
              },
              symbolSize: symbolSize,
              groupName: group.name,
              name: `Patient ${patient.id}`,
              patientId: patient.id,
              diseaseId: patient.disease
            });
          });
          break;
      }
      
      return data;
    },
    
    // 将hex颜色转换为RGB格式
    hexToRgb(hex: string) {
      // 移除#
      hex = hex.replace('#', '');
      
      // 解析r,g,b
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      
      return `${r}, ${g}, ${b}`;
    },
    
    // 加深颜色
    darkenColor(hex: string) {
      // 移除#
      hex = hex.replace('#', '');
      
      // 解析r,g,b
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);
      
      // 降低颜色值以加深颜色
      r = Math.max(0, r - 40);
      g = Math.max(0, g - 40);
      b = Math.max(0, b - 40);
      
      // 将结果转换回hex
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    },
    // 打开AI对话框
    openAIDialog() {
      this.showAIDialog = true;
      console.log('Opening AI comparison dialog...');
    },
    // 关闭AI对话框
    closeAIDialog() {
      this.showAIDialog = false;
    },
    // 下载报告
    downloadReport() {
      console.log('Downloading AI comparison report...');
      // 这里可以添加实际的下载逻辑
    },
    // 发送消息
    sendMessage() {
      if (!this.userMessage.trim()) return;
      
      // 获取当前时间
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const timeString = `${hours}:${minutes}`;
      
      // 添加用户消息
      this.chatMessages.push({
        sender: 'user',
        text: this.userMessage,
        time: timeString
      });
      
      // 清空输入框
      const userQuestion = this.userMessage;
      this.userMessage = '';
      
      // 显示AI正在输入
      this.isAiTyping = true;
      
      // 立即滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      // 模拟AI思考时间
      setTimeout(() => {
        this.isAiTyping = false;
        
        // 根据用户问题生成AI回复
        const aiResponse = this.generateAIResponse(userQuestion);
        
        // 添加AI回复
        this.chatMessages.push({
          sender: 'ai',
          text: aiResponse,
          time: timeString
        });
        
        // 滚动到最新消息
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 1500);
    },
    // 添加一个新方法来滚动到底部
    scrollToBottom() {
      if (this.$refs.chatMessages) {
        (this.$refs.chatMessages as HTMLElement).scrollTop = (this.$refs.chatMessages as HTMLElement).scrollHeight;
      }
    },
    // 生成AI回复
    generateAIResponse(question: string) {
      // 模拟AI分析问题并生成回复
      question = question.toLowerCase();
      
      // 一些预设的问题和回复
      if (question.includes('difference') || question.includes('different')) {
        return `Based on my analysis, the key differences between these patients are:<br>
               <ul>
                 <li><strong>Disc Morphology:</strong> Patient 1 has a posterior disc bulge at C5-C6 with mild spinal canal stenosis, while Patient 2 shows normal disc alignment.</li>
                 <li><strong>Neural Compression:</strong> Patient 1 shows mild compression of the ventral thecal sac, not seen in Patient 2.</li>
                 <li><strong>Signal Intensity:</strong> There is increased T2 signal intensity in the affected disc in Patient 1, indicating possible inflammatory changes.</li>
               </ul>`;
      } else if (question.includes('similar') || question.includes('similarities')) {
        return `The main similarities between these patients include:<br>
               <ul>
                 <li>Both maintain normal cervical lordosis</li>
                 <li>Neither patient shows evidence of fracture or dislocation</li>
                 <li>Both have normal vertebral body height and signal intensity</li>
                 <li>Adjacent C4-C5 and C6-C7 levels appear normal in both patients</li>
               </ul>`;
      } else if (question.includes('treatment') || question.includes('therapy') || question.includes('manage')) {
        return `For the patient with disc bulging (Patient 1), typical management approaches include:<br>
               <ul>
                 <li><strong>Conservative treatment:</strong> Physical therapy, NSAIDs, and activity modification for 6-8 weeks</li>
                 <li><strong>If symptoms persist:</strong> Consider epidural steroid injection</li>
                 <li><strong>For significant neurological symptoms:</strong> Surgical consultation may be warranted</li>
                 <li><strong>Follow-up imaging:</strong> Recommended in 6 months if symptoms persist</li>
               </ul>
               Would you like me to provide more specific recommendations based on the patient's clinical presentation?`;
      } else if (question.includes('prognosis') || question.includes('outlook')) {
        return `The prognosis for Patient 1 with cervical disc bulging is generally favorable:<br>
               <ul>
                 <li>Approximately 70-80% of patients with similar findings improve with conservative management</li>
                 <li>Recovery timeline typically ranges from 6-12 weeks</li>
                 <li>Risk of progression to disc herniation is relatively low (~15%)</li>
                 <li>Regular follow-up is recommended to monitor for any neurological changes</li>
               </ul>`;
      } else if (question.includes('diagnosis') || question.includes('diagnose')) {
        return `Based on the imaging findings, I would diagnose Patient 1 with:<br>
               <strong>Cervical spondylosis with C5-C6 disc bulge and mild spinal canal stenosis</strong><br><br>
               Differential diagnoses to consider include:<br>
               <ul>
                 <li>Disc herniation (less likely given the contour pattern)</li>
                 <li>Facet joint arthropathy (would need additional views to evaluate)</li>
                 <li>Ligamentum flavum hypertrophy (contributing factor to stenosis)</li>
               </ul>`;
      } else {
        return `Thank you for your question. As an AI diagnostic assistant, I can help analyze the differences between these patients, suggest treatment approaches, provide prognostic information, or offer educational insights about cervical spine pathology. What specific aspect would you like me to address?`;
      }
    },
    // 添加：开始比较分析
    startComparison() {
      this.showAIChat = true;
      this.activeChatTab = 'chat';
      
      // 自动收缩比较面板
      this.isComparisonCollapsed = true;
      
      // 添加初始AI消息
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const timeString = `${hours}:${minutes}`;
      
      this.chatMessages = [
        {
          sender: 'ai',
          text: 'I\'ve analyzed both patients. Patient 1 shows a C5-C6 disc bulge with mild canal stenosis, while Patient 2 has normal cervical spine anatomy. How can I help with your analysis?',
          time: timeString
        }
      ];
      
      // 确保初始消息显示后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 添加：处理患者选择变化
    handlePatientChange(event: Event) {
      // 当患者选择变化时，更新SHAP图表
      this.updatePatientComparison();
      
      // 如果对话已经开始，则自动收起对话框
      if (this.showAIChat) {
        this.isComparisonCollapsed = false;
      }
    },
    // 添加：切换比较面板显示状态
    toggleComparisonPanel() {
      this.isComparisonCollapsed = !this.isComparisonCollapsed;
    },
    // 更新左侧疾病选择
    updateLeftDisease(event: Event) {
      this.selectedLeftDisease = (event.target as HTMLSelectElement).value;
      this.updateDiseaseImages(this.selectedLeftDisease);
    },
    
    // 更新右侧疾病选择
    updateRightDisease(event: Event) {
      this.selectedRightDisease = (event.target as HTMLSelectElement).value;
      // 这里可以添加更新图表或数据的逻辑
    },
    
    // 获取疾病相关患者
    getPatientsForDisease(diseaseId: string) {
      const disease = this.ophthalmicDiseases.find(d => d.id === diseaseId);
      return disease ? disease.patients : [];
    },
    
    // 更新疾病图像
    updateDiseaseImages(diseaseId: string) {
      if (this.diseaseImages[diseaseId as keyof typeof this.diseaseImages]) {
        this.currentDiseaseImages = this.diseaseImages[diseaseId as keyof typeof this.diseaseImages];
      }
    },
    // 获取疾病描述
    getDiseaseDescription(diseaseId: string) {
      const descriptions = {
        macular_edema: 'Macular edema is a condition characterized by swelling of the macula, which is the part of the retina responsible for central vision. The fundus image shows microaneurysms, hemorrhages, and exudates, while the OCT scan can reveal macular edema.',
        open_angle_glaucoma: 'Open-angle glaucoma is a type of glaucoma that affects the optic nerve. The fundus image shows optic nerve cupping and the OCT scan reveals thinning of the retinal nerve fiber layer.',
        closed_angle_glaucoma: 'Closed-angle glaucoma is a type of glaucoma that affects the optic nerve. The fundus image shows optic nerve cupping and the OCT scan reveals thinning of the retinal nerve fiber layer.',
        diabetic_maculopathy: 'Diabetic maculopathy involves damage to the blood vessels in the retina. The fundus image shows microaneurysms, hemorrhages, and exudates, while the OCT scan can reveal macular edema.',
        retinitis_pigmentosa: 'Retinitis pigmentosa is a genetic disorder that affects the retina. The fundus image shows drusen or geographic atrophy, and the OCT scan reveals changes in the retinal pigment epithelium.',
        branch_retinal_vein_occlusion: 'Branch retinal vein occlusion is a condition that affects the retina. The fundus image shows a retinal break, subretinal fluid, vitreous traction, and photopsia (flashes of light).'
      };
      
      return descriptions[diseaseId as keyof typeof descriptions] || 'No description available.';
    },

    // 获取疾病特征
    getDiseaseFeatures(diseaseId: string) {
      const features = {
        macular_edema: [
          'Increased cup-to-disc ratio',
          'Retinal nerve fiber layer thinning',
          'Visual field defects',
          'Elevated intraocular pressure'
        ],
        open_angle_glaucoma: [
          'Increased optic nerve cupping',
          'Retinal nerve fiber layer thinning',
          'Visual field defects',
          'Elevated intraocular pressure'
        ],
        closed_angle_glaucoma: [
          'Increased optic nerve cupping',
          'Retinal nerve fiber layer thinning',
          'Visual field defects',
          'Elevated intraocular pressure'
        ],
        diabetic_maculopathy: [
          'Microaneurysms',
          'Hemorrhages',
          'Hard exudates',
          'Cotton wool spots',
          'Neovascularization'
        ],
        retinitis_pigmentosa: [
          'Drusen',
          'Geographic atrophy',
          'Choroidal neovascularization',
          'Pigmentary changes'
        ],
        branch_retinal_vein_occlusion: [
          'Retinal breaks',
          'Subretinal fluid',
          'Vitreous traction',
          'Photopsia (flashes of light)'
        ]
      };
      
      return features[diseaseId as keyof typeof features] || [];
    },
    
    // 格式化疾病名称显示
    formatDiseaseName(diseaseId: string) {
      if (!diseaseId) return '';
      
      // 将下划线替换为空格，并首字母大写
      return diseaseId
        .split('_')
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },
    
    // 获取疾病对应的颜色
    getDiseaseColor(diseaseId: string) {
      const colors = {
        macular_edema: '#1976d2',
        open_angle_glaucoma: '#2196f3',
        closed_angle_glaucoma: '#42a5f5',
        diabetic_maculopathy: '#64b5f6',
        retinitis_pigmentosa: '#90caf9',
        branch_retinal_vein_occlusion: '#bbdefb'
      };
      
      return colors[diseaseId as keyof typeof colors] || '#2196f3';
    },
    
    // 更新疾病显示
    updateDiseaseDisplay() {
      console.log('Updating disease display:', this.selectedDisease1, this.selectedDisease2);
      // 更新图像
      this.updateDiseaseImages(this.selectedDisease1);
      
      // 自动选择每个疾病的第一个患者
      const disease1Patients = this.getPatientsForDisease(this.selectedDisease1);
      const disease2Patients = this.getPatientsForDisease(this.selectedDisease2);
      
      if (disease1Patients.length > 0) {
        this.selectedPatient1 = disease1Patients[0];
      }
      
      if (disease2Patients.length > 0) {
        this.selectedPatient2 = disease2Patients[0];
      }
      
      // 注意：不直接调用updatePatientComparison，而是让选择患者的逻辑触发SHAP图更新
      // 强制重新渲染视图会触发患者数据的更新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 获取单个疾病的数据
    getDiseaseData(diseaseId: string) {
      return { 
        id: diseaseId, 
        name: this.formatDiseaseName(diseaseId), 
        color: this.getDiseaseColor(diseaseId)
      };
    },

    // 获取特定疾病的患者数据
    getPatientsDataForDisease(diseaseId: string) {
      const patients: Array<{id: string, diseaseId: string}> = [];
      const diseasePatients = this.getPatientsForDisease(diseaseId);
      
      diseasePatients.forEach(patientId => {
        patients.push({
          id: patientId,
          diseaseId: diseaseId
        });
      });
      
      return patients;
    },
    handleNodeClick(node: { id: string, name: string, diseaseId?: string }) {
      console.log('Node clicked:', node);
      
      if (node.id.includes('patient')) {
        // 是患者节点
        const patientId = node.name;
        const patient = this.patientDetails[patientId];
        
        if (patient) {
          // 如果是患者1疾病的患者，选择为患者1
          if (patient.disease === this.selectedDisease1) {
            this.selectedPatient1 = patientId;
          } 
          // 如果是患者2疾病的患者，选择为患者2
          else if (patient.disease === this.selectedDisease2) {
            this.selectedPatient2 = patientId;
          }
          
          // 更新患者比较数据
          this.updatePatientComparison();
        }
      } else {
        // 是疾病节点
        const diseaseId = node.id;
        // 可以在这里添加处理疾病节点点击的逻辑，例如显示疾病详情
        console.log('Disease node clicked:', this.formatDiseaseName(diseaseId));
      }
    },
    // 生成随机连接线
    generateRandomLinks(count: number) {
      const links = [];
      const maxIndex = 50; // 连接前50个数据点
      
      for (let i = 0; i < count; i++) {
        const source = Math.floor(Math.random() * maxIndex);
        let target;
        do {
          target = Math.floor(Math.random() * maxIndex);
        } while (target === source);
        
        links.push({
          source: source,
          target: target
        });
      }
      
      return links;
    },
    
    // 更新患者对比数据
    updatePatientComparison() {
      const patient1 = this.patientDetails[this.selectedPatient1];
      const patient2 = this.patientDetails[this.selectedPatient2];
      
      if (!patient1 || !patient2) return;
      
      // 更新SHAP图表
      this.updateShapCharts(patient1, patient2);
      
      // 更新患者趋势图表
      this.updatePatientTrendCharts(patient1, patient2);
      
      // 强制重新渲染视图
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    
    // 更新患者趋势图表
    updatePatientTrendCharts(patient1: any, patient2: any) {
      // 根据患者ID生成确定性的趋势数据
      this.patientTrend1Option = this.generatePatientTrendOption(patient1);
      this.patientTrend2Option = this.generatePatientTrendOption(patient2);
    },

    // 生成患者趋势图配置
    generatePatientTrendOption(patient: any) {
      if (!patient) return {};
      
      // 使用患者ID和年龄生成确定性的伪随机数，以便每个患者的图表保持一致
      const patientSeed = parseInt(patient.disease.charCodeAt(0) + String(patient.age));
      
      // 生成未来6个月的疾病发展趋势数据
      // 对于不同疾病，趋势线会有不同的形态
      let trendData = [];
      const months = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6'];
      
      // 根据疾病类型生成不同的趋势曲线
      switch(patient.disease) {
        case 'macular_edema':
          // 黄斑水肿：先恶化后改善
          trendData = [60, 70, 75, 72, 65, 60].map(v => v + (Math.sin(patientSeed) * 10));
          break;
        case 'open_angle_glaucoma':
          // 开角型青光眼：持续恶化
          trendData = [50, 55, 62, 68, 75, 82].map(v => v + (Math.cos(patientSeed) * 8));
          break;
        case 'closed_angle_glaucoma':
          // 闭角型青光眼：急剧恶化后平稳
          trendData = [55, 75, 85, 87, 88, 89].map(v => v + (Math.sin(patientSeed * 0.5) * 7));
          break;
        case 'diabetic_maculopathy':
          // 糖尿病性黄斑病变：波动性恶化
          trendData = [60, 68, 65, 72, 70, 76].map(v => v + (Math.tan(patientSeed * 0.3) * 5));
          break;
        case 'retinitis_pigmentosa':
          // 视网膜色素变性：持续缓慢恶化
          trendData = [62, 65, 67, 70, 72, 75].map(v => v + (Math.cos(patientSeed * 0.7) * 6));
          break;
        case 'branch_retinal_vein_occlusion':
          // 视网膜分支静脉阻塞：急剧恶化后改善
          trendData = [55, 80, 75, 70, 67, 65].map(v => v + (Math.sin(patientSeed * 0.4) * 9));
          break;
        default:
          trendData = [50, 55, 60, 65, 70, 75].map(v => v + (Math.random() * 10));
      }
      
      return {
        tooltip: {
          trigger: 'axis',
          formatter: function(params: any) {
            return `<div style="font-size: 12px; padding: 4px 8px;">
                    <div>Month ${params[0].axisValue.substring(1)}</div>
                    <div>
                      <span style="display:inline-block; width:10px; height:10px; border-radius:50%; background-color:${params[0].color};"></span>
                      Risk: ${params[0].data}%
                    </div>
                  </div>`;
          }
        },
        grid: {
          top: 5,
          right: 5,
          bottom: 8,
          left: 5,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: months,
          axisLabel: {
            fontSize: 9,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        yAxis: {
          type: 'value',
          min: 40,
          max: 100,
          axisLabel: {
            fontSize: 9,
            color: '#666',
            formatter: '{value}%'
          },
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: 'Disease Risk',
            type: 'line',
            data: trendData,
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: '#ff7675'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 118, 117, 0.4)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 118, 117, 0.1)'
                  }
                ]
              }
            },
            emphasis: {
              focus: 'series'
            }
          }
        ]
      };
    },
    
    // 更新SHAP图表
    updateShapCharts(patient1: any, patient2: any) {
      // 准备SHAP图数据 - 左侧患者 (Patient 1)
      const patient1Indicators = this.prepareIndicatorData(patient1);
      
      this.shapChartOption1 = {
        title: {
          text: 'Patient 1 Indicators',
          textStyle: {
            fontSize: 12,
            fontWeight: 'normal',
            color: '#666'
          },
          left: 'center',
          top: 0
        },
        grid: {
          top: 25,
          right: 5,
          bottom: 5,
          left: 5,
          containLabel: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            const value = params.value;
            const color = value > 0 ? '#1976d2' : '#64b5f6';
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${params.name}</div>
              <div style="color: ${color}; font-size: 13px;">Impact: ${value > 0 ? '+' : ''}${value}</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'category',
          data: patient1Indicators.map(item => item.name),
          show: false
        },
        series: [
          {
            type: 'bar',
            data: patient1Indicators.map(item => {
              return {
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: item.value > 0 ? '#1976d2' : '#64b5f6',
                  borderRadius: 3
                }
              };
            }),
            label: {
              show: false
            },
            barWidth: '60%',
            barGap: '30%',
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0,0,0,0.2)'
              }
            }
          }
        ]
      };
      
      // 准备SHAP图数据 - 右侧患者 (Patient 2)
      const patient2Indicators = this.prepareIndicatorData(patient2);
      
      this.shapChartOption2 = {
        title: {
          text: 'Patient 2 Indicators',
          textStyle: {
            fontSize: 12,
            fontWeight: 'normal',
            color: '#666'
          },
          left: 'center',
          top: 0
        },
        grid: {
          top: 25,
          right: 5,
          bottom: 5,
          left: 5,
          containLabel: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            const value = params.value;
            const color = value > 0 ? '#1976d2' : '#64b5f6';
            return `<div style="padding: 8px 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 5px; font-size: 14px;">${params.name}</div>
              <div style="color: ${color}; font-size: 13px;">Impact: ${value > 0 ? '+' : ''}${value}</div>
            </div>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 13
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px; z-index: 100;',
          enterable: true,
          confine: true,
          hideDelay: 300
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'category',
          data: patient2Indicators.map(item => item.name),
          show: false
        },
        series: [
          {
            type: 'bar',
            data: patient2Indicators.map(item => {
              return {
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: item.value > 0 ? '#1976d2' : '#64b5f6',
                  borderRadius: 3
                }
              };
            }),
            label: {
              show: false
            },
            barWidth: '60%',
            barGap: '30%',
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0,0,0,0.2)'
              }
            }
          }
        ]
      };
    },
    
    // 准备指标数据
    prepareIndicatorData(patient: any) {
      if (!patient || !patient.indicators) return [];
      
      const result = [];
      
      // 使用患者ID的最后4位数字作为随机种子
      const patientIdSeed = parseInt(String(patient.disease).charCodeAt(0) + String(patient.age));
      
      // 将指标数据转换为SHAP图所需格式
      for (const [key, value] of Object.entries(patient.indicators)) {
        // 格式化指标名称
        const formattedName = key
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        // 使用伪随机值，但基于患者ID，这样同一个患者的指标影响值会保持一致
        // 不同患者则会有不同的值
        let shapValue;
        if (typeof value === 'number') {
          // 根据指标类型确定SHAP值的方向
          if (['visual_acuity'].includes(key)) {
            // 视力值越高越好
            shapValue = value > 0.5 ? 0.6 * (1 + Math.sin(patientIdSeed * key.length * 0.1)) : 
                                      -0.6 * (1 + Math.cos(patientIdSeed * key.length * 0.1));
          } else if (['intraocular_pressure', 'central_macular_thickness', 'cup_to_disc_ratio', 'hba1c'].includes(key)) {
            // 这些指标值越低越好
            shapValue = value > 20 ? -0.7 * (1 + Math.sin(patientIdSeed * key.length * 0.2)) : 
                                     0.5 * (1 + Math.cos(patientIdSeed * key.length * 0.2));
          } else {
            // 使用患者ID作为随机种子，保证同一患者每次产生相同的"随机"值
            const deterministicRandom = Math.sin(patientIdSeed * key.length) * 0.5;
            shapValue = deterministicRandom * 0.8;
          }
        } else {
          // 文本型指标
          const strValue = String(value);
          if (['Present', 'Moderate', 'Severe', 'Numerous', 'Sluggish'].includes(strValue)) {
            // 负面影响，但每个患者有不同程度的差异
            shapValue = -0.5 - (0.3 * Math.sin(patientIdSeed * strValue.length * 0.3));
          } else {
            // 正面或中性影响，但每个患者有不同程度的差异
            shapValue = 0.2 + (0.3 * Math.cos(patientIdSeed * strValue.length * 0.3));
          }
        }
        
        result.push({
          name: formattedName,
          value: parseFloat(shapValue.toFixed(2))
        });
      }
      
      // 按SHAP值大小排序
      return result.sort((a, b) => b.value - a.value);
    },
    
    // 处理患者选择变化
    handlePatientSelection(event: Event, patientNum: number) {
      const selectedValue = (event.target as HTMLSelectElement).value;
      if (patientNum === 1) {
        this.selectedPatient1 = selectedValue;
      } else {
        this.selectedPatient2 = selectedValue;
      }
      
      // 获取患者数据
      const patient1 = this.patientDetails[this.selectedPatient1];
      const patient2 = this.patientDetails[this.selectedPatient2];
      
      // 确保两个患者数据都存在
      if (patient1 && patient2) {
        // 直接调用更新SHAP图表
        this.updateShapCharts(patient1, patient2);
        
        // 同时更新其他相关图表
        this.updatePatientTrendCharts(patient1, patient2);
      }
      
      // 当患者选择变化时，更新完整的患者比较
      this.updatePatientComparison();
      
      // 强制重新渲染视图
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    
    // 获取患者疾病名称
    getPatientDiseaseName(patientId: string) {
      const patient = this.patientDetails[patientId];
      return patient ? this.formatDiseaseName(patient.disease) : '';
    },

    // 辅助方法：获取疾病的图像路径
    getImagePathForDisease(diseaseId: string) {
      // 根据疾病ID返回图像路径
      const diseaseImages = {
        macular_edema: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/macular-edema-oct.jpg'
        ],
        open_angle_glaucoma: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/open-angle-glaucoma-oct.jpg'
        ],
        closed_angle_glaucoma: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/closed-angle-glaucoma-oct.jpg'
        ],
        diabetic_maculopathy: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/diabetic-maculopathy-oct.jpg'
        ],
        retinitis_pigmentosa: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/retinitis-pigmentosa-oct.jpg'
        ],
        branch_retinal_vein_occlusion: [
          'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-fundus.jpg',
          'https://www.aao.org/images/k-assets/eye-health/diseases/brvo-oct.jpg'
        ]
      };
      
      // 随机选择这种疾病的一个图像
      const images = diseaseImages[diseaseId as keyof typeof diseaseImages] || [];
      if (images.length > 0) {
        return images[Math.floor(Math.random() * images.length)];
      }
      
      // 如果没有找到，返回默认图像
      return 'https://www.aao.org/images/k-assets/eye-health/diseases/glaucoma-fundus.jpg';
    },
    
    // 生成缺失的患者详细信息
    generateMissingPatientDetails() {
      // 遍历每种疾病的患者列表
      this.ophthalmicDiseases.forEach(disease => {
        disease.patients.forEach(patientId => {
          // 如果患者详情不存在，则生成一个新的
          if (!this.patientDetails[patientId]) {
            // 从patientId提取数字信息以保持一致性
            const patientSeed = parseInt(patientId.slice(-5));
            const rand = (min: number, max: number) => Math.floor(min + (patientSeed % 100) / 100 * (max - min));
            
            // 生成年龄分布，不同疾病年龄段有所不同
            let age;
            if (disease.id === 'macular_edema') {
              age = rand(55, 80); // 黄斑水肿多见于老年人
            } else if (disease.id === 'open_angle_glaucoma') {
              age = rand(50, 85); // 开角型青光眼多见于老年人
            } else if (disease.id === 'closed_angle_glaucoma') {
              age = rand(45, 75); // 闭角型青光眼年龄分布
            } else if (disease.id === 'diabetic_maculopathy') {
              age = rand(40, 70); // 糖尿病性黄斑病变
            } else if (disease.id === 'retinitis_pigmentosa') {
              age = rand(25, 60); // 视网膜色素变性多见于年轻人
            } else if (disease.id === 'branch_retinal_vein_occlusion') {
              age = rand(45, 75); // 视网膜分支静脉阻塞
            } else {
              age = rand(30, 80); // 默认年龄范围
            }
            
            // 随机生成性别
            const gender = patientSeed % 2 === 0 ? 'Male' : 'Female';
            
            // 随机生成就诊次数
            const visits = rand(1, 12);
            
            // 随机生成住院次数 (通常小于就诊次数)
            const hospitalized = rand(0, Math.min(3, visits));
            
            // 根据疾病类型生成特定的指标
            let indicators: any = {};
            let textData = '';
            
            switch (disease.id) {
              case 'macular_edema':
                indicators = {
                  visual_acuity: (rand(1, 10) / 10).toFixed(1),
                  intraocular_pressure: rand(14, 25),
                  central_macular_thickness: rand(300, 600),
                  hba1c: (rand(60, 100) / 10).toFixed(1),
                  blood_pressure: `${rand(120, 160)}/${rand(70, 100)}`
                };
                textData = `Patient presents with ${visits > 6 ? 'longstanding' : 'recent'} vision changes. OCT shows ${indicators.central_macular_thickness > 450 ? 'significant' : 'moderate'} macular edema with ${rand(0, 1) ? 'intraretinal fluid' : 'cystoid changes'}. ${gender === 'Male' ? 'He' : 'She'} has a ${rand(0, 1) ? 'history of diabetes' : 'history of hypertension'} for ${rand(5, 20)} years.`;
                break;
                
              case 'open_angle_glaucoma':
                indicators = {
                  visual_acuity: (rand(4, 10) / 10).toFixed(1),
                  intraocular_pressure: rand(20, 35),
                  cup_to_disc_ratio: (rand(6, 9) / 10).toFixed(1),
                  visual_field_md: -rand(3, 20),
                  corneal_thickness: rand(500, 570)
                };
                textData = `Patient with ${indicators.intraocular_pressure > 25 ? 'high' : 'elevated'} intraocular pressure. Examination reveals increased cup-to-disc ratio of ${indicators.cup_to_disc_ratio} with ${rand(0, 1) ? 'thinning of neuroretinal rim' : 'nerve fiber layer loss'}. ${gender === 'Male' ? 'He' : 'She'} ${rand(0, 1) ? 'reports gradual peripheral vision loss' : 'is relatively asymptomatic'}.`;
                break;
                
              case 'closed_angle_glaucoma':
                indicators = {
                  visual_acuity: (rand(1, 8) / 10).toFixed(1),
                  intraocular_pressure: rand(25, 60),
                  anterior_chamber_depth: (rand(15, 30) / 10).toFixed(1),
                  corneal_edema: ['Mild', 'Moderate', 'Severe'][rand(0, 2)],
                  pupil_reaction: ['Normal', 'Sluggish', 'Fixed'][rand(0, 2)]
                };
                textData = `Patient presented with ${rand(0, 1) ? 'acute eye pain and headache' : 'blurred vision and halos around lights'}. Examination showed ${indicators.corneal_edema.toLowerCase()} corneal edema, ${indicators.pupil_reaction.toLowerCase()} pupillary reaction, and shallow anterior chamber. IOP was markedly elevated at ${indicators.intraocular_pressure} mmHg.`;
                break;
                
              case 'diabetic_maculopathy':
                indicators = {
                  visual_acuity: (rand(2, 8) / 10).toFixed(1),
                  hba1c: (rand(70, 120) / 10).toFixed(1),
                  microaneurysms: ['Few', 'Moderate', 'Numerous'][rand(0, 2)],
                  hard_exudates: ['Absent', 'Present', 'Extensive'][rand(0, 2)],
                  macular_edema: ['Mild', 'Moderate', 'Clinically significant'][rand(0, 2)]
                };
                textData = `Patient with ${rand(5, 25)}-year history of diabetes presents with gradually ${rand(0, 1) ? 'worsening vision' : 'decreased visual acuity'}. Fundus examination shows ${indicators.microaneurysms.toLowerCase()} microaneurysms, ${indicators.hard_exudates.toLowerCase()} hard exudates, and ${indicators.macular_edema.toLowerCase()} macular edema. HbA1c is ${indicators.hba1c}%.`;
                break;
                
              case 'retinitis_pigmentosa':
                indicators = {
                  visual_acuity: (rand(3, 8) / 10).toFixed(1),
                  visual_field: ['Mildly constricted', 'Moderately constricted', 'Severely constricted'][rand(0, 2)],
                  erg_amplitude: ['Mildly reduced', 'Moderately reduced', 'Severely reduced'][rand(0, 2)],
                  bone_spicules: ['Minimal', 'Moderate', 'Extensive'][rand(0, 2)],
                  night_vision: ['Mildly impaired', 'Moderately impaired', 'Severely impaired'][rand(0, 2)]
                };
                textData = `Patient reports ${indicators.night_vision} and progressive loss of peripheral vision. Fundus examination reveals ${indicators.bone_spicules} bone spicule pigmentation, attenuated vessels, and ${rand(0, 1) ? 'waxy pallor' : 'pale appearance'} of the optic disc. ${gender === 'Male' ? 'He' : 'She'} has ${rand(0, 1) ? 'family history of RP' : 'no known family history'}.`;
                break;
                
              case 'branch_retinal_vein_occlusion':
                indicators = {
                  visual_acuity: (rand(2, 8) / 10).toFixed(1),
                  intraocular_pressure: rand(15, 25),
                  hemorrhages: ['Few', 'Moderate', 'Extensive flame-shaped'][rand(0, 2)],
                  cotton_wool_spots: ['Absent', 'Few', 'Multiple'][rand(0, 2)],
                  macular_edema: ['Absent', 'Mild', 'Significant'][rand(0, 2)]
                };
                textData = `Patient noticed ${rand(0, 1) ? 'sudden' : 'gradual'}, painless vision loss in the ${rand(0, 1) ? 'right' : 'left'} eye ${rand(1, 8)} weeks ago. Fundus examination shows ${indicators.hemorrhages} hemorrhages in the ${rand(0, 1) ? 'superotemporal' : 'inferotemporal'} quadrant with ${indicators.cotton_wool_spots} cotton wool spots. Patient has history of ${rand(0, 1) ? 'hypertension' : 'cardiovascular disease'}.`;
                break;
                
              default:
                textData = "No detailed information available.";
            }
            
            // 生成图像路径，基于疾病类型
            const imagePath = this.getImagePathForDisease(disease.id);
            
            // 创建并保存患者详情
            this.patientDetails[patientId] = {
              disease: disease.id,
              age: age,
              gender: gender,
              visits: visits,
              hospitalized: hospitalized,
              indicators: indicators,
              textData: textData,
              imagePath: imagePath
            };
          }
        });
      });
    },
    
    // 生成聚类中心点
    generateClusterCenters(): any[] {
      const centers = [];
      
      switch (this.clusteringBy) {
        case 'age':
          // 按年龄段聚类
          centers.push(
            { name: '18-40', value: [-3, -3], itemStyle: { color: '#2196f3' } },
            { name: '41-60', value: [0, 0], itemStyle: { color: '#f44336' } },
            { name: '61+', value: [3, 3], itemStyle: { color: '#4caf50' } }
          );
          break;
        
        case 'disease':
          // 按疾病类型聚类
          centers.push(
            { name: 'Macular Edema', value: [-4, 0], itemStyle: { color: '#2196f3' } },
            { name: 'Open-Angle Glaucoma', value: [-2, 2], itemStyle: { color: '#f44336' } },
            { name: 'Closed-Angle Glaucoma', value: [0, -2], itemStyle: { color: '#4caf50' } },
            { name: 'Diabetic Maculopathy', value: [2, 2], itemStyle: { color: '#ff9800' } },
            { name: 'Retinitis Pigmentosa', value: [4, 0], itemStyle: { color: '#9c27b0' } },
            { name: 'Branch Retinal Vein Occlusion', value: [0, 3], itemStyle: { color: '#607d8b' } }
          );
          break;
        
        case 'visits':
          // 按就诊次数聚类
          centers.push(
            { name: 'First Visit', value: [-3, 0], itemStyle: { color: '#2196f3' } },
            { name: '1-3 Visits', value: [0, 0], itemStyle: { color: '#f44336' } },
            { name: '4+ Visits', value: [3, 0], itemStyle: { color: '#4caf50' } }
          );
          break;
        
        case 'other':
          // 按其他属性聚类
          centers.push(
            { name: 'Male', value: [-2, -2], itemStyle: { color: '#2196f3' } },
            { name: 'Female', value: [2, -2], itemStyle: { color: '#e91e63' } },
            { name: 'Urban', value: [0, 2], itemStyle: { color: '#ff9800' } },
            { name: 'Rural', value: [0, -2], itemStyle: { color: '#4caf50' } }
          );
          break;
        
        default:
          // 不聚类或默认情况
          break;
      }
      
      return centers;
    },
    
    // 生成聚类连接线
    generateClusterEdges(count: number): any[] {
      const edges: any[] = [];
      
      // 如果不聚类，则不生成连接线
      if (this.clusteringBy === 'none') {
        return edges;
      }
      
      // 根据聚类方式生成连接线
      let centerCount = 0;
      let groupInfo: {[key: string]: {index: number, count: number, color: string}} = {};
      
      switch (this.clusteringBy) {
        case 'age':
          groupInfo = {
            '18-40': {index: 0, count: 0, color: '#2196f3'},
            '41-60': {index: 1, count: 0, color: '#f44336'},
            '61+': {index: 2, count: 0, color: '#4caf50'}
          };
          centerCount = 3;
          break;
        
        case 'disease':
          groupInfo = {
            'Macular Edema': {index: 0, count: 0, color: '#2196f3'},
            'Open-Angle Glaucoma': {index: 1, count: 0, color: '#f44336'},
            'Closed-Angle Glaucoma': {index: 2, count: 0, color: '#4caf50'},
            'Diabetic Maculopathy': {index: 3, count: 0, color: '#ff9800'},
            'Retinitis Pigmentosa': {index: 4, count: 0, color: '#9c27b0'},
            'Branch Retinal Vein Occlusion': {index: 5, count: 0, color: '#607d8b'}
          };
          centerCount = 6;
          break;
        
        case 'visits':
          groupInfo = {
            'First Visit': {index: 0, count: 0, color: '#2196f3'},
            '1-3 Visits': {index: 1, count: 0, color: '#f44336'},
            '4+ Visits': {index: 2, count: 0, color: '#4caf50'}
          };
          centerCount = 3;
          break;
        
        case 'other':
          groupInfo = {
            'Male': {index: 0, count: 0, color: '#2196f3'},
            'Female': {index: 1, count: 0, color: '#e91e63'},
            'Urban': {index: 2, count: 0, color: '#ff9800'},
            'Rural': {index: 3, count: 0, color: '#4caf50'}
          };
          centerCount = 4;
          break;
      }
      
      // 为每个组生成一定数量的连接线
      for (const group in groupInfo) {
        const info = groupInfo[group];
        const pointCount = Math.floor(count / Object.keys(groupInfo).length); // 每个组的点数量大致相等
        
        for (let i = 0; i < pointCount; i++) {
          // 为每个点创建一个连接到中心点的线
          const sourceIndex = info.index; // 中心点索引
          const targetIndex = centerCount + info.count; // 点的索引从centerCount开始
          
          edges.push({
            source: sourceIndex,
            target: targetIndex,
            lineStyle: {
              color: info.color,
              width: 0.5,
              opacity: 0.5
            }
          });
          
          info.count++;
        }
      }
      
      return edges;
    },
    // Toggle right panel visibility
    toggleRightPanel() {
      this.isRightPanelCollapsed = !this.isRightPanelCollapsed;
      
      // Allow time for DOM to update, then trigger window resize to update charts
      this.$nextTick(() => {
        window.dispatchEvent(new Event('resize'));
      });
    },
  },
  mounted() {
    // 首先生成缺失的患者数据
    this.generateMissingPatientDetails();
    
    // 监听聊天消息变化，自动滚动到底部
    this.$watch('chatMessages', () => {
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    }, { deep: true });
    
    // 初始化患者对比数据
    this.updatePatientComparison();
    
    // 在组件挂载后，初始化散点图的聚类视图
    this.$nextTick(() => {
      this.updateScatterChart();
    });

    // 监听图表点击事件
    this.$nextTick(() => {
      const charts = document.querySelectorAll('.chart');
      charts.forEach(chart => {
        const instance = echarts.getInstanceByDom(chart as HTMLElement);
        if (instance) {
          instance.on('click', (params: any) => {
            if (params.data && typeof params.data === 'object' && 'patientId' in params.data) {
              // 当点击患者节点时，选择该患者
              const patientId = params.data.patientId;
              const diseaseId = params.data.diseaseId;
              
              // 根据疾病决定选择Patient1还是Patient2
              if (diseaseId === this.selectedDisease1) {
                this.selectedPatient1 = patientId;
              } else if (diseaseId === this.selectedDisease2) {
                this.selectedPatient2 = patientId;
              } else {
                // 如果疾病与当前选择的两个疾病不匹配，选择Patient1
                this.selectedPatient1 = patientId;
                this.selectedDisease1 = diseaseId;
              }
              
              // 更新患者比较数据
              this.updatePatientComparison();
            }
          });
        }
      });
    });
  },
}
</script>

<style scoped>
.diagnosis-assistant {
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
  color: #333;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.header {
  background-color: #222;
  color: white;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  height: 45px;
  z-index: 10;
}

.header h1 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: bold;
  margin-right: 10px;
}

.subtitle {
  font-size: 0.85rem;
  font-style: italic;
}

.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px);
  transition: all 0.3s ease;
}

.right-panel {
  width: 30%;
  background-color: #fff;
  border-left: 1px solid #ddd;
  overflow: hidden;
  transition: width 0.3s ease;
  position: relative;
}

.right-panel.collapsed {
  width: 30px;
  min-width: 30px;
}

.right-panel-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  transition: opacity 0.2s ease;
}

.collapsed .right-panel-content {
  opacity: 0;
  pointer-events: none;
}

.panel-pull-tab {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 0 5px 5px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  border: 1px solid #ddd;
  border-left: none;
}

.pull-tab-icon {
  font-size: 1.2rem;
  color: #555;
}

.center-content {
  flex: 1;
  transition: width 0.3s ease;
  overflow-y: auto;
}

/* Adjust center content when right panel is collapsed */
.main-content.right-panel-collapsed .center-content {
  flex: 1;
}

/* Adjust left panel when right panel is collapsed */
.main-content.right-panel-collapsed .left-panel {
  flex: 0 0 20%;
}

/* Make charts responsive */
.chart {
  width: 100% !important;
  height: 100% !important;
  transition: all 0.3s ease;
}

.left-panel {
  width: 190px;
  background-color: #f0f0f0;
  border-right: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 5px; /* 减小水平内边距 */
  box-sizing: border-box;
  min-width: 0;
  height: 100%;
  overflow: hidden;
}

/* 第一屏和第二屏的通用样式 */
.first-screen, .detail-section {
  min-height: calc(100vh - 46px);
  margin: 0;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
}

/* 第一屏特有样式 */
.first-screen {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 46px); /* 设置固定高度为可视区域高度减去header高度 */
  overflow-y: hidden; /* 禁止滚动 */
  padding: 5px; /* 减小内边距 */
}

.embedding-section {
  flex: 0 0 calc(50vh - 25px); /* 进一步微调高度 */
  height: calc(50vh - 25px);
  min-height: 270px;
  margin-bottom: 3px; /* 减小底部间距 */
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
}

.visualization-section {
  flex: 0 0 calc(50vh - 25px); /* 进一步微调高度 */
  height: calc(50vh - 25px);
  min-height: 270px;
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
}

/* 调整嵌入式可视化的高度 */
.visualization-canvas {
  flex: 1; /* 填充所有可用空间 */
  min-height: 200px;
  background-color: #f5f5f5;
  margin-bottom: 5px; /* 减小边距 */
  border-radius: 8px;
  box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
  display: flex;
}

.visualization-canvas .chart {
  width: 100%;
  height: 100%;
}

/* 调整可视化面板容器 */
.visualization-panels {
  flex: 1;
  display: flex;
  margin-bottom: 0; /* 移除底部边距 */
  height: 90%; /* 设置面板高度占据C区域的大部分 */
  gap: 5px; /* 减小间隙 */
}

/* 调整面板内容区域填充整个可用空间 */
.panel-content {
  flex: 1;
  height: 90%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 5px; /* 减小填充 */
}

/* 确保图表填充可用空间 */
.chart {
  width: 100%;
  height: 100%;
  min-height: 150px;
}

/* 第二屏特有样式 */
.detail-section {
  min-height: calc(100vh - 46px);
  margin: 0;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  height: 100%;
  overflow-y: auto;
  z-index: 5;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0 5px;
  margin-bottom: 20px;
}

.knowledge-graph-container {
  flex: 3;
  margin-bottom: 15px;
  min-height: 400px;
  max-height: 60vh;
  overflow: hidden;
}

.knowledge-graphs {
  display: flex;
  gap: 15px;
  height: 100%;
  width: 100%;
}

.graph-section {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.image-comparison-enhanced {
  flex: 2;
  display: flex;
  margin-bottom: 10px;
  min-height: 250px;
  max-height: 35vh;
}

.right-panel {
  width: 400px;
  background-color: #f0f0f0;
  border-left: 1px solid #ddd;
  padding: 10px;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
  flex-shrink: 0;
  position: relative; /* Add position relative */
}

/* 压缩左侧面板中的标题和内容 */
.panel-section {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 5px; /* 减小边距 */
}

.panel-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 3px 0 10px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #1976d2;
}

.modality-item {
  padding: 3px 0;
  cursor: pointer;
  font-size: 0.85rem;
}

.modality-item.selected {
  font-weight: bold;
  color: #1976d2;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* 减小边距 */
}

.section-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  font-weight: bold;
  margin-right: 8px;
  font-size: 0.9rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1rem;
}

.save-button {
  background-color: #4caf50;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  margin-left: auto;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-button:hover {
  background-color: #388e3c;
}

.case-selection {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* 减小边距 */
  flex-wrap: wrap; /* 允许换行，避免拥挤 */
}

.case {
  margin: 0 5px;
  padding: 0 5px;
  border-bottom: 2px solid;
}

.case.normal {
  border-color: #2196f3;
}

.case.herniated {
  border-color: #f44336;
}

.case.bulging {
  border-color: #4caf50;
}

.current-case {
  font-weight: bold;
}

.embedding-visualization {
  flex: 1; /* 填充所有可用空间 */
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  padding: 8px; /* 减小填充 */
  margin-bottom: 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.embedding-visualization:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.visualization-canvas::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, transparent 70%, rgba(0,0,0,0.03) 100%);
  pointer-events: none;
}

.embedding-types {
  display: flex;
  justify-content: space-between;
}

.embedding-type {
  flex: 1;
  text-align: center;
}

.embedding-bars {
  height: 35px; /* 稍微减小高度 */
  background-color: #f0f0f0;
  margin: 0 5px;
}

.embedding-name {
  font-size: 0.75rem;
  margin-top: 4px;
}

.visualization-section {
  display: flex;
  flex-direction: column;
}

.visualization-panels {
  flex: 1; /* 填充所有可用空间 */
  display: flex;
  margin-bottom: 0; /* 移除底部边距 */
  min-height: 200px;
  gap: 5px; /* 面板之间的间距 */
}

.panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
  z-index: 10;
  height: 40px; /* 固定高度 */
}

.panel-controls {
  display: flex;
  margin-top: 5px; /* 减小上边距 */
  height: 30px; /* 减小高度 */
}

.control {
  padding: 1px 5px;
  font-size: 0.75rem;
  cursor: pointer;
}

.control.active {
  background-color: #1976d2;
  color: white;
  border-radius: 3px;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 5px; /* 减小填充 */
}

.detail-header {
  font-weight: bold;
  margin-bottom: 15px;
  font-size: 1rem;
  width: 100%;
  background-color: #f5f5f5;
  padding: 5px 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.detail-header .section-header {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.card-details {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.card-header {
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.card-id, .card-label {
  font-weight: bold;
  margin-right: 8px;
}

.card-label.bulging {
  color: #4caf50;
}

/* 新增：完全移除灰色背景的图像比较部分样式 */
.image-comparison-enhanced {
  flex: 4; /* 占据第二屏的4/10 */
  display: flex;
  margin-bottom: 8px;
  height: 30vh;
}

.raw-image-section, .cam-image-section {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  margin: 0 5px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-content-container {
  display: flex;
  flex-direction: column;
}

/* 修改：使用CSS背景图片而不是img标签 */
.medical-image {
  width: 100%;
  height: 20vh; /* 图像高度占据视口的20% */
  border-radius: 4px;
  margin-bottom: 6px;
  background-size: cover;
  background-position: center;
  border: 1px solid #e0e0e0;
  background-color: #f8f8f8;
}

.raw-placeholder {
  background-color: #f2f2f2;
  background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0), 
                    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.cam-placeholder {
  background-color: #f8f8f8;
  background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0), 
                    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.image-chart {
  width: 100%;
  flex: 1; /* 图表占据剩余空间 */
  min-height: 60px;
}

.comparison-section {
  display: flex;
  flex-direction: column;
}

.comparison-item {
  background-color: white;
  border-radius: 4px;
  padding: 10px 10px 10px 25px; /* Reduced left padding from 35px to 25px */
  margin-bottom: 12px;
  position: relative;
}

.item-number {
  position: absolute;
  top: 10px;
  left: 5px; /* Moved left from 10px to 5px */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.comparison-item h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  padding-left: 0; /* Removed padding-left (was 5px) */
}

.patient-comparison {
  display: flex;
  padding-left: 0; /* Removed padding-left (was 20px) */
}

.patient {
  flex: 1;
  padding: 0 5px; /* Reduced horizontal padding from 8px to 5px */
}

.patient-header {
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.indicator-section, .text-section, .image-section {
  margin-bottom: 12px;
}

.indicator-header, .text-header, .image-header {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.card-info {
  padding-left: 25px; /* 为数字留出空间 */
}

.info-row {
  display: flex;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.info-row span {
  flex: 1;
}

/* 图表样式 */
.chart {
  width: 100%;
  height: 100%;
}

.small-chart {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.pie-comparison {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.pie-chart-container {
  width: 45%;
}

.pie-chart {
  width: 100%;
  height: 140px;
}

/* 图像网格样式 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 4px;
  height: 100%;
  overflow-y: auto;
}

.image-item {
  background-color: #ddd;
  aspect-ratio: 1;
  border-radius: 2px;
}

/* 新增：按钮样式 */
.panel-button {
  display: block;
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 6px;
  background-color: #f8f8f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  text-align: left;
  transition: all 0.2s;
}

.panel-button:hover {
  background-color: #e8e8e8;
}

.panel-button.primary {
  background-color: #1976d2;
  color: white;
  border-color: #1565c0;
}

.panel-button.primary:hover {
  background-color: #1565c0;
}

.button-icon {
  margin-right: 5px;
}

/* 新增：疾病选择下拉框 */
.disease-dropdown {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.85rem;
}

/* 新增：助诊功能样式 */
.diagnostic-assistant-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 0; /* Remove bottom margin */
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 210px); /* Adjust max height */
  padding: 0 5px;
  position: relative; /* Add position relative */
}

.diagnosis-card {
  background-color: white;
  border-radius: 4px;
  padding: 10px 10px 15px 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.toggle-button {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.toggle-button:hover {
  background-color: #e3f2fd;
}

.diagnosis-card.collapsed {
  padding-bottom: 5px;
}

.diagnosis-card h4 {
  margin: 0 0 8px 0;
  font-size: 0.95rem;
  color: #1976d2;
}

.diagnosis-description {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 10px;
}

.patient-selector {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selector-group {
  margin-bottom: 0;
}

.selector-group label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 4px;
}

.patient-dropdown {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
}

.action-button {
  width: 100%;
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.compare-button {
  background-color: #1976d2;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: normal;
  margin-top: 10px;
}

.compare-button:hover {
  background-color: #1565c0;
}

.ai-controls {
  margin-bottom: 10px;
}

.ai-options {
  margin-bottom: 10px;
}

.checkbox-container {
  display: block;
  position: relative;
  padding-left: 25px;
  margin-bottom: 6px;
  font-size: 0.8rem;
  cursor: pointer;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #eee;
  border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #2196F3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.insights-container {
  margin-bottom: 12px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 6px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.insight-icon {
  margin-right: 8px;
  font-size: 1rem;
}

.insight-content {
  flex: 1;
  font-size: 0.8rem;
}

.insight-text {
  color: #555;
}

.results-card {
  border-left: 4px solid #ff9800;
}

/* 新增：AI对话框样式 */
.ai-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.ai-dialog {
  background-color: white;
  border-radius: 6px;
  width: 80%;
  max-width: 700px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  color: #1976d2;
}

.close-button {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.dialog-content {
  padding: 0 20px;
  overflow-y: auto;
  flex: 1;
  max-height: 60vh;
}

.dialog-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin: 0 -20px 15px;
  padding: 0 20px;
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
}

.tab.active {
  border-bottom: 2px solid #1976d2;
  color: #1976d2;
}

.tab-panel {
  padding: 10px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.dialog-footer button {
  margin-left: 10px;
  padding: 8px 16px;
}

.primary-button {
  background-color: #1976d2;
}

.primary-button:hover {
  background-color: #1565c0;
}

/* 新增：小按钮样式 */
.mini-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 5px;
  font-size: 0.7rem;
  cursor: pointer;
  margin-top: 4px;
}

.mini-button:hover {
  background-color: #e0e0e0;
}

/* 新增：次要按钮样式 */
.secondary-button {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  margin-bottom: 8px;
}

.secondary-button:hover {
  background-color: #e0e0e0;
}

/* 新增：临床应用按钮组 */
.clinical-actions {
  display: flex;
  flex-direction: column;
}

/* 改进：复选框标签样式 */
.checkbox-label {
  font-size: 0.8rem;
}

/* 新增：聊天界面样式 */
.chat-panel {
  padding: 0 !important;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Fill the entire height */
  min-height: 280px; /* Reduced from 300px */
  overflow: hidden;
  padding-bottom: 0; /* Remove bottom padding */
  position: relative; /* Add position relative */
}

.chat-messages-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 180px; /* Reduced from 200px */
  max-height: calc(100% - 60px); /* Leave room for input */
  padding-bottom: 10px; /* Add padding at the bottom */
}

.chat-messages {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 10px;
  padding-bottom: 15px; /* 增加底部内边距 */
  display: flex;
  flex-direction: column;
}

.message {
  display: flex;
  max-width: 80%;
  margin-bottom: 8px;
  margin-top: 2px;
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  margin: 0 8px;
}

.message-content {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  max-width: 90%;
}

.ai-message .message-content {
  background-color: #e3f2fd;
  border-top-left-radius: 2px;
  border-left: 3px solid #1976d2;
}

.user-message .message-content {
  background-color: #e8f5e9;
  border-top-right-radius: 2px;
  border-right: 3px solid #4caf50;
}

.message-text {
  font-size: 0.9rem;
}

.message-time {
  font-size: 0.7rem;
  color: #888;
  text-align: right;
  margin-top: 4px;
}

.chat-input-container {
  padding: 10px;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 5;
  min-height: 50px; /* Reduced from 60px */
  box-sizing: border-box;
  display: flex;
  align-items: center;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  margin-top: auto;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.chat-input {
  flex: 1;
  padding: 8px 15px; /* Reduced top/bottom padding from 12px to 8px */
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 0.9rem; /* Reduced from 1rem */
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chat-send-button {
  margin-left: 8px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 15px; /* Reduced padding */
  font-size: 0.9rem;
  cursor: pointer;
  height: 36px; /* Reduced from 40px */
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-send-button:hover {
  background-color: #1565c0;
}

.typing-indicator {
  padding: 5px 0;
}

.typing-dots {
  display: flex;
  align-items: center;
  gap: 5px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #aaa;
  display: inline-block;
  animation: typing 1.4s infinite both;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    opacity: 0.3;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-3px);
  }
  100% {
    opacity: 0.3;
    transform: translateY(0);
  }
}

/* 确保AI聊天面板在比较面板收缩时扩展 */
.collapsed + .ai-chat-panel {
  max-height: calc(100vh - 80px);
}

/* 修改AI聊天面板样式，确保正确显示 */
.ai-chat-panel {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 400px; /* Increase min height */
  max-height: calc(100vh - 180px); /* Ensure there's room at the bottom */
  margin-top: 5px;
  margin-bottom: 0; /* Remove bottom margin */
  padding-bottom: 0; /* Remove bottom padding */
  position: relative; /* Add position relative for absolute positioning */
}

/* 添加图像标题样式 */
.image-header {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 8px;
  color: #1976d2;
}

/* 添加图像说明样式 */
.image-description {
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
  padding: 0 5px;
}

/* 添加疾病特征列表样式 */
.disease-features {
  margin-top: 10px;
  padding: 0 5px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;
  font-size: 0.8rem;
}

.feature-icon {
  margin-right: 5px;
  color: #1976d2;
}

.feature-text {
  flex: 1;
}

.node-label {
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  text-align: center;
}

.patient-connections {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 1;
}

.disease1-connections {
  top: 60px;
  left: 50px;
}

.disease2-connections {
  top: 60px;
  right: 50px;
}

.connection-line {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.line {
  width: 2px;
  height: 30px;
  background-color: #90caf9;
}

.patient-node {
  width: 100px;
  height: 30px;
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.patient-id {
  font-weight: bold;
  color: #333;
}

/* 修改疾病选择样式 */
.disease-comparison {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selector-group {
  margin-bottom: 8px;
}

.selector-group label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 4px;
  font-weight: bold;
}

.left-panel h2 {
  font-size: 1.1rem;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #1976d2;
  color: #333;
}

.graph-header {
  font-weight: bold;
  font-size: 1rem;
  color: #1976d2;
  margin-bottom: 15px;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  border-radius: 4px 4px 0 0;
}

.knowledge-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  width: 100%;
  flex: 1;
  min-height: 300px;
  overflow: hidden;
}

.patient-eye-image {
  width: 100%;
  height: auto;
  max-height: 120px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e0e0e0;
}

.text-content {
  font-size: 0.8rem;
  color: #333;
  padding: 5px;
  background-color: #f8f8f8;
  border-radius: 4px;
  max-height: 80px;
  overflow-y: auto;
  line-height: 1.4;
}

.comparison-item.full-height {
  flex: 1;
  height: calc(100vh - 350px);
  overflow-y: auto;
}

.indicator-visualization {
  height: 180px;
  overflow-y: hidden;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 10px;
}

.text-content {
  font-size: 0.8rem;
  color: #333;
  padding: 5px;
  background-color: #f8f8f8;
  border-radius: 4px;
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.4;
}

.indicator-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 0.9rem;
}

.patient-info {
  font-size: 0.85rem;
  color: #666;
  margin-left: auto;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 5px;
}

.indicator-visualization {
  height: 180px;
  overflow-y: hidden;
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 10px;
}

/* Add specific styles for the analysis container */
.analysis-container {
  padding: 10px;
  overflow-y: auto;
  max-height: 350px; /* Match the chat-messages-wrapper max height */
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 0;
}

.analysis-section {
  background-color: white;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 5px;
}

.analysis-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 0.95rem;
  color: #1976d2;
}

/* Adjust panel header with better tab spacing */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
}

.tab-buttons {
  display: flex;
  gap: 5px;
}

.tab-button {
  background: none;
  border: none;
  padding: 5px 10px;
  font-size: 0.9rem;
  cursor: pointer;
  border-radius: 4px;
}

.tab-button.active {
  background-color: #1976d2;
  color: white;
}

.disease-node {
  width: 120px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  margin: 10px 0;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.disease-node.disease1 {
  position: absolute;
  top: 20px;
  left: 50px;
}

.disease-node.disease2 {
  position: absolute;
  top: 20px;
  right: 50px;
}

.disease-node.macular_edema {
  background-color: #e53935;
}

.disease-node.open_angle_glaucoma {
  background-color: #ff9800;
}

.disease-node.closed_angle_glaucoma {
  background-color: #7b1fa2;
}

.disease-node.diabetic_maculopathy {
  background-color: #4caf50;
}

.disease-node.retinitis_pigmentosa {
  background-color: #9c27b0;
}

.disease-node.branch_retinal_vein_occlusion {
  background-color: #795548;
}

/* Add styles for the patient info summary */
.patient-info-summary {
  display: flex;
  justify-content: space-between;
  background-color: rgba(240, 240, 240, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  margin: 5px 0 10px 0;
  font-size: 0.9rem;
  border-left: 3px solid #1976d2;
}

.patient-info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: bold;
  margin-right: 5px;
  color: #333;
}

.info-value {
  color: #1976d2;
}

/* Add these CSS styles at the end of the style section */
.clustering-controls {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.clustering-title {
  font-size: 0.85rem;
  font-weight: bold;
  color: #555;
  margin-bottom: 8px;
}

.clustering-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.clustering-option {
  padding: 5px 12px;
  background-color: #e0e0e0;
  border-radius: 15px;
  font-size: 0.8rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.clustering-option:hover {
  background-color: #d0d0d0;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

/* Add this CSS to the <style> section */
.patient-visits-section {
  display: flex;
  background-color: #f5f9ff;
  border-radius: 4px;
  margin-bottom: 10px;
  border-left: 3px solid #ff7675;
  overflow: hidden;
}

.visits-stats {
  flex: 2;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
}

.visit-stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-label {
  font-size: 0.85rem;
  font-weight: bold;
  color: #555;
}

.stat-value {
  font-size: 1rem;
  color: #ff7675;
  font-weight: bold;
}

.trend-chart-container {
  width: 100%;
  height: 250px !important; /* 增加高度并使用!important确保应用 */
  margin-bottom: 15px;
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
}

.patient-trend-chart {
  width: 100%;
  height: 100% !important;
}

/* 调整图表容器布局 */
.charts-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 15px;
  height: 270px !important; /* 增加整体高度 */
}

/* 添加患者统计行样式 */
.patient-stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  padding: 5px;
  border-radius: 4px;
}

.patient-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 8px;
}

.stat-label {
  font-size: 0.7rem;
  color: #666;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: bold;
  color: #333;
}

/* 调整图像内容容器 */
.image-content-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  padding: 8px;
}

/* 调整图像比较部分 */
.image-comparison-enhanced {
  flex: 2;
  display: flex;
  margin-bottom: 10px;
  min-height: 350px !important; /* 增加最小高度 */
  max-height: 40vh !important; /* 增加最大高度 */
}

.raw-image-section, .cam-image-section {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  margin: 0 5px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.image-header {
  font-size: 0.85rem;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1976d2;
}

/* 调整panel头部样式使其更紧凑 */
.panel-header {
  font-weight: bold;
  padding: 5px 8px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

/* 调整面板控件样式使其更紧凑 */
.panel-controls {
  display: flex;
  padding: 3px 8px;
  border-bottom: 1px solid #eee;
  background-color: #fafafa;
  font-size: 12px;
}

/* 调整控件样式 */
.control, .filter-control, .label-control {
  padding: 2px 6px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 3px;
}

.control.active {
  background-color: #1976d2;
  color: white;
}

/* 精简聚类控件样式 */
.clustering-controls {
  display: flex;
  align-items: center;
  margin-top: 3px;
  height: 25px;
}

.clustering-title {
  font-size: 12px;
  margin-right: 5px;
  white-space: nowrap;
}

.clustering-options {
  display: flex;
  flex-wrap: wrap;
}

.clustering-option {
  font-size: 12px;
  padding: 2px 5px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 3px;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

/* 调整指标部分的样式 */
.indicator-section {
  margin-bottom: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  padding: 0 3px;
}

.indicator-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 0.85rem;
}

.patient-info {
  font-size: 0.75rem;
  color: #666;
  margin-left: auto;
}

.indicator-visualization {
  height: 180px;
  background-color: white;
  border-radius: 4px;
  padding: 5px;
  overflow: hidden;
  border: 1px solid #eee;
}

/* 优化小型图表样式 */
.small-chart {
  width: 100%;
  height: 100%;
  min-height: 160px;
}

/* Add collapsible panel styles */
.right-panel {
  width: 30%;
  background-color: #fff;
  border-left: 1px solid #ddd;
  overflow: hidden;
  transition: width 0.3s ease;
  position: relative;
}

.right-panel.collapsed {
  width: 30px;
  min-width: 30px;
}

.right-panel-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  transition: opacity 0.2s ease;
}

.collapsed .right-panel-content {
  opacity: 0;
  pointer-events: none;
}

.panel-pull-tab {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 0 5px 5px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  border: 1px solid #ddd;
  border-left: none;
}

.pull-tab-icon {
  font-size: 1.2rem;
  color: #555;
}

/* Adjust center content when right panel is collapsed */
.main-content.right-panel-collapsed .center-content {
  flex: 1;
}

/* Adjust left panel when right panel is collapsed */
.main-content.right-panel-collapsed .left-panel {
  flex: 0 0 20%;
}

/* Make charts responsive */
.chart {
  width: 100% !important;
  height: 100% !important;
  transition: all 0.3s ease;
}

.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px);
  transition: all 0.3s ease;
}

.center-content {
  flex: 1;
  transition: width 0.3s ease;
  overflow-y: auto;
}
</style> 