// Mock Patient Data Generator
// This module provides mock patient data for development and testing

const DIAGNOSIS_CODES = [
  'H35.804', // 黄斑水肿
  'H40.11', // 开角型青光眼
  'H40.21', // 闭角型青光眼
  'E11.319', // 2型糖尿病
  'H36.0', // 糖尿病视网膜病变
  'H35.351', // 湿性年龄相关性黄斑变性
  'H40.9', // 未特指的青光眼
  'H25.9', // 未特指的老年性白内障
];

const DEPARTMENT_CODES = [
  175, // 眼科
  150, // 内分泌科
  180, // 神经科
  160, // 心内科
  170, // 普外科
];

const CHIEF_COMPLAINTS = [
  '视力下降',
  '眼部疼痛',
  '眼睛干涩',
  '视物模糊',
  '复查',
  '视野缺损',
  '眼压高',
  '双眼视力下降',
  '右眼视力下降',
  '左眼视力下降',
  '看不清楚',
  '眼睛发红',
  '眼睛不舒服',
];

const ILLNESS_HISTORIES = [
  '患者2型糖尿病10年，血糖控制一般，近期出现视力下降。',
  '患有青光眼5年，定期复查，目前眼压控制稳定。',
  '白内障术后复查，恢复良好，视力有所提高。',
  '高度近视20年，近期感觉视力明显下降。',
  '双眼干涩不适，伴有视物模糊，查找原因。',
  '右眼突发视力下降，伴有眼前黑影。',
  '既往高血压病史10年，近期视物变形。',
  '糖尿病史15年，糖尿病视网膜病变5年，定期复查。',
  '突发眼痛，伴随头痛，眼压明显升高。',
  '视物模糊加重，曾诊断为黄斑变性，现复查。',
];

/**
 * Generate a random integer between min and max (inclusive)
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Generate a random floating point number between min and max with specified decimal places
 */
function randomFloat(min, max, decimals = 1) {
  const value = Math.random() * (max - min) + min;
  return Number(value.toFixed(decimals));
}

/**
 * Pick a random item from an array
 */
function randomPick(array) {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate a random date within the last n years
 */
function randomDate(yearsBack = 2) {
  const date = new Date();
  date.setFullYear(date.getFullYear() - randomInt(0, yearsBack));
  date.setMonth(randomInt(0, 11));
  date.setDate(randomInt(1, 28));
  return date.toISOString().slice(0, 10);
}

/**
 * Generate a random patient ID
 */
function generatePatientId() {
  return `80${randomInt(100000, 999999)}`;
}

/**
 * Generate random lab values
 */
function generateLabValues() {
  return [
    {
      name: 'HbA1c',
      value: randomFloat(4.5, 14.0, 1),
      unit: '%',
      reference: '4.0-6.0'
    },
    {
      name: 'eAG',
      value: randomFloat(4.0, 16.0, 1),
      unit: 'mmol/L',
      reference: '3.9-6.1'
    },
    {
      name: 'IOP_R',
      value: randomFloat(10.0, 30.0, 1),
      unit: 'mmHg',
      reference: '10-21'
    },
    {
      name: 'IOP_L',
      value: randomFloat(10.0, 30.0, 1),
      unit: 'mmHg',
      reference: '10-21'
    }
  ];
}

/**
 * Generate a single mock patient record
 */
function generatePatient() {
  const gender = Math.random() > 0.5 ? '男' : '女';
  const age = randomInt(18, 85);
  const mainDiagnosis = randomPick(DIAGNOSIS_CODES);
  const departmentCode = randomPick(DEPARTMENT_CODES);
  const visitCount = randomInt(1, 20);
  
  return {
    '就诊卡号': generatePatientId(),
    '姓名': `患者${randomInt(1000, 9999)}`,
    '性别': gender,
    '年龄': `${age}岁`,
    '诊断日期': randomDate(),
    '处置科室代码': departmentCode,
    '主诉': randomPick(CHIEF_COMPLAINTS),
    '现病史': randomPick(ILLNESS_HISTORIES),
    '主要诊断': mainDiagnosis,
    '检查项目': generateLabValues(),
    'visit_count': visitCount
  };
}

/**
 * Generate a dataset with the specified number of patients
 */
function generatePatientDataset(count = 500) {
  const patients = [];
  for (let i = 0; i < count; i++) {
    patients.push(generatePatient());
  }
  return patients;
}

export default {
  generatePatient,
  generatePatientDataset
}; 