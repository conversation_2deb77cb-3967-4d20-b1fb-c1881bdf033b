<template>
  <div class="disease-network">
    <div class="chart-container" ref="chartContainer"></div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, watch, defineComponent, nextTick } from 'vue';
import * as echarts from 'echarts';
import type { EChartsOption } from 'echarts';

interface Disease {
  id: string;
  name: string;
  color: string;
}

interface Patient {
  id: string;
  diseaseId: string;
}

export default defineComponent({
  props: {
    diseases: {
      type: Array as () => Disease[],
      default: () => []
    },
    patients: {
      type: Array as () => Patient[],
      default: () => []
    }
  },
  emits: ['node-click'],
  setup(props, { emit }) {
    const chartContainer = ref<HTMLElement | null>(null);
    let chart: echarts.ECharts | null = null;

    function renderChart() {
      if (!chartContainer.value || !chart) return;

      const nodes: any[] = [];
      const edges: any[] = [];
      
      // Calculate chart dimensions
      const chartWidth = chart.getWidth();
      const chartHeight = chart.getHeight();
      const centerX = chartWidth / 2;
      const centerY = chartHeight / 2;
      
      // Add disease nodes in center positions
      props.diseases.forEach((disease, index) => {
        // Position diseases in the center
        const x = centerX;
        const y = centerY;
        
        nodes.push({
          id: disease.id,
          name: disease.name,
          symbolSize: 60,
          x: x,
          y: y,
          fixed: true, // Fix position to prevent movement
          itemStyle: {
            color: disease.color,
            borderWidth: 2,
            borderColor: '#fff'
          },
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}',
            fontSize: 12,
            color: '#fff',
            fontWeight: 'bold'
          }
        });
      });
      
      // Add patient nodes in a circle around the disease
      const patientsByDisease: Record<string, Patient[]> = {};
      
      // Group patients by disease
      props.patients.forEach(patient => {
        if (!patientsByDisease[patient.diseaseId]) {
          patientsByDisease[patient.diseaseId] = [];
        }
        patientsByDisease[patient.diseaseId].push(patient);
      });
      
      // Add patient nodes for each disease in a circle
      Object.keys(patientsByDisease).forEach(diseaseId => {
        const patients = patientsByDisease[diseaseId];
        const disease = props.diseases.find(d => d.id === diseaseId);
        if (!disease) return;
        
        // Make a full circle of patient nodes
        const radius = Math.min(chartWidth, chartHeight) * 0.4; // Radius of the circle
        
        patients.forEach((patient, patientIndex) => {
          const patientId = `patient-${patient.id}`;
          const totalPatients = patients.length;
          
          // Calculate angle for circular layout (full 360 degrees)
          const angle = 2 * Math.PI * patientIndex / totalPatients;
          
          // Calculate position on the circle
          const x = centerX + Math.cos(angle) * radius;
          const y = centerY + Math.sin(angle) * radius;
          
          // Add patient node
          nodes.push({
            id: patientId,
            name: patient.id,
            diseaseId: patient.diseaseId, // Store diseaseId for reference
            symbolSize: 30,
            x: x,
            y: y,
            itemStyle: {
              color: '#64b5f6',
              borderWidth: 1,
              borderColor: '#fff',
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{b}',
              fontSize: 10,
              color: '#333',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              padding: [2, 4],
              borderRadius: 2
            },
            // Allow nodes to be dragged
            draggable: true
          });
          
          // Connect patient to disease
          edges.push({
            source: disease.id,
            target: patientId,
            lineStyle: {
              width: 1.5,
              opacity: 0.7,
              color: disease.color,
              curveness: 0.1
            }
          });
        });
      });
      
      const option: any = {
        tooltip: {
          trigger: 'item',
          formatter: function(params: any) {
            if (params.dataType === 'node') {
              if (params.data.id.includes('patient')) {
                return `<div style="padding: 8px 12px;">
                  <div style="font-weight: bold; margin-bottom: 5px;">Patient ID: ${params.data.name}</div>
                  <div>Click to view patient details</div>
                </div>`;
              } else {
                return `<div style="padding: 8px 12px;">
                  <div style="font-weight: bold; margin-bottom: 5px;">${params.data.name}</div>
                  <div>Click to view disease details</div>
                </div>`;
              }
            }
            return params.name;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          padding: 0,
          textStyle: {
            color: '#333',
            fontSize: 12
          },
          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;'
        },
        animationDuration: 1000,
        animationEasingUpdate: 'cubicOut',
        series: [
          {
            type: 'graph',
            layout: 'none',
            data: nodes,
            edges: edges,
            roam: true,
            zoom: 0.9,
            draggable: true,
            focusNodeAdjacency: true,
            label: {
              show: true
            },
            edgeSymbol: ['none', 'arrow'],
            edgeSymbolSize: [0, 5],
            lineStyle: {
              width: 1.5,
              curveness: 0.1,
              opacity: 0.7
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 2.5
              },
              itemStyle: {
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
              },
              label: {
                fontWeight: 'bold'
              }
            }
          }
        ]
      };
      
      chart.setOption(option);
    }

    onMounted(() => {
      if (chartContainer.value) {
        chart = echarts.init(chartContainer.value);
        renderChart();
        
        // Add click event for nodes
        chart.on('click', function(params) {
          if (params.dataType === 'node') {
            // Emit event to parent component
            emit('node-click', params.data);
          }
        });
        
        // Improve resize handling
        const resizeObserver = new ResizeObserver(() => {
          chart?.resize();
          setTimeout(() => renderChart(), 300); // Re-render after resize
        });
        
        resizeObserver.observe(chartContainer.value);
        
        window.addEventListener('resize', () => {
          chart?.resize();
          setTimeout(() => renderChart(), 300); // Re-render after window resize
        });
      }
    });

    watch(
      () => [props.diseases, props.patients],
      () => {
        // Use nextTick to ensure the component is fully rendered
        nextTick(() => {
          if (chart) {
            chart.resize();
            renderChart();
          }
        });
      },
      { deep: true }
    );

    return {
      chartContainer
    };
  }
});
</script>

<style scoped>
.disease-network {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style> 