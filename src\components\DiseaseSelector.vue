<template>
  <div class="disease-selector">
    <h3>疾病选择</h3>
    
    <div class="search-box">
      <input 
        type="text" 
        v-model="searchQuery" 
        placeholder="搜索疾病..." 
        @input="filterDiseases"
      />
    </div>
    
    <div class="disease-list" v-if="filteredDiseases.length > 0">
      <div 
        v-for="disease in filteredDiseases" 
        :key="disease" 
        class="disease-item"
        :class="{ selected: selectedDisease === disease }"
        @click="selectDisease(disease)"
      >
        {{ disease }}
      </div>
    </div>
    <div v-else class="no-diseases">
      没有找到匹配的疾病
    </div>
    
    <div class="disease-info" v-if="selectedDisease">
      <h4>已选疾病: {{ selectedDisease }}</h4>
      <div class="patient-count">
        患者数量: {{ getPatientCountForDisease(selectedDisease) }}
      </div>
    </div>
  </div>
</template>

<script>
import patientDataProcessor from '../utils/patientDataProcessor';

export default {
  name: 'DiseaseSelector',
  data() {
    return {
      diseases: [],
      filteredDiseases: [],
      selectedDisease: null,
      searchQuery: '',
      isLoading: false
    };
  },
  mounted() {
    this.loadDiseases();
  },
  methods: {
    async loadDiseases() {
      this.isLoading = true;
      
      try {
        // Wait for patient data to be loaded
        if (!patientDataProcessor.rawData) {
          await patientDataProcessor.loadData();
          await patientDataProcessor.processData();
        }
        
        // Get all unique diseases
        this.diseases = patientDataProcessor.getUniqueDiseases();
        this.filteredDiseases = [...this.diseases];
        
        if (this.diseases.length > 0) {
          this.selectedDisease = this.diseases[0];
          this.$emit('disease-selected', this.selectedDisease);
        }
      } catch (error) {
        console.error('Error loading diseases:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    filterDiseases() {
      if (!this.searchQuery.trim()) {
        this.filteredDiseases = [...this.diseases];
        return;
      }
      
      const query = this.searchQuery.toLowerCase();
      this.filteredDiseases = this.diseases.filter(disease => 
        disease.toLowerCase().includes(query)
      );
    },
    
    selectDisease(disease) {
      this.selectedDisease = disease;
      this.$emit('disease-selected', disease);
    },
    
    getPatientCountForDisease(disease) {
      if (!patientDataProcessor.processedData) return 0;
      
      return patientDataProcessor.processedData.filter(patient => 
        patient.categorical.main_diagnosis_code === disease
      ).length;
    }
  }
};
</script>

<style scoped>
.disease-selector {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 20px;
}

.disease-selector h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.search-box {
  margin-bottom: 15px;
}

.search-box input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.disease-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.disease-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.disease-item:hover {
  background-color: #f5f5f5;
}

.disease-item.selected {
  background-color: #e3f2fd;
  font-weight: 500;
}

.no-diseases {
  padding: 15px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.disease-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.disease-info h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.patient-count {
  font-size: 14px;
  color: #666;
}
</style> 