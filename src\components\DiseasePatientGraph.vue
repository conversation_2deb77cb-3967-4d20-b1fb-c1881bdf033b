<template>
  <div class="chart-container">
    <div ref="chartContainer" style="width: 100%; height: 400px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

interface Edge {
  source: string;
  target: string;
  lineStyle: {
    width: number;
    opacity: number;
  };
}

const props = defineProps({
  diseaseName: {
    type: String,
    default: 'Disease'
  },
  patientIds: {
    type: Array as () => string[],
    default: () => []
  }
});

const chartContainer = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

function renderChart() {
  if (!chartContainer.value || !myChart) return;

  // Create data nodes
  const data = [
    {
      id: 'disease',
      name: props.diseaseName,
      symbolSize: 50,
      itemStyle: {
        color: '#4e79a7'
      },
      x: myChart.getWidth() / 2,
      y: myChart.getHeight() / 2,
      fixed: true,
      label: {
        show: true,
        position: 'inside',
        formatter: '{b}',
        fontSize: 12,
        color: '#fff'
      }
    }
  ];

  // Create patient nodes
  const edges: Edge[] = [];
  const patientCount = props.patientIds.length;
  
  props.patientIds.forEach((patientId, index) => {
    // Calculate position in a circle around the disease
    const angle = (Math.PI * 2 / patientCount) * index;
    const radius = 150;
    
    if (myChart) {
      data.push({
        id: `patient-${index}`,
        name: patientId,
        symbolSize: 30,
        itemStyle: {
          color: '#90be6d'
        },
        x: myChart.getWidth() / 2 + Math.cos(angle) * radius,
        y: myChart.getHeight() / 2 + Math.sin(angle) * radius,
        fixed: true,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}',
          fontSize: 10,
          color: '#000'
        }
      });

      // Create edge from disease to patient
      edges.push({
        source: 'disease',
        target: `patient-${index}`,
        lineStyle: {
          width: 1,
          opacity: 0.6
        }
      });
    }
  });

  // Set chart options
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}'
    },
    series: [
      {
        type: 'graph',
        layout: 'none', // Using our custom layout
        data: data,
        edges: edges,
        roam: true,
        label: {
          show: true
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 2
          }
        }
      }
    ]
  };

  myChart.setOption(option);
}

onMounted(() => {
  if (chartContainer.value) {
    myChart = echarts.init(chartContainer.value);
    renderChart();
    
    // Handle resize
    window.addEventListener('resize', () => {
      myChart?.resize();
    });
  }
});

watch(
  () => [props.diseaseName, props.patientIds],
  () => {
    renderChart();
  },
  { deep: true }
);
</script>

<style scoped>
.chart-container {
  width: 100%;
  padding: 20px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>