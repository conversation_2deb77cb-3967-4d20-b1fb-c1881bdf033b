#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于大语言模型的文本知识图谱构建系统
LLM-Based Text Knowledge Graph Construction System

作者: AI Assistant
日期: 2025-06-25
版本: 1.0

功能描述:
- 将非结构化文本转换为结构化知识图谱
- 使用LLM提取SPO三元组
- 构建NetworkX图结构
- 支持交互式可视化

依赖库:
pip install openai networkx ipycytoscape ipywidgets pandas
"""

import openai
import json
import networkx as nx
import pandas as pd
import os
import math
import re
import warnings
from typing import List, Dict, Tuple, Optional, Any
import sys

# 配置设置
warnings.filterwarnings('ignore', category=DeprecationWarning)
pd.set_option('display.max_rows', 100)
pd.set_option('display.max_colwidth', 150)


class LLMTextKnowledgeGraph:
    """基于LLM的文本知识图谱构建器"""
    
    def __init__(self, 
                 model_name: str = "deepseek-chat",
                 temperature: float = 0.0,
                 max_tokens: int = 4096,
                 chunk_size: int = 150,
                 overlap: int = 30):
        """
        初始化知识图谱构建器
        
        Args:
            model_name: LLM模型名称
            temperature: 温度参数，控制输出随机性
            max_tokens: 最大token数
            chunk_size: 文本分块大小
            overlap: 分块重叠词数
        """
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.chunk_size = chunk_size
        self.overlap = overlap
        
        # 初始化客户端
        self.client = None
        self._init_client()
        
        # 数据存储
        self.chunks = []
        self.all_extracted_triples = []
        self.normalized_triples = []
        self.failed_chunks = []
        self.knowledge_graph = nx.DiGraph()
        
        # 统计信息
        self.processing_stats = {
            'total_chunks': 0,
            'processed_chunks': 0,
            'successful_chunks': 0,
            'failed_chunks': 0,
            'total_triples': 0
        }
        
        # 提示词模板
        self._init_prompts()
    
    def _init_client(self):
        """初始化OpenAI客户端"""
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_API_BASE")

        if not api_key:
            raise ValueError("未找到OPENAI_API_KEY环境变量，请设置API密钥")

        try:
            self.client = openai.OpenAI(
                base_url=base_url,
                api_key=api_key
            )
            print("✅ OpenAI客户端初始化成功")

            # 验证模型可用性
            self._validate_model()

        except Exception as e:
            raise RuntimeError(f"OpenAI客户端初始化失败: {e}")

    def _validate_model(self):
        """验证模型可用性并提供建议"""
        try:
            # 尝试获取可用模型列表
            models = self.client.models.list()
            available_models = [model.id for model in models.data]

            if self.model_name not in available_models:
                print(f"⚠️  警告: 模型 '{self.model_name}' 可能不可用")

                # 推荐常见模型
                common_models = ["gpt-3.5-turbo", "gpt-4", "deepseek-chat", "claude-3-sonnet"]
                available_common = [m for m in common_models if m in available_models]

                if available_common:
                    print(f"💡 建议使用以下可用模型: {', '.join(available_common[:3])}")

                    # 自动选择第一个可用的常见模型
                    recommended_model = available_common[0]
                    print(f"🔄 自动切换到模型: {recommended_model}")
                    self.model_name = recommended_model
                else:
                    print(f"📋 可用模型列表: {', '.join(available_models[:5])}...")

        except Exception as e:
            print(f"⚠️  无法验证模型可用性: {e}")
            print(f"🔄 继续使用配置的模型: {self.model_name}")
    
    def _init_prompts(self):
        """初始化提示词模板"""
        self.system_prompt = """
        你是一个专门从事知识图谱提取的AI专家。
        你的任务是从给定文本中识别和提取事实性的主语-谓语-宾语(SPO)三元组。
        专注于准确性，严格遵循用户提示中要求的JSON输出格式。
        提取核心实体和最直接的关系。
        """
        
        self.user_prompt_template = """
        请从下面的文本中提取主语-谓语-宾语(S-P-O)三元组。

        **重要规则:**
        1. **输出格式**: 只返回一个有效的JSON数组。每个元素必须是包含"subject"、"predicate"、"object"键的对象。
        2. **纯JSON**: 不要包含JSON数组前后的任何文本。不要使用markdown标签。
        3. **简洁谓语**: 保持'predicate'值简洁(1-3个词)。使用动词或短动词短语。
        4. **小写**: 'subject'、'predicate'和'object'的所有值必须是小写。
        5. **代词解析**: 将代词替换为具体的实体名称。
        6. **具体性**: 捕获具体细节。
        7. **完整性**: 提取所有提到的不同事实关系。

        **要处理的文本:**
        ```text
        {text_chunk}
        ```

        **必需的JSON输出格式示例:**
        [
          {{"subject": "患者", "predicate": "患有", "object": "青光眼"}},
          {{"subject": "医生", "predicate": "处方", "object": "滴眼液"}}
        ]

        **你的JSON输出(必须以'['开始，以']'结束):**
        """
    
    def split_text_into_chunks(self, text: str) -> List[Dict[str, Any]]:
        """
        将文本分割为重叠的块
        
        Args:
            text: 输入文本
            
        Returns:
            文本块列表
        """
        print(f"🔄 开始文本分块处理...")
        
        words = text.split()
        total_words = len(words)
        chunks = []
        start_index = 0
        chunk_number = 1
        
        print(f"   总词数: {total_words}")
        print(f"   块大小: {self.chunk_size}, 重叠: {self.overlap}")
        
        while start_index < total_words:
            end_index = min(start_index + self.chunk_size, total_words)
            chunk_text = " ".join(words[start_index:end_index])
            
            chunks.append({
                "text": chunk_text,
                "chunk_number": chunk_number,
                "start_word": start_index,
                "end_word": end_index - 1,
                "word_count": end_index - start_index
            })
            
            # 计算下一个块的起始位置
            next_start_index = start_index + self.chunk_size - self.overlap
            
            # 确保处理有进展
            if next_start_index <= start_index:
                if end_index == total_words:
                    break
                next_start_index = start_index + 1
            
            start_index = next_start_index
            chunk_number += 1
            
            # 安全检查
            if chunk_number > total_words:
                print("⚠️  警告: 分块循环异常，强制中断")
                break
        
        self.chunks = chunks
        self.processing_stats['total_chunks'] = len(chunks)
        
        print(f"✅ 分块完成: 共创建 {len(chunks)} 个文本块")
        return chunks
    
    def extract_triples_from_chunk(self, chunk_info: Dict[str, Any], max_retries: int = 2) -> List[Dict[str, Any]]:
        """
        从单个文本块提取三元组

        Args:
            chunk_info: 文本块信息
            max_retries: 最大重试次数

        Returns:
            提取的三元组列表
        """
        chunk_text = chunk_info['text']
        chunk_num = chunk_info['chunk_number']

        for attempt in range(max_retries + 1):
            try:
                # 格式化提示词
                user_prompt = self.user_prompt_template.format(text_chunk=chunk_text)

                print(f"   🤖 调用LLM API (尝试 {attempt + 1}/{max_retries + 1})...")

                # 调用LLM API
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    response_format={"type": "json_object"},
                )

                # 提取响应内容
                llm_output = response.choices[0].message.content.strip()
                print(f"   📄 收到响应，长度: {len(llm_output)} 字符")

                # 解析JSON
                parsed_json = self._parse_llm_response(llm_output)

                if parsed_json is None:
                    print(f"   ⚠️  JSON解析失败，原始响应: {llm_output[:200]}...")
                    if attempt < max_retries:
                        print(f"   🔄 准备重试...")
                        continue
                    return []

                # 验证三元组
                valid_triples = self._validate_triples(parsed_json, chunk_num)
                print(f"   ✅ 成功提取 {len(valid_triples)} 个有效三元组")

                return valid_triples

            except Exception as e:
                error_msg = str(e)
                print(f"   ❌ 尝试 {attempt + 1} 失败: {error_msg}")

                # 分析错误类型并提供建议
                if "Model Not Exist" in error_msg or "model" in error_msg.lower():
                    print(f"   💡 模型错误建议: 请检查模型名称 '{self.model_name}' 是否正确")
                    print(f"   💡 常用模型: gpt-3.5-turbo, gpt-4, deepseek-chat")
                elif "401" in error_msg or "authentication" in error_msg.lower():
                    print(f"   💡 认证错误建议: 请检查API密钥是否正确")
                elif "429" in error_msg or "rate limit" in error_msg.lower():
                    print(f"   💡 限流建议: API调用频率过高，请稍后重试")
                elif "timeout" in error_msg.lower():
                    print(f"   💡 超时建议: 网络连接问题，请检查网络状态")

                if attempt < max_retries:
                    print(f"   🔄 {3 - attempt} 秒后重试...")
                    import time
                    time.sleep(3 - attempt)
                else:
                    # 记录最终失败
                    self.failed_chunks.append({
                        'chunk_number': chunk_num,
                        'error_type': 'API_ERROR',
                        'error': error_msg,
                        'response': '',
                        'attempts': max_retries + 1
                    })
                    return []
    
    def _parse_llm_response(self, llm_output: str) -> Optional[List[Dict[str, Any]]]:
        """解析LLM响应"""
        try:
            # 直接解析JSON
            parsed_data = json.loads(llm_output)
            
            # 处理不同的响应格式
            if isinstance(parsed_data, dict):
                # 查找包含三元组列表的值
                list_values = [v for v in parsed_data.values() if isinstance(v, list)]
                if len(list_values) == 1:
                    return list_values[0]
                else:
                    raise ValueError("字典中没有找到唯一的三元组列表")
            elif isinstance(parsed_data, list):
                return parsed_data
            else:
                raise ValueError("解析的JSON格式不正确")
                
        except json.JSONDecodeError:
            # 正则表达式回退
            match = re.search(r'\s*(\[.*?\])\s*$', llm_output, re.DOTALL)
            if match:
                try:
                    return json.loads(match.group(1))
                except json.JSONDecodeError:
                    pass
            return None
    
    def _validate_triples(self, parsed_json: List[Dict[str, Any]], chunk_num: int) -> List[Dict[str, Any]]:
        """验证三元组结构"""
        valid_triples = []
        
        if not isinstance(parsed_json, list):
            return valid_triples
        
        for item in parsed_json:
            if (isinstance(item, dict) and 
                all(k in item for k in ['subject', 'predicate', 'object']) and
                all(isinstance(item[k], str) for k in ['subject', 'predicate', 'object'])):
                
                item['source_chunk'] = chunk_num
                valid_triples.append(item)
        
        return valid_triples

    def process_all_chunks(self) -> List[Dict[str, Any]]:
        """
        处理所有文本块，提取三元组

        Returns:
            所有提取的三元组列表
        """
        print(f"🚀 开始处理 {len(self.chunks)} 个文本块...")

        self.all_extracted_triples = []
        self.failed_chunks = []

        for i, chunk_info in enumerate(self.chunks):
            print(f"\n🔄 处理块 {i+1}/{len(self.chunks)}")

            triples = self.extract_triples_from_chunk(chunk_info)
            self.all_extracted_triples.extend(triples)

            if triples:
                self.processing_stats['successful_chunks'] += 1
                print(f"   ✅ 提取到 {len(triples)} 个三元组")
            else:
                self.processing_stats['failed_chunks'] += 1
                print(f"   ❌ 未提取到有效三元组")

            self.processing_stats['processed_chunks'] += 1

            # 显示进度
            progress = (i + 1) / len(self.chunks) * 100
            print(f"   📈 进度: {progress:.1f}%")

        self.processing_stats['total_triples'] = len(self.all_extracted_triples)

        print(f"\n🎉 处理完成!")
        print(f"   成功块数: {self.processing_stats['successful_chunks']}")
        print(f"   失败块数: {self.processing_stats['failed_chunks']}")
        print(f"   总三元组数: {self.processing_stats['total_triples']}")

        return self.all_extracted_triples

    def normalize_and_deduplicate(self) -> List[Dict[str, Any]]:
        """
        标准化和去重三元组

        Returns:
            清洗后的三元组列表
        """
        print(f"🧹 开始数据清洗和去重...")

        normalized_triples = []
        seen_triples = set()

        original_count = len(self.all_extracted_triples)
        empty_removed = 0
        duplicates_removed = 0

        for triple in self.all_extracted_triples:
            # 提取和标准化
            subject = triple.get('subject', '').strip().lower()
            predicate = re.sub(r'\s+', ' ', triple.get('predicate', '').strip().lower()).strip()
            obj = triple.get('object', '').strip().lower()
            source_chunk = triple.get('source_chunk', 'unknown')

            # 过滤空值
            if not all([subject, predicate, obj]):
                empty_removed += 1
                continue

            # 去重检查
            triple_signature = (subject, predicate, obj)

            if triple_signature not in seen_triples:
                normalized_triples.append({
                    'subject': subject,
                    'predicate': predicate,
                    'object': obj,
                    'source_chunk': source_chunk
                })
                seen_triples.add(triple_signature)
            else:
                duplicates_removed += 1

        self.normalized_triples = normalized_triples

        print(f"✅ 数据清洗完成:")
        print(f"   原始数量: {original_count}")
        print(f"   移除空值: {empty_removed}")
        print(f"   移除重复: {duplicates_removed}")
        print(f"   最终数量: {len(normalized_triples)}")

        return normalized_triples

    def build_knowledge_graph(self) -> nx.DiGraph:
        """
        构建知识图谱

        Returns:
            NetworkX有向图
        """
        print(f"🕸️  构建知识图谱...")

        self.knowledge_graph = nx.DiGraph()

        for triple in self.normalized_triples:
            subject = triple['subject']
            predicate = triple['predicate']
            obj = triple['object']
            source_chunk = triple['source_chunk']

            # 添加节点(如果不存在)
            if not self.knowledge_graph.has_node(subject):
                self.knowledge_graph.add_node(subject, node_type='entity', frequency=0)

            if not self.knowledge_graph.has_node(obj):
                self.knowledge_graph.add_node(obj, node_type='entity', frequency=0)

            # 增加节点频率
            self.knowledge_graph.nodes[subject]['frequency'] += 1
            self.knowledge_graph.nodes[obj]['frequency'] += 1

            # 添加边
            if self.knowledge_graph.has_edge(subject, obj):
                # 更新现有边
                edge_data = self.knowledge_graph[subject][obj]
                if 'predicates' not in edge_data:
                    edge_data['predicates'] = []
                edge_data['predicates'].append(predicate)
                edge_data['weight'] = edge_data.get('weight', 0) + 1
                edge_data['sources'] = edge_data.get('sources', []) + [source_chunk]
            else:
                # 添加新边
                self.knowledge_graph.add_edge(
                    subject, obj,
                    predicate=predicate,
                    predicates=[predicate],
                    weight=1,
                    sources=[source_chunk]
                )

        print(f"✅ 知识图谱构建完成:")
        print(f"   节点数: {self.knowledge_graph.number_of_nodes()}")
        print(f"   边数: {self.knowledge_graph.number_of_edges()}")

        return self.knowledge_graph

    def analyze_graph(self) -> Dict[str, Any]:
        """
        分析图结构

        Returns:
            图分析结果
        """
        if self.knowledge_graph.number_of_nodes() == 0:
            return {}

        print(f"🔍 进行图分析...")

        # 计算度中心性
        degree_centrality = nx.degree_centrality(self.knowledge_graph)

        # 计算连通性
        undirected_graph = self.knowledge_graph.to_undirected()
        connected_components = list(nx.connected_components(undirected_graph))

        # 重要节点
        top_nodes_by_degree = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:10]
        top_nodes_by_frequency = sorted(
            [(node, data['frequency']) for node, data in self.knowledge_graph.nodes(data=True)],
            key=lambda x: x[1], reverse=True
        )[:10]

        # 谓语统计
        predicate_counts = {}
        for _, _, edge_data in self.knowledge_graph.edges(data=True):
            predicates = edge_data.get('predicates', [edge_data.get('predicate', '')])
            for pred in predicates:
                predicate_counts[pred] = predicate_counts.get(pred, 0) + 1

        top_predicates = sorted(predicate_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        analysis_results = {
            'nodes_count': self.knowledge_graph.number_of_nodes(),
            'edges_count': self.knowledge_graph.number_of_edges(),
            'connected_components': len(connected_components),
            'largest_component_size': len(max(connected_components, key=len)) if connected_components else 0,
            'top_nodes_by_degree': top_nodes_by_degree,
            'top_nodes_by_frequency': top_nodes_by_frequency,
            'top_predicates': top_predicates
        }

        print(f"📈 图分析完成:")
        print(f"   连通组件数: {analysis_results['connected_components']}")
        print(f"   最大连通组件: {analysis_results['largest_component_size']} 节点")

        return analysis_results

    def prepare_visualization_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        准备可视化数据

        Returns:
            Cytoscape格式的图数据
        """
        if self.knowledge_graph.number_of_nodes() == 0:
            return {'nodes': [], 'edges': []}

        print(f"🎨 准备可视化数据...")

        # 计算节点大小缩放
        max_frequency = max([data['frequency'] for _, data in self.knowledge_graph.nodes(data=True)])
        min_size, max_size = 20, 80

        # 创建节点数据
        nodes_data = []
        for node, data in self.knowledge_graph.nodes(data=True):
            frequency = data.get('frequency', 1)
            size = min_size + (max_size - min_size) * (frequency / max_frequency) if max_frequency > 0 else min_size

            nodes_data.append({
                'data': {
                    'id': node,
                    'label': node,
                    'frequency': frequency,
                    'size': size
                }
            })

        # 创建边数据
        edges_data = []
        for source, target, data in self.knowledge_graph.edges(data=True):
            weight = data.get('weight', 1)
            predicate = data.get('predicate', '')
            predicates = data.get('predicates', [predicate])

            width = min(1 + weight * 2, 10)

            edges_data.append({
                'data': {
                    'id': f"{source}-{target}",
                    'source': source,
                    'target': target,
                    'label': ', '.join(predicates[:2]),
                    'weight': weight,
                    'width': width,
                    'predicates': predicates
                }
            })

        print(f"✅ 可视化数据准备完成: {len(nodes_data)} 节点, {len(edges_data)} 边")

        return {'nodes': nodes_data, 'edges': edges_data}

    def save_results(self, output_dir: str = "output"):
        """
        保存结果到文件

        Args:
            output_dir: 输出目录
        """
        import os

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        print(f"💾 保存结果到 {output_dir} 目录...")

        # 保存三元组
        if self.normalized_triples:
            triples_df = pd.DataFrame(self.normalized_triples)
            triples_df.to_csv(f"{output_dir}/triples.csv", index=False, encoding='utf-8')
            print(f"   ✅ 三元组已保存: {output_dir}/triples.csv")

        # 保存图数据
        if self.knowledge_graph.number_of_nodes() > 0:
            try:
                nx.write_gexf(self.knowledge_graph, f"{output_dir}/knowledge_graph.gexf")
                print(f"   ✅ 图数据已保存: {output_dir}/knowledge_graph.gexf")
            except Exception as e:
                print(f"   ⚠️  GEXF保存失败: {e}")
                # 尝试其他格式
                try:
                    nx.write_graphml(self.knowledge_graph, f"{output_dir}/knowledge_graph.graphml")
                    print(f"   ✅ 图数据已保存为GraphML: {output_dir}/knowledge_graph.graphml")
                except Exception as e2:
                    print(f"   ⚠️  GraphML保存也失败: {e2}")

        # 保存统计信息
        stats = {
            'processing_stats': self.processing_stats,
            'graph_stats': {
                'nodes': self.knowledge_graph.number_of_nodes(),
                'edges': self.knowledge_graph.number_of_edges()
            }
        }

        with open(f"{output_dir}/statistics.json", 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"   ✅ 统计信息已保存: {output_dir}/statistics.json")

    def process_text(self, text: str) -> Dict[str, Any]:
        """
        完整的文本处理流程

        Args:
            text: 输入文本

        Returns:
            处理结果
        """
        print("🚀 开始完整的文本知识图谱构建流程...")

        # 1. 文本分块
        chunks = self.split_text_into_chunks(text)

        # 2. 提取三元组
        all_triples = self.process_all_chunks()

        # 3. 数据清洗
        normalized_triples = self.normalize_and_deduplicate()

        # 4. 构建图
        knowledge_graph = self.build_knowledge_graph()

        # 5. 图分析
        analysis_results = self.analyze_graph()

        # 6. 准备可视化数据
        viz_data = self.prepare_visualization_data()

        results = {
            'chunks_count': len(chunks),
            'raw_triples_count': len(all_triples),
            'normalized_triples_count': len(normalized_triples),
            'graph_nodes': knowledge_graph.number_of_nodes(),
            'graph_edges': knowledge_graph.number_of_edges(),
            'processing_stats': self.processing_stats,
            'analysis_results': analysis_results,
            'visualization_data': viz_data,
            'normalized_triples': normalized_triples,
            'knowledge_graph': knowledge_graph
        }

        print("🎉 知识图谱构建完成!")
        return results


def detect_available_model():
    """检测可用的模型"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")

    if not api_key:
        return "gpt-3.5-turbo"  # 默认模型

    try:
        client = openai.OpenAI(base_url=base_url, api_key=api_key)
        models = client.models.list()
        available_models = [model.id for model in models.data]

        # 按优先级选择模型
        preferred_models = ["deepseek-chat", "gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"]

        for model in preferred_models:
            if model in available_models:
                print(f"🎯 检测到可用模型: {model}")
                return model

        # 如果没有找到首选模型，返回第一个可用模型
        if available_models:
            selected_model = available_models[0]
            print(f"🎯 使用第一个可用模型: {selected_model}")
            return selected_model

    except Exception as e:
        print(f"⚠️  模型检测失败: {e}")

    return "gpt-3.5-turbo"  # 默认回退


def main():
    """主函数 - 演示完整的使用流程"""

    # 示例医疗文本
    sample_text = """
    患者年龄：[具体年龄]

性别：[男 / 女]

# 眼科疾病患者自我描述

大约 1 个月前，我开始出现眼部不适症状。最开始是双眼干涩，感觉有异物在眼皮后摩擦，眨眼时尤为明显，到晚上症状会加重，使用人工泪液后有一定缓解，但效果短暂。紧接着，我察觉到视力有明显下降，看东西模糊，尤其是看手机、电脑屏幕时，文字和图像边缘像是蒙了一层雾，需要凑近才能看清；看远处的物体，如交通标志、建筑物轮廓等，也变得模糊不清，甚至有些扭曲变形，偶尔还会出现重影。此外，我的左眼眼白处开始发红，一开始只是小片区域，后来逐渐扩散，伴有轻微的烧灼感和痒感，按压眼睑周围有轻微疼痛。右眼也出现轻微发红，但程度较左眼轻。

发病前一周，我因工作需要长时间熬夜赶项目，几乎每天只睡四五个小时，而且长时间盯着电脑屏幕，用眼环境光线较暗。发病后，我自行购买了眼药水使用，但症状没有明显改善，反而有加重趋势。

既往身体状况一直良好，没有其他慢性疾病，也没有眼部手术或外伤史。家族中没有遗传性眼病史。我担心自己的眼睛会持续恶化，希望医生能帮助我明确病因，尽快治疗，恢复视力，让我能正常生活和工作。
    """

    try:
        # 自动检测可用模型
        print("🔍 检测可用的LLM模型...")
        available_model = detect_available_model()

        # 创建知识图谱构建器
        print(f"🤖 使用模型: {available_model}")
        kg_builder = LLMTextKnowledgeGraph(
            model_name=available_model,
            temperature=0.0,
            max_tokens=4096,
            chunk_size=150,
            overlap=30
        )

        # 处理文本
        results = kg_builder.process_text(sample_text)

        # 显示结果摘要
        print("\n" + "="*60)
        print("📊 处理结果摘要:")
        print(f"   文本块数: {results['chunks_count']}")
        print(f"   原始三元组数: {results['raw_triples_count']}")
        print(f"   清洗后三元组数: {results['normalized_triples_count']}")
        print(f"   图节点数: {results['graph_nodes']}")
        print(f"   图边数: {results['graph_edges']}")

        # 显示重要节点
        if results['analysis_results']:
            print(f"\n🔍 重要节点 (按频率):")
            for i, (node, freq) in enumerate(results['analysis_results']['top_nodes_by_frequency'][:5], 1):
                print(f"   {i}. {node}: {freq} 次")

        # 显示常见关系
        if results['analysis_results']:
            print(f"\n🔗 常见关系:")
            for i, (pred, count) in enumerate(results['analysis_results']['top_predicates'][:5], 1):
                print(f"   {i}. '{pred}': {count} 次")

        # 保存结果
        kg_builder.save_results("output")

        # 显示部分三元组
        print(f"\n📋 示例三元组:")
        if results['normalized_triples']:
            triples_df = pd.DataFrame(results['normalized_triples'][:10])
            print(triples_df[['subject', 'predicate', 'object']].to_string(index=False))
        else:
            print("   没有提取到有效的三元组数据")

        print("\n🎉 处理完成! 结果已保存到 output/ 目录")

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")

        # 提供具体的错误解决建议
        error_msg = str(e).lower()
        print("\n🔧 错误解决建议:")

        if "model not exist" in error_msg or "model" in error_msg:
            print("   1. 检查模型名称是否正确")
            print("   2. 确认您的API提供商支持该模型")
            print("   3. 尝试使用其他模型，如: gpt-3.5-turbo, deepseek-chat")

        elif "api" in error_msg or "key" in error_msg:
            print("   1. 检查OPENAI_API_KEY环境变量是否设置正确")
            print("   2. 确认API密钥有效且有足够余额")
            print("   3. 检查OPENAI_API_BASE是否设置正确（如果使用第三方API）")

        elif "network" in error_msg or "connection" in error_msg:
            print("   1. 检查网络连接")
            print("   2. 确认API服务可访问")
            print("   3. 尝试使用VPN或代理")

        else:
            print("   1. 检查所有依赖是否正确安装")
            print("   2. 确认Python版本兼容性")
            print("   3. 查看详细错误信息进行调试")

        print("\n📋 环境检查清单:")
        print(f"   ✅ Python版本: {sys.version}")
        print(f"   {'✅' if os.getenv('OPENAI_API_KEY') else '❌'} API密钥: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
        print(f"   {'✅' if os.getenv('OPENAI_API_BASE') else '⚠️ '} API基础URL: {os.getenv('OPENAI_API_BASE') or '使用默认'}")

        # 只在调试模式下显示完整traceback
        if os.getenv("DEBUG"):
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    print("="*60)
    print("🚀 基于LLM的文本知识图谱构建系统")
    print("="*60)

    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 错误: 未设置 OPENAI_API_KEY 环境变量")
        print("请设置环境变量后重新运行:")
        print("export OPENAI_API_KEY='your_api_key_here'")
        sys.exit(1)

    main()
