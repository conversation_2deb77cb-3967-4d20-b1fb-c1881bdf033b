@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\D:\Import_Code\bibm\node_modules\.store\@babel+parser@7.28.0\node_modules\@babel"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\D:\Import_Code\bibm\node_modules\.store\@babel+parser@7.28.0\node_modules\@babel"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\@babel+parser@7.28.0\node_modules\@babel\parser\bin\babel-parser.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\@babel+parser@7.28.0\node_modules\@babel\parser\bin\babel-parser.js" %*
)
