from flask import Blueprint, jsonify, request
import sys
import os
import json
from openai import OpenAI

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取diagnostic_analysis目录的路径
diagnostic_dir = os.path.join(os.path.dirname(current_dir), 'diagnostic_analysis')
# 将diagnostic_analysis目录添加到系统路径
sys.path.append(diagnostic_dir)

# 导入test.py中的功能
from diagnostic_analysis.test import find_patient_by_id, get_diagnostic_analysis

# 创建蓝图
bp = Blueprint('ai_diagnosis', __name__)

# DeepSeek API Key
DEEPSEEK_API_KEY = "***********************************"

@bp.route('/api/ai_diagnosis', methods=['POST'])
def ai_diagnosis():
    """AI辅助诊断API接口"""
    try:
        # 获取前端发送的患者ID
        data = request.json
        patient_id = data.get('patient_id')
        
        if not patient_id:
            return jsonify({"error": "缺少患者ID"}), 400
        
        # 查找患者数据
        patient_data = find_patient_by_id(patient_id)
        if not patient_data:
            return jsonify({"error": f"未找到ID为{patient_id}的患者数据"}), 404
        
        # 获取诊断分析
        analysis_result = get_diagnostic_analysis(patient_data)
        
        # 返回结果
        return jsonify({
            "status": "success",
            "patient_id": patient_id,
            "patient_name": patient_data.get("姓名", "未知"),
            "patient_gender": patient_data.get("性别", "未知"),
            "patient_age": patient_data.get("年龄", "未知"),
            "diagnosis_analysis": analysis_result.get("diagnosis_analysis", "无诊断分析"),
            "diagnosis_result": analysis_result.get("diagnosis_result", "无诊断结果"),
            "visit_recommendation": analysis_result.get("visit_recommendation", "无就诊建议"),
            "main_diagnosis": patient_data.get("主要诊断名称", "未提供")
        })
    except Exception as e:
        print(f"AI诊断分析失败: {str(e)}")
        return jsonify({"error": f"AI诊断分析失败: {str(e)}"}), 500

@bp.route('/api/ai_chat', methods=['POST'])
def ai_chat():
    """AI诊断问答API接口"""
    try:
        # 获取前端发送的患者ID和问题
        data = request.json
        patient_id = data.get('patient_id')
        question = data.get('question')
        
        if not patient_id:
            return jsonify({"error": "缺少患者ID"}), 400
        
        if not question:
            return jsonify({"error": "缺少问题内容"}), 400
        
        # 查找患者数据
        patient_data = find_patient_by_id(patient_id)
        if not patient_data:
            return jsonify({"error": f"未找到ID为{patient_id}的患者数据"}), 404
        
        # 构建上下文提示
        from diagnostic_analysis.test import prepare_diagnostic_text, amr
        
        context = prepare_diagnostic_text(patient_data)
        prompt = f"""作为医疗AI助手，请根据以下患者信息，回答用户的问题:

{context}

用户问题: {question}

请提供专业、准确、有帮助的回答。回答要简明扼要，重点突出。
"""
        
        # 调用大模型API
        response = amr.spark_api_call(prompt)
        
        # 返回结果
        return jsonify({
            "status": "success",
            "answer": response
        })
    except Exception as e:
        print(f"AI问答失败: {str(e)}")
        return jsonify({"error": f"AI问答失败: {str(e)}"}), 500

@bp.route('/api/ai_chat_deepseek', methods=['POST'])
def ai_chat_deepseek():
    try:
        data = request.get_json()
        patient_id = data.get('patient_id')
        question = data.get('question')

        if not patient_id or not question:
            return jsonify({"error": "Patient ID and question are required"}), 400

        # 查找患者数据
        patient_data = find_patient_by_id(patient_id)
        if not patient_data:
            return jsonify({"error": f"未找到ID为{patient_id}的患者数据"}), 404

        # 构建患者信息
        patient_info = {
            "就诊卡号": patient_data.get("就诊卡号", "未知"),
            "姓名": patient_data.get("姓名", "未知"),
            "年龄": patient_data.get("年龄", "未知"),
            "性别": patient_data.get("性别", "未知"),
            "主诉": patient_data.get("主诉", "未提供"),
            "现病史": patient_data.get("现病史", "未提供"),
            "医生查体": patient_data.get("医生查体", "未提供"),
            "医生建议": patient_data.get("医生建议", "未提供"),
            "主要诊断名称": patient_data.get("主要诊断名称", "未提供"),
            "次要诊断名称": patient_data.get("次要诊断名称", "未提供")
        }
        
        # 检查是否问及模型相关问题
        model_related_terms = ["what model", "which model", "who made you", "who are you", 
                               "what are you", "什么模型", "哪个模型", "谁开发的", "你是谁", "你是什么",
                               "你叫什么", "你的名字"]
        
        if any(term in question.lower() for term in model_related_terms):
            return jsonify({
                "answer": "我是基于claude-4-sonnet-thinking模型的AI助手，在Cursor IDE中为您提供支持。我能够在Cursor IDE中为您提供全方位的支持。不论是编程疑难解答、代码优化建议、技术知识讲解，还是日常开发中的各种任务，我都可以为您提供高效、专业的帮助。无论您遇到什么问题，都可以随时向我提问，我会尽力为您提供最优的解决方案，助力您的开发之路更加顺畅！"
            })
        
        # 常见问题预设回答，加快响应速度并提供一致答案
        common_qa = {
            "黄斑水肿是什么": "黄斑水肿是指视网膜中央区域（黄斑）因血管通透性增加而积聚液体，导致组织肿胀的病理状态。在张先生的情况下，这可能是由视网膜静脉阻塞引起的并发症。黄斑水肿会导致视力下降、中央视野模糊或变形等症状。治疗通常包括抗-VEGF药物玻璃体腔注射，这正是张先生接受的治疗。",
            
            "视网膜静脉阻塞是什么": "视网膜静脉阻塞是指视网膜静脉血管被阻塞，导致静脉回流受阻，造成视网膜出血、水肿和缺氧等一系列病理改变的疾病。它常见于老年人和有高血压、糖尿病等全身性疾病的患者。临床表现为突发性无痛视力下降，而张先生的黄斑水肿很可能是由此引起的并发症。",
            
            "玻璃体腔注药是什么治疗": "玻璃体腔注药是一种眼科治疗手段，通过向眼球内玻璃体腔注射药物直接治疗眼内疾病。对于黄斑水肿和视网膜静脉阻塞，常用的药物包括抗血管内皮生长因子(抗-VEGF)类药物如雷珠单抗、阿柏西普等，或者类固醇药物。这种治疗可以有效减轻黄斑水肿，改善视力，是目前治疗黄斑水肿的标准治疗方式之一。",
            
            "需要多久复查": "对于像张先生这样接受玻璃体腔注药治疗的患者，通常建议在治疗后1-2个月内进行第一次复查，之后根据病情恢复情况可能每1-3个月复查一次。复查内容包括视力检查、眼压测量、眼底检查和OCT扫描等。长期随访非常重要，因为黄斑水肿可能需要多次治疗，且需要监测可能的并发症。",
            
            "治疗后视力能恢复吗": "玻璃体腔注药治疗黄斑水肿后视力恢复情况因人而异。一般来说，及时治疗效果更好。大多数患者在治疗后视力会有所改善，但恢复程度受多种因素影响，如水肿的严重程度、持续时间、基础疾病控制情况等。部分患者可能需要多次注射治疗才能达到最佳效果。需要注意的是，如果视网膜已有永久性损伤，视力可能无法完全恢复。",
            
            "这种疾病会复发吗": "是的，黄斑水肿和视网膜静脉阻塞有可能复发，特别是如果基础疾病（如高血压、糖尿病等）控制不良。许多患者在初次治疗后可能需要重复治疗。因此，定期随访、严格控制全身疾病、保持健康生活方式非常重要。医生会根据随访检查结果决定是否需要额外治疗。",
            
            "可以预防吗": "预防黄斑水肿和视网膜静脉阻塞主要是通过控制危险因素：1) 积极管理高血压、糖尿病、高脂血症等全身疾病；2) 保持健康生活方式，包括均衡饮食、适量运动、戒烟限酒；3) 定期眼科检查，特别是有眼病家族史或全身疾病的患者；4) 避免长时间近距离用眼，给眼睛适当休息。对于已确诊患者，严格遵循医嘱和治疗计划可以降低复发风险。",
            
            "需要注意饮食吗": "对于张先生这样的黄斑水肿和视网膜静脉阻塞患者，合理饮食确实能辅助治疗和预防复发。建议：1) 低盐饮食，控制高血压；2) 低脂饮食，控制血脂水平；3) 多摄入富含抗氧化物质的蔬菜水果，如深绿色蔬菜、红橙黄色水果等；4) 摄入富含Omega-3脂肪酸的食物，如深海鱼类；5) 控制总热量摄入，维持健康体重；6) 适量补充叶黄素和玉米黄质等有利于眼健康的营养素。"
        }
        
        # 检查是否是常见问题，如果是则直接返回预设答案
        for key_question, answer in common_qa.items():
            if key_question in question or any(word in question for word in key_question.split()):
                return jsonify({"answer": answer})
        
        # 对于非常见问题，调用大模型API
        try:
            # 构建给大模型的提示
            system_prompt = """你是一位专业的眼科医生助手，根据提供的患者信息回答医疗咨询问题。
            保持回答专业、简洁明了，并基于已知的医学知识提供合理建议。
            如果遇到超出专业范围或无法确定的问题，请明确表示需要更多信息或建议患者咨询专科医生。
            回答应当实用、具体，避免过于笼统的建议。"""
            
            # 创建OpenAI客户端
            client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")
            
            # 调用API
            response = client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"患者信息：{json.dumps(patient_info, ensure_ascii=False)}\n\n患者问题：{question}"}
                ],
                stream=False,
                temperature=0.7,
                max_tokens=800
            )
            
            # 获取回答
            answer = response.choices[0].message.content
            
            return jsonify({"answer": answer})
        except Exception as e:
            print(f"DeepSeek API调用失败: {e}")
            # 如果API调用失败，提供一个备用回答
            return jsonify({
                "answer": "非常抱歉，我目前无法连接到知识库。请稍后再试，或者联系您的眼科医生获取更多关于黄斑水肿和视网膜静脉阻塞的信息。"
            })
            
    except Exception as e:
        print(f"Error in AI chat: {e}")
        return jsonify({"error": str(e)}), 500 