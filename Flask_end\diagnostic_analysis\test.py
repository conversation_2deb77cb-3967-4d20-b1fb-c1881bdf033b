from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import re
import os
from typing import Dict, Any
import Auto_Medical_Record as amr

# 获取当前文件所在目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取Flask_end根目录
flask_end_dir = os.path.dirname(current_dir)
# 构建 All.json 文件的完整路径
all_json_path = os.path.join(flask_end_dir, 'data', 'All.json')
all_json_path_two = os.path.join(flask_end_dir, 'data', 'All_data2.json')
# 用于缓存全部数据，减少重复读取
_all_patients_data = None
_all_data2_patients = None

def load_all_patients_data():
    """加载所有患者数据"""
    global _all_patients_data
    if _all_patients_data is None:
        try:
            with open(all_json_path, 'r', encoding='utf-8') as f:
                _all_patients_data = json.load(f)
                print(f"成功加载患者数据: {len(_all_patients_data)} 条记录")
        except Exception as e:
            print(f"加载患者数据失败: {str(e)}")
            _all_patients_data = []
    return _all_patients_data

def load_all_data2_patients():
    """加载All_data2.json中的患者数据"""
    global _all_data2_patients
    if _all_data2_patients is None:
        try:
            with open(all_json_path_two, 'r', encoding='utf-8') as f:
                _all_data2_patients = json.load(f)
                print(f"成功加载All_data2患者数据: {len(_all_data2_patients)} 条记录")
        except Exception as e:
            print(f"加载All_data2患者数据失败: {str(e)}")
            _all_data2_patients = []
    return _all_data2_patients

def find_patient_in_data2_by_id(patient_id: str, patient_name: str = None) -> Dict[str, Any]:
    """
    在All_data2.json中查找患者数据

    参数:
        patient_id: 患者ID（就诊卡号）
        patient_name: 患者姓名（用于辅助匹配）

    返回:
        匹配的患者数据字典，如果未找到则返回None
    """
    all_data2 = load_all_data2_patients()

    # 首先尝试通过medical_record_number精确匹配
    for patient in all_data2:
        if patient.get("medical_record_number") == patient_id:
            print(f"通过medical_record_number找到患者: {patient.get('name', '未知')}")
            return patient

    # 如果提供了患者姓名，尝试通过姓名匹配
    if patient_name:
        for patient in all_data2:
            if patient.get("name") == patient_name:
                print(f"通过姓名找到患者: {patient.get('name', '未知')} (ID: {patient.get('medical_record_number', '未知')})")
                return patient

    print(f"未在All_data2.json中找到患者 ID: {patient_id}, 姓名: {patient_name}")
    return None

def find_patient_by_id(patient_id: str) -> Dict[str, Any]:
    """根据就诊卡号查找患者数据"""
    all_data = load_all_patients_data()
    for patient in all_data:
        if patient.get("就诊卡号") == patient_id:
            return patient
    return None

def prepare_diagnostic_text(patient_data: Dict[str, Any]) -> str:
    """将患者数据格式化为发送给大模型的文本"""
    # 基本信息
    text = f"患者信息:\n"
    text += f"就诊卡号: {patient_data.get('就诊卡号', '未知')}\n"
    text += f"姓名: {patient_data.get('姓名', '未知')}\n"
    text += f"性别: {patient_data.get('性别', '未知')}\n"
    text += f"年龄: {patient_data.get('年龄', '未知')}\n"
    text += f"诊断日期: {patient_data.get('诊断日期', '未知')}\n"

    # 尝试从All_data2.json中获取额外的患者信息
    patient_id = patient_data.get('就诊卡号', '')
    patient_name = patient_data.get('姓名', '')
    additional_data = find_patient_in_data2_by_id(patient_id, patient_name)

    if additional_data:
        text += f"\n=== 补充患者信息 (来自All_data2.json) ===\n"
        text += f"病案号: {additional_data.get('medical_record_number', '未知')}\n"
        text += f"患者姓名: {additional_data.get('name', '未知')}\n"
        text += f"年龄: {additional_data.get('age', '未知')}\n"
        text += f"住院流水号: {additional_data.get('inpatient_serial_number', '未知')}\n"
        text += f"入院日期: {additional_data.get('admission_date', '未知')}\n"
        text += f"出院时间: {additional_data.get('discharge_time', '未知')}\n"
        text += f"主要出院诊断名称: {additional_data.get('primary_discharge_diagnosis_name', '未知')}\n"
        text += f"主要出院诊断代码: {additional_data.get('primary_discharge_diagnosis_code', '未知')}\n"

        # 添加详细诊断信息
        if additional_data.get('diagnosis') and len(additional_data.get('diagnosis', [])) > 0:
            text += f"\n=== 详细出院诊断列表 ===\n"
            for idx, diag in enumerate(additional_data.get('diagnosis', []), 1):
                text += f"诊断{idx}: {diag.get('discharge_diagnosis_name', '未知')} "
                text += f"(代码: {diag.get('discharge_diagnosis_code', '未知')}, "
                text += f"序号: {diag.get('diagnosis_sequence_number', '未知')})\n"

        # 添加眼科用药信息
        if additional_data.get('ophthalmic_medication_info') and len(additional_data.get('ophthalmic_medication_info', [])) > 0:
            text += f"\n=== 眼科用药信息 ===\n"
            for idx, med in enumerate(additional_data.get('ophthalmic_medication_info', []), 1):
                text += f"药物{idx}: {med.get('medication_name', '未知')}\n"
                text += f"  - 药物代码: {med.get('medication_code', '未知')}\n"
                text += f"  - 用法用量: {med.get('medication_usage', '未知')}\n"
                text += f"  - 数量: {med.get('medication_quantity', '未知')} {med.get('medication_unit', '')}\n"
                text += f"  - 给药途径: {med.get('administration_route', '未知')}\n"
                text += f"  - 剂量单位: {med.get('dosage_unit', '未知')}\n"
                text += f"  - 单价: {med.get('medication_unit_price', '未知')}元\n"
                text += f"  - 开药日期: {med.get('pricing_date', '未知')}\n"
                text += f"  - 发药日期: {med.get('dispensing_date', '未知')}\n"
                text += "\n"
    else:
        text += f"\n注意: 未在All_data2.json中找到ID为 {patient_id} 或姓名为 {patient_name} 的患者补充信息\n"

    # 主要临床信息
    text += f"\n临床信息:\n"
    text += f"主诉: {patient_data.get('主诉', '未提供')}\n"
    text += f"现病史: {patient_data.get('现病史', '未提供')}\n"
    text += f"医生查体: {patient_data.get('医生查体', '未提供')}\n"
    text += f"医生建议: {patient_data.get('医生建议', '未提供')}\n"

    # 诊断信息
    text += f"\n诊断信息:\n"
    text += f"主要诊断名称: {patient_data.get('主要诊断名称', '未提供')}\n"
    text += f"主要诊断代码: {patient_data.get('主要诊断', '未提供')}\n"

    if patient_data.get('次要诊断名称'):
        text += f"次要诊断名称: {patient_data.get('次要诊断名称', '未提供')}\n"
        text += f"次要诊断代码: {patient_data.get('次要诊断代码', '未提供')}\n"
    
    # 检查信息
    if patient_data.get('检查项目') and len(patient_data.get('检查项目', [])) > 0:
        text += f"\n检查信息:\n"
        for idx, check in enumerate(patient_data.get('检查项目', []), 1):
            # 安全地获取字段值，处理可能的字段名变化
            item_name = check.get('itemCName', check.get('ItemCName', '未知'))
            item_value = check.get('ItemValue', check.get('itemValue', ''))
            item_unit = check.get('Itemunit', check.get('ItemUnit', check.get('itemUnit', '')))

            text += f"检查项目{idx}: {item_name} - {item_value} {item_unit}"

            item_range = check.get('ItemRange', check.get('itemRange', ''))
            if item_range:
                text += f" (参考范围: {item_range})"

            result_status = check.get('ResultStatus', check.get('resultStatus', ''))
            if result_status == 'H':
                text += " (偏高)"
            elif result_status == 'L':
                text += " (偏低)"
            text += "\n"

    print(text)
    return text


def parse_text_response(response: str) -> Dict[str, Any]:
    """解析文本格式的AI响应"""
    # 初始化结果
    result = {
        'diagnosis_analysis': '',
        'diagnosis_result': '',
        'visit_recommendation': ''
    }

    # 分割响应文本
    sections = response.split('\n\n')
    current_section = None

    for section in sections:
        section = section.strip()
        if not section:
            continue

        # 检查是否是新的部分标题
        if '诊断分析' in section or '主要症状' in section:
            current_section = 'diagnosis_analysis'
            result[current_section] += section + '\n\n'
        elif '诊断结果' in section or '确诊疾病' in section:
            current_section = 'diagnosis_result'
            result[current_section] += section + '\n\n'
        elif '就诊建议' in section or '治疗方案' in section:
            current_section = 'visit_recommendation'
            result[current_section] += section + '\n\n'
        elif current_section:
            # 继续添加到当前部分
            result[current_section] += section + '\n\n'
        else:
            # 如果没有明确的部分，添加到诊断分析
            result['diagnosis_analysis'] += section + '\n\n'

    # 如果某个部分为空，提供默认内容
    if not result['diagnosis_analysis'].strip():
        result['diagnosis_analysis'] = response[:500] + '...' if len(response) > 500 else response
    if not result['diagnosis_result'].strip():
        result['diagnosis_result'] = '请参考诊断分析内容'
    if not result['visit_recommendation'].strip():
        result['visit_recommendation'] = '请咨询专业医生获取详细建议'

    return result


def generate_fallback_analysis(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """当AI API不可用时，生成基于患者数据的基础分析"""
    patient_name = patient_data.get('姓名', '患者')
    age = patient_data.get('年龄', '未知')
    gender = patient_data.get('性别', '未知')
    main_diagnosis = patient_data.get('主要诊断名称', '未明确诊断')

    # 分析检查项目
    check_items = patient_data.get('检查项目', [])
    abnormal_items = []
    normal_items = []

    for item in check_items:
        try:
            item_name = item.get('itemCName', item.get('ItemCName', '未知项目'))
            item_value = item.get('ItemValue', item.get('itemValue', ''))
            item_range = item.get('ItemRange', item.get('itemRange', ''))
            result_status = item.get('ResultStatus', item.get('resultStatus', ''))

            if result_status in ['H', 'L']:
                status_text = '偏高' if result_status == 'H' else '偏低'
                abnormal_items.append(f"{item_name}: {item_value} ({status_text})")
            else:
                normal_items.append(f"{item_name}: {item_value}")
        except:
            continue

    # 构建诊断分析
    diagnosis_analysis = f"""
**主要症状分析**：
患者{patient_name}，{age}岁，{gender}性，主要诊断为{main_diagnosis}。

**检查结果解读**：
本次检查共进行了{len(check_items)}项检查项目。
"""

    if abnormal_items:
        diagnosis_analysis += f"\n异常检查项目：\n"
        for item in abnormal_items[:5]:  # 只显示前5个异常项目
            diagnosis_analysis += f"• {item}\n"

    if normal_items:
        diagnosis_analysis += f"\n正常检查项目：{len(normal_items)}项"

    diagnosis_analysis += f"""

**病理生理机制**：
根据检查结果，{main_diagnosis}的病理变化主要涉及相关器官功能异常。

**与主诊断的相关性分析**：
患者的临床表现和检查结果与{main_diagnosis}的典型特征相符。
"""

    # 构建诊断结果
    diagnosis_result = f"""
**确诊疾病名称**：
{main_diagnosis}

**诊断依据**：
1. 患者临床症状表现
2. 相关检查项目结果
3. 病史和体征综合分析

**严重程度评估**：
根据当前检查结果进行综合评估。

**需要关注的检查项目**：
"""

    if abnormal_items:
        for item in abnormal_items[:3]:
            diagnosis_result += f"• {item}\n"
    else:
        diagnosis_result += "各项检查指标基本正常"

    # 构建就诊建议
    visit_recommendation = f"""
**治疗方案建议**：
1. 针对{main_diagnosis}进行规范化治疗
2. 定期监测相关检查指标
3. 根据病情变化调整治疗方案

**复查时间安排**：
建议1-3个月后复查，具体时间根据病情严重程度确定。

**生活注意事项**：
1. 保持良好的生活习惯
2. 按时服药，遵医嘱治疗
3. 注意观察症状变化

**预后评估**：
经过规范治疗，大部分患者预后良好。建议定期随访。
"""

    return {
        'diagnosis_analysis': diagnosis_analysis,
        'diagnosis_result': diagnosis_result,
        'visit_recommendation': visit_recommendation
    }


def get_diagnostic_analysis(patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """获取智能诊断分析结果"""
    # 准备发送给大模型的文本
    diagnostic_text = prepare_diagnostic_text(patient_data)

    # 构造提示词
    prompt = f"""作为专业的眼科医生，请基于以下患者信息进行详细的诊断分析。

患者信息：
{diagnostic_text}

请按照以下格式提供详细分析，每个部分都要包含具体的医学术语和专业解释：

1. **诊断分析**：
   - 主要症状分析
   - 检查结果解读
   - 病理生理机制
   - 与主诊断的相关性分析

2. **诊断结果**：
   - 确诊疾病名称
   - 诊断依据
   - 严重程度评估
   - 可能的并发症

3. **就诊建议**：
   - 治疗方案建议
   - 复查时间安排
   - 生活注意事项
   - 预后评估

请以JSON格式返回，包含三个字段：diagnosis_analysis、diagnosis_result、visit_recommendation。
每个字段的内容应该是结构化的文本，包含标题和详细说明。
"""

    # 调用科大讯飞大模型API
    response = amr.spark_api_call(prompt)

    # 处理响应结果
    if not response:
        # API调用失败时，返回基于患者数据的基础分析
        return generate_fallback_analysis(patient_data)

    try:
        # 尝试解析JSON响应
        import json
        result = json.loads(response)

        # 确保所有必需的字段都存在
        return {
            'diagnosis_analysis': result.get('diagnosis_analysis', '诊断分析信息不完整'),
            'diagnosis_result': result.get('diagnosis_result', '诊断结果信息不完整'),
            'visit_recommendation': result.get('visit_recommendation', '就诊建议信息不完整')
        }
    except json.JSONDecodeError:
        # 如果不是JSON格式，尝试解析文本格式
        return parse_text_response(response)
    except Exception as e:
        print(f"处理AI响应时出错: {e}")
        return {
            'diagnosis_analysis': f'AI分析结果：\n{response[:500]}...',
            'diagnosis_result': '请参考诊断分析内容',
            'visit_recommendation': '请咨询专业医生获取详细建议'
        }

    # 原来的AI调用代码（暂时注释掉）
    """
    # 准备发送给大模型的文本
    diagnostic_text = prepare_diagnostic_text(patient_data)

    # 构造提示词
    prompt = f'''作为专业的眼科医生，请基于以下患者信息进行详细的诊断分析。

患者信息：
{diagnostic_text}

请按照以下格式提供详细分析，每个部分都要包含具体的医学术语和专业解释：

1. **诊断分析**：
   - 主要症状分析
   - 检查结果解读
   - 病理生理机制
   - 与主诊断的相关性分析

2. **诊断结果**：
   - 确诊疾病名称
   - 诊断依据
   - 严重程度评估
   - 可能的并发症

3. **就诊建议**：
   - 治疗方案建议
   - 复查时间安排
   - 生活注意事项
   - 预后评估

请以JSON格式返回，包含三个字段：diagnosis_analysis、diagnosis_result、visit_recommendation。
每个字段的内容应该是结构化的文本，包含标题和详细说明。
'''

    # 调用科大讯飞大模型API
    response = amr.spark_api_call(prompt)

    # 处理响应结果
    if not response:
        return {
            'diagnosis_analysis': '无法获取诊断分析',
            'diagnosis_result': '无法获取诊断结果',
            'visit_recommendation': '无法获取就诊建议'
        }
    """

    try:
        # 尝试直接解析JSON
        parsed_result = json.loads(response)
        # 确保返回的数据包含所需字段
        return {
            'diagnosis_analysis': format_diagnostic_content(parsed_result.get('diagnosis_analysis', '暂无诊断分析')),
            'diagnosis_result': format_diagnostic_content(parsed_result.get('diagnosis_result', '暂无诊断结果')),
            'visit_recommendation': format_diagnostic_content(parsed_result.get('visit_recommendation', '暂无就诊建议'))
        }
    except json.JSONDecodeError:
        # 如果不是JSON格式，尝试提取JSON部分
        json_match = re.search(r'({[\s\S]*})', response)
        if json_match:
            try:
                parsed_result = json.loads(json_match.group(1))
                return {
                    'diagnosis_analysis': format_diagnostic_content(parsed_result.get('diagnosis_analysis', '暂无诊断分析')),
                    'diagnosis_result': format_diagnostic_content(parsed_result.get('diagnosis_result', '暂无诊断结果')),
                    'visit_recommendation': format_diagnostic_content(parsed_result.get('visit_recommendation', '暂无就诊建议'))
                }
            except:
                pass

        # 如果无法解析为JSON，则手动构造返回结果
        return parse_text_response(response)

def format_diagnostic_content(content: str) -> str:
    """格式化诊断内容，确保结构化显示"""
    if not content or content.strip() == '':
        return '暂无相关信息'

    # 移除多余的空白字符
    content = re.sub(r'\s+', ' ', content.strip())

    # 确保内容不是纯JSON格式
    if content.startswith('{') and content.endswith('}'):
        try:
            parsed = json.loads(content)
            if isinstance(parsed, dict):
                # 如果是字典，转换为可读文本
                formatted_parts = []
                for key, value in parsed.items():
                    if value:
                        formatted_parts.append(f"**{key}**: {value}")
                return '\n\n'.join(formatted_parts)
        except:
            pass

    return content

def parse_text_response(response: str) -> Dict[str, str]:
    """解析文本格式的响应"""
    sections = ['诊断分析', '诊断结果', '就诊建议']
    result = {}

    for section in sections:
        # 尝试多种模式匹配
        patterns = [
            rf'{section}[:：]\s*(.*?)(?=(?:诊断分析|诊断结果|就诊建议)[:：]|$)',
            rf'\*\*{section}\*\*[:：]?\s*(.*?)(?=\*\*(?:诊断分析|诊断结果|就诊建议)\*\*|$)',
            rf'{section}\s*(.*?)(?=(?:诊断分析|诊断结果|就诊建议)|$)'
        ]

        field_name = {
            '诊断分析': 'diagnosis_analysis',
            '诊断结果': 'diagnosis_result',
            '就诊建议': 'visit_recommendation'
        }[section]

        content_found = False
        for pattern in patterns:
            match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if match and match.group(1).strip():
                result[field_name] = format_diagnostic_content(match.group(1).strip())
                content_found = True
                break

        if not content_found:
            result[field_name] = f"暂无{section}信息"

    return result

# 添加进app.py中
def register_routes(app):
    """注册路由到Flask应用"""
    
    @app.route('/api/smart_diagnosis', methods=['GET'])
    def smart_diagnosis():
        """智能诊断分析接口"""
        try:
            # 获取请求参数
            patient_id = request.args.get('medical_record_number')
            if not patient_id:
                return jsonify({"error": "缺少就诊卡号参数"}), 400
            
            # 查找患者数据
            patient_data = find_patient_by_id(patient_id)
            if not patient_data:
                return jsonify({"error": f"未找到就诊卡号为 {patient_id} 的患者数据"}), 404
            
            # 获取诊断分析
            analysis_result = get_diagnostic_analysis(patient_data)
            
            # 返回结果
            return jsonify({
                "patient_id": patient_id,
                "analysis_result": analysis_result
            })
            
        except Exception as e:
            print(f"智能诊断分析失败: {str(e)}")
            return jsonify({"error": f"智能诊断分析失败: {str(e)}"}), 500

def test_data_matching():
    """测试数据匹配功能"""
    print("=== 测试数据匹配功能 ===")

    # 加载数据
    data1 = load_all_patients_data()
    data2 = load_all_data2_patients()
    print(f"All.json: {len(data1)} 条记录")
    print(f"All_data2.json: {len(data2)} 条记录")

    # 测试几个患者的匹配
    test_patients = ["600912320", "A01633122"]  # 测试不同的ID格式

    for patient_id in test_patients:
        print(f"\n--- 测试患者ID: {patient_id} ---")

        # 在All.json中查找
        patient1 = find_patient_by_id(patient_id)
        if patient1:
            print(f"在All.json中找到: {patient1.get('姓名', '未知')}")

            # 在All_data2.json中查找
            patient2 = find_patient_in_data2_by_id(patient_id, patient1.get('姓名', ''))
            if patient2:
                print(f"在All_data2.json中找到: {patient2.get('name', '未知')}")
                print(f"病案号: {patient2.get('medical_record_number', '未知')}")
                print(f"诊断数量: {len(patient2.get('diagnosis', []))}")
                print(f"用药数量: {len(patient2.get('ophthalmic_medication_info', []))}")
            else:
                print("在All_data2.json中未找到匹配数据")
        else:
            print("在All.json中未找到")

    # 测试文本生成
    print(f"\n--- 测试文本生成 ---")
    patient = find_patient_by_id("600912320")
    if patient:
        text = prepare_diagnostic_text(patient)
        print("生成的文本长度:", len(text))
        print("文本预览 (前500字符):")
        print(text[:500] + "..." if len(text) > 500 else text)

if __name__ == "__main__":
    # 运行测试
    test_data_matching()

    print("\n" + "="*60)
    print("原有测试代码:")

    # 测试数据加载
    data = load_all_patients_data()
    print(f"共加载 {len(data)} 条患者数据")

    # 测试查找患者
    patient = find_patient_by_id("600912320")
    if patient:
        print(f"找到患者: {patient.get('姓名')}")

        # 测试诊断分析
        analysis = get_diagnostic_analysis(patient)
        print("诊断分析结果:")
        print(json.dumps(analysis, ensure_ascii=False, indent=2))
