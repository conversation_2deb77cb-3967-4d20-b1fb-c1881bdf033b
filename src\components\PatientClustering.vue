<template>
  <div class="patient-clustering">
    <div class="clustering-header">
      <h3>Patient Data Clustering</h3>
      <div class="processing-status" v-if="isProcessing">
        <div class="spinner"></div>
        <span>{{ processingMessage }}</span>
      </div>
    </div>

    <!-- Backend connection status -->
    <div class="connection-status" v-if="!backendConnected">
      <div class="status-icon">🔌</div>
      <div class="status-text">
        <div class="status-title">Backend Connection Required</div>
        <div class="status-message">
          Please start the backend server to view clustering visualization.
          <br>
          <small>Run: <code>python app.py</code> in the Flask_end directory</small>
        </div>
        <button class="retry-btn" @click="checkBackendConnection" :disabled="isCheckingConnection">
          {{ isCheckingConnection ? 'Checking...' : 'Retry Connection' }}
        </button>
      </div>
    </div>

    <!-- Error message -->
    <div class="error-message" v-if="errorMessage && backendConnected">
      <div class="error-icon">⚠️</div>
      <div class="error-text">{{ errorMessage }}</div>
    </div>

    <!-- Processing steps -->
    <div class="processing-steps" v-if="isProcessing && processingSteps.length > 0">
      <div class="step-item" v-for="(step, index) in processingSteps" :key="index">
        <div class="step-icon" :class="{ 'completed': step.completed, 'current': step.current }">
          <span v-if="step.completed">✓</span>
          <span v-else-if="step.current">→</span>
          <span v-else>○</span>
        </div>
        <div class="step-text">{{ step.text }}</div>
      </div>
    </div>

    <!-- Clustering Controls -->
    <div class="clustering-controls" v-if="backendConnected && !errorMessage">
      <div class="clustering-title">Cluster By:</div>
      <div class="clustering-options">
        <div class="clustering-option" :class="{ active: clusteringMethod === 'disease' }" @click="setClusteringMethod('disease')">Disease</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'age' }" @click="setClusteringMethod('age')">Age</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'visits' }" @click="setClusteringMethod('visits')">Visit Count</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'algorithm' }" @click="setClusteringMethod('algorithm')">HDBSCAN</div>
      </div>
    </div>

    <!-- Visualization Container with Legend -->
    <div class="visualization-container" v-if="backendConnected && !errorMessage">
      <div ref="chartContainer" class="chart-container"></div>
      
      <!-- Selection info -->
      <div class="selection-info" v-if="selectedPatients.length > 0">
        <div class="selection-count">{{ selectedPatients.length }} patients selected</div>
        <button class="clear-selection-btn" @click="clearSelection">Clear</button>
      </div>
      
      <!-- Non-obtrusive cluster legend -->
      <div class="cluster-legend">
        <div v-for="(cluster, index) in clusterLegendItems" 
             :key="index" 
             class="cluster-legend-item"
             :class="{ 'disabled': !visibleClusters.includes(cluster.id) }"
             @click="toggleClusterVisibility(cluster.id)">
          <div class="color-dot" :style="{ backgroundColor: cluster.color }"></div>
          <div class="cluster-name">{{ cluster.name }}</div>
          <div class="cluster-count">({{ cluster.count }})</div>
        </div>
      </div>
    </div>

    <!-- Selected Patient Details -->
    <div class="selected-patient" v-if="selectedPatient">
      <h4>Selected Patient</h4>
      <div class="patient-card">
        <div class="patient-header">
          <span class="patient-id">Medical Card: {{ selectedPatient.patientId }}</span>
          <span class="patient-cluster" :style="{ backgroundColor: selectedPatient.color }">
            {{ getClusterLabel(selectedPatient.cluster) }}
          </span>
        </div>
        <div class="patient-details">
          <div class="patient-detail-row">
            <span class="detail-label">Age:</span>
            <span class="detail-value">{{ selectedPatient.age }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Gender:</span>
            <span class="detail-value">{{ selectedPatient.gender }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Diagnosis:</span>
            <span class="detail-value">{{ selectedPatient.diagnosis }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Visit Count:</span>
            <span class="detail-value">{{ selectedPatient.visitCount }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">HbA1c:</span>
            <span class="detail-value">{{ formatNumber(selectedPatient.HbA1c) }}%</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">IOP (R/L):</span>
            <span class="detail-value">{{ formatNumber(selectedPatient.IOP_R) }} / {{ formatNumber(selectedPatient.IOP_L) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Multiple Selected Patients -->
    <div class="selected-patients-list" v-if="selectedPatients.length > 1">
      <h4>Selected Patients ({{ selectedPatients.length }})</h4>
      <div class="patients-table">
        <table>
          <thead>
            <tr>
              <th>Medical Card</th>
              <th>Age</th>
              <th>Gender</th>
              <th>Diagnosis</th>
              <th>Visit Count</th>
              <th>HbA1c</th>
              <th>Cluster</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(patient, index) in selectedPatients" :key="index" @click="selectSinglePatient(patient)">
              <td>{{ patient.patientId }}</td>
              <td>{{ patient.age }}</td>
              <td>{{ patient.gender }}</td>
              <td>{{ patient.diagnosis }}</td>
              <td>{{ patient.visitCount }}</td>
              <td>{{ formatNumber(patient.HbA1c) }}%</td>
              <td>
                <span class="table-cluster-indicator" :style="{ backgroundColor: patient.color }">
                  {{ getClusterLabel(patient.cluster) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import patientDataProcessor from '../utils/patientDataProcessor';

export default {
  name: 'PatientClustering',
  data() {
    return {
      isProcessing: false,
      processingComplete: false,
      chart: null,
      clusteringMethod: 'disease',
      visualizationData: [],
      clusterStats: [],
      selectedPatient: null,
      selectedPatients: [],
      errorMessage: null,
      backendConnected: false,
      isCheckingConnection: false,
      colors: [
        '#4682B4', '#5F9EA0', '#6495ED', '#00BFFF', '#1E90FF',
        '#87CEEB', '#87CEFA', '#ADD8E6', '#B0E0E6', '#00CED1',
        '#48D1CC', '#40E0D0', '#7FFFD4', '#66CDAA', '#20B2AA'
      ],
      processingMessage: '',
      processingSteps: [],
      visibleClusters: [], // Track which clusters are visible
      clusterLegendItems: [], // Store legend items for display
      brushAreas: [], // 存储选择框区域
      maxBrushCount: 2 // 最大选择框数量
    };
  },
  async mounted() {
    console.log('PatientClustering mounted');
    // 首先检查后端连接
    await this.checkBackendConnection();

    // 只有在后端连接成功后才初始化图表和加载数据
    if (this.backendConnected) {
      this.initChart();
      this.loadData();
    }
  },

  activated() {
    console.log('PatientClustering activated');
    // 当组件被激活时，确保图表正确渲染
    this.$nextTick(() => {
      if (this.chart) {
        setTimeout(() => {
          this.chart.resize();
          console.log('PatientClustering chart resized on activation');
        }, 100);
      }
    });
  },

  beforeUnmount() {
    console.log('PatientClustering beforeUnmount');
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    async checkBackendConnection() {
      try {
        console.log('检查后端连接状态...');
        this.isCheckingConnection = true;
        this.errorMessage = null;

        const connectionStatus = await patientDataProcessor.checkBackendConnection();

        if (connectionStatus.connected) {
          this.backendConnected = true;
          console.log('✅ 后端连接成功');
        } else {
          this.backendConnected = false;
          console.error('❌ 后端连接失败:', connectionStatus.error);
        }
      } catch (error) {
        this.backendConnected = false;
        console.error('❌ 后端连接检查失败:', error);
      } finally {
        this.isCheckingConnection = false;
      }
    },

    async loadData() {
      if (!this.backendConnected) {
        console.warn('后端未连接，跳过数据加载');
        return;
      }

      this.isProcessing = true;
      this.errorMessage = null;
      this.processingMessage = '正在从后端加载聚类数据...';
      
      // 初始化处理步骤
      this.processingSteps = [
        { text: '连接后端服务', completed: true, current: false },
        { text: '获取默认聚类结果', completed: false, current: true },
        { text: '处理可视化数据', completed: false, current: false },
        { text: '渲染聚类图表', completed: false, current: false }
      ];

      try {
        console.log('开始从后端加载聚类数据...');

        // Step 1: 获取默认聚类结果
        this.processingMessage = '正在获取后端聚类结果...';
        this.updateProcessingStep(1, true);

        const result = await patientDataProcessor.runFullPipeline({ useDefault: true });

        if (result && result.visualizationData && result.clusterStats) {
          // Step 2: 处理可视化数据
          this.processingMessage = '正在处理可视化数据...';
          this.updateProcessingStep(2, true);

          this.visualizationData = result.visualizationData;
          this.clusterStats = result.clusterStats;

          // Step 3: 渲染聚类图表
          this.processingMessage = '正在渲染聚类图表...';
          this.updateProcessingStep(3, true);

          this.processingComplete = true;
          console.log(`✅ 成功加载 ${this.visualizationData.length} 条患者记录，聚类数量: ${this.clusterStats.length}`);
          this.processingMessage = '聚类数据加载完成！';

            // 更新可视化
            this.updateVisualization();
        } else {
          throw new Error('后端返回的聚类结果无效');
        }

      } catch (error) {
        console.error('加载聚类数据时出错:', error);
        this.errorMessage = `加载聚类数据失败: ${error.message || '未知错误'}。请确保后端服务正在运行。`;
      } finally {
        this.isProcessing = false;
      }
    },

    updateProcessingStep(stepIndex, completed) {
      if (stepIndex > 0) {
        this.processingSteps[stepIndex - 1].completed = true;
        this.processingSteps[stepIndex - 1].current = false;
      }
      if (stepIndex < this.processingSteps.length) {
        this.processingSteps[stepIndex].current = !completed;
        this.processingSteps[stepIndex].completed = completed;
      }
    },
    
    initChart() {
      const chartDom = this.$refs.chartContainer;

      // 如果图表已经存在，先销毁它
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }

      // 确保DOM元素存在
      if (!chartDom) {
        console.warn('Chart container not found');
        return;
      }

      console.log('Initializing PatientClustering chart');
      this.chart = echarts.init(chartDom);

      // Set empty chart with loading state
      this.chart.setOption({
        title: {
          text: 'Patient Clustering',
          left: 'center'
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          type: 'scatter',
          data: []
        }]
      });

      // Add click event handler
      this.chart.on('click', this.handleChartClick);
    },
    
    handleChartClick(params) {
      if (params.data) {
        this.selectedPatient = params.data;
        // Clear multiple selections when clicking on a single point
        this.selectedPatients = [params.data];
      }
    },
    
    setClusteringMethod(method) {
      this.clusteringMethod = method;
      this.updateVisualization();

      // 通知父组件聚类方法已改变，以便同步疾病列表
      this.$emit('clustering-method-changed', method);
    },
    
    async updateVisualization() {
      if (!this.processingComplete) {
        return;
      }

      this.isProcessing = true;
      this.selectedPatient = null;
      this.errorMessage = null;
      this.processingMessage = `正在更新 ${this.clusteringMethod} 聚类...`;

      try {
        let result;

        // Apply clustering based on selected method using backend APIs
        switch (this.clusteringMethod) {
          case 'age':
            console.log('调用后端年龄聚类API...');
            result = await patientDataProcessor.clusterByAgeGroups();
            break;

          case 'visits':
            console.log('调用后端就诊频次聚类API...');
            result = await patientDataProcessor.clusterByVisitFrequency();
            break;

          case 'algorithm':
            console.log('调用后端诊断聚类API...');
            result = await patientDataProcessor.clusterByDiagnosisAPI();
            break;

          case 'disease':
          default:
            // Disease clustering is default - use current data
            console.log('使用当前疾病聚类数据...');
            result = {
              visualizationData: this.visualizationData,
              clusterStats: this.clusterStats
            };
            break;
        }

        if (result && result.visualizationData && result.visualizationData.length > 0) {
          this.visualizationData = result.visualizationData;
          this.clusterStats = result.clusterStats || [];
          this.processingMessage = '可视化更新完成';
          this.renderVisualization();
        } else if (result && result.visualization_data && result.visualization_data.length > 0) {
          // Handle backend API response format
          this.visualizationData = result.visualization_data;
          this.clusterStats = result.cluster_stats || [];
          this.processingMessage = '可视化更新完成';
          this.renderVisualization();
        } else {
          console.error('No visualization data available');
          this.errorMessage = '无法生成可视化数据。请尝试其他聚类方法。';
        }
      } catch (error) {
        console.error('Error updating visualization:', error);
        this.errorMessage = `更新可视化时出错: ${error.message || '未知错误'}`;
      } finally {
        this.isProcessing = false;
      }
    },
    
    renderVisualization() {
      if (!this.visualizationData || this.visualizationData.length === 0) {
        return;
      }
      
      // Get colored data based on clustering method
      const coloredData = patientDataProcessor.getVisualizationWithColors(
        this.clusteringMethod === 'disease' ? 'cluster' : 
        this.clusteringMethod === 'algorithm' ? 'cluster' : 
        this.clusteringMethod
      );
      
      // Get unique clusters and create legend items
      const uniqueClusters = [...new Set(coloredData.map(item => item.cluster))];
      
      // Initialize visible clusters if empty
      if (this.visibleClusters.length === 0) {
        this.visibleClusters = [...uniqueClusters];
      }
      
      // Create legend items
      this.clusterLegendItems = uniqueClusters.map(clusterId => {
        const stats = this.clusterStats.find(s => s.clusterId === clusterId);
        const clusterColor = this.getClusterColor(clusterId);
        const clusterPatients = coloredData.filter(item => item.cluster === clusterId);
        
        return {
          id: clusterId,
          name: stats?.label || `Cluster ${clusterId + 1}`,
          color: clusterColor,
          count: clusterPatients.length
        };
      });
      
      // Filter data based on visible clusters
      const filteredData = coloredData.filter(item => this.visibleClusters.includes(item.cluster));
      
      // Prepare data for scatter plot
      const scatterData = filteredData.map(item => ({
        ...item,
        value: [item.x, item.y],
        symbolSize: 10,
        itemStyle: {
          color: item.color
        }
      }));
      
      // Update chart
      this.chart.setOption({
        title: {
          text: `Patient Clustering by ${this.formatClusteringMethod(this.clusteringMethod)}`,
          left: 'center',
          top: 30 // 将标题下移
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const data = params.data;
            return `
              <div style="font-weight: bold; margin-bottom: 5px;">Medical Card: ${data.patientId}</div>
              <div>Age: ${data.age}</div>
              <div>Gender: ${data.gender}</div>
              <div>Diagnosis: ${data.diagnosis}</div>
              <div>Visit Count: ${data.visitCount}</div>
              <div>HbA1c: ${this.formatNumber(data.HbA1c)}%</div>
            `;
          }
        },
        legend: {
          show: false // Hide the default legend
        },
        xAxis: {
          type: 'value',
          show: false,
          min: (value) => Math.floor(value.min * 1.2),
          max: (value) => Math.ceil(value.max * 1.2)
        },
        yAxis: {
          type: 'value',
          show: false,
          min: (value) => Math.floor(value.min * 1.2),
          max: (value) => Math.ceil(value.max * 1.2)
        },
        grid: {
          left: 10,
          right: 10,
          top: 70, // 增加顶部空间，为工具栏腾出位置
          bottom: 10,
          containLabel: false
        },
        brush: {
          toolbox: ['rect', 'polygon', 'keep', 'clear', 'circle', 'lineX', 'lineY'],
          xAxisIndex: 0,
          brushLink: 'all',
          outOfBrush: {
            colorAlpha: 0.1
          },
          brushStyle: {
            borderWidth: 1,
            color: 'rgba(120,140,180,0.15)',
            borderColor: 'rgba(70,130,180,0.8)'
          },
          throttleType: 'debounce',
          throttleDelay: 300,
          brushMode: 'multiple', // 支持多选
          removeOnClick: true, // 点击已有选择框可删除
        },
        toolbox: {
          top: 5, // 工具栏位置调整
          right: 20,
          feature: {
            brush: {
              type: ['rect', 'polygon', 'circle', 'keep', 'clear', 'lineX', 'lineY'],
              title: {
                rect: '矩形选择',
                polygon: '多边形选择',
                circle: '圆形选择',
                lineX: '横向选择',
                lineY: '纵向选择',
                keep: '保持选择',
                clear: '清除选择'
              }
            },
            myTool: {
              show: true,
              title: '删除最后选择框',
              icon: 'path://M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z',
              onclick: () => {
                this.removeLastBrushArea();
              }
            },
            dataZoom: {
              show: true,
              title: {
                zoom: '缩放',
                back: '还原'
              }
            },
            restore: {
              show: true,
              title: '重置'
            }
          }
        },
        series: [{
          name: 'Patients',
          type: 'scatter',
          data: scatterData,
          symbolSize: 12,
          emphasis: {
            focus: 'self',
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      });

      // 恢复已有的选择框
      if (this.brushAreas.length > 0) {
        this.chart.dispatchAction({
          type: 'brush',
          areas: this.brushAreas
        });
      }

      // 添加brush事件监听
      this.chart.off('brushSelected'); // 先移除旧的监听器
      this.chart.on('brushSelected', this.handleBrushSelected);
      
      // 添加brushEnd事件监听，用于限制选择框数量
      this.chart.off('brushEnd'); // 先移除旧的监听器
      this.chart.on('brushEnd', this.handleBrushEnd);
      
      // 添加窗口大小变化时的自适应
      window.addEventListener('resize', this.resizeChart);
    },
    
    formatClusteringMethod(method) {
      switch (method) {
        case 'age': return 'Age';
        case 'visits': return 'Visit Count';
        case 'disease': return 'Disease';
        case 'algorithm': return 'HDBSCAN Algorithm';
        default: return method;
      }
    },
    
    getClusterColor(clusterId) {
      return this.colors[clusterId % this.colors.length];
    },
    
    getClusterLabel(clusterId) {
      const stats = this.clusterStats.find(s => s.clusterId === clusterId);
      return stats?.label || `Cluster ${clusterId + 1}`;
    },
    
    formatNumber(num) {
      if (num === undefined || num === null) return 'N/A';
      return Number(num).toFixed(1);
    },
    
    getDiagnosisPercentage(count, total) {
      if (!total) return 0;
      return (count / total) * 100;
    },
    
    // Toggle cluster visibility
    toggleClusterVisibility(clusterId) {
      const index = this.visibleClusters.indexOf(clusterId);
      if (index >= 0) {
        // Remove from visible clusters
        this.visibleClusters.splice(index, 1);
      } else {
        // Add to visible clusters
        this.visibleClusters.push(clusterId);
      }
      
      // Re-render the visualization with updated visibility
      this.renderVisualization();
    },

    // 处理brushEnd事件，限制选择框数量
    handleBrushEnd(params) {
      if (params.areas) {
        // 过滤掉空的选择框
        const validAreas = params.areas.filter(area => 
          area.brushType && 
          ((area.coordRange && area.coordRange.length > 0) || 
           (area.range && area.range.length > 0))
        );
        
        // 更新选择框数组
        this.brushAreas = validAreas;
        
        // 如果超过最大数量，只保留最新的N个
        if (this.brushAreas.length > this.maxBrushCount) {
          this.brushAreas = this.brushAreas.slice(-this.maxBrushCount);
          
          // 重新应用选择框
          this.chart.dispatchAction({
            type: 'brush',
            areas: this.brushAreas,
            removable: true
          });
        }
      }
    },
    
    // 移除最后添加的选择框
    removeLastBrushArea() {
      if (this.brushAreas.length > 0) {
        // 移除最后一个选择框
        this.brushAreas.pop();
        
        // 重新应用剩余的选择框
        this.chart.dispatchAction({
          type: 'brush',
          areas: this.brushAreas,
          removable: true
        });
        
        // 如果没有选择框，清除所有选择
        if (this.brushAreas.length === 0) {
          this.clearSelection();
        } else {
          // 重新计算选中的点
          this.updateSelectedPatients();
        }
      }
    },
    
    // 更新选中的患者
    updateSelectedPatients() {
      // 获取当前图表的选中状态
      const selectedItems = this.chart.getModel().getComponent('brush').brushTargetManager.mapper.mapping;
      
      if (selectedItems && selectedItems[0] && selectedItems[0].selected) {
        // 获取所有选中的索引
        const selectedIndices = [];
        selectedItems[0].selected.forEach(selection => {
          if (selection.dataIndex) {
            selectedIndices.push(...selection.dataIndex);
          }
        });
        
        // 获取选中的数据点，确保包含完整的患者数据
        const selectedData = selectedIndices.map(index => {
          const pointData = this.chart.getOption().series[0].data[index];
          
          // 确保包含原始数据，特别是诊断日期
          if (pointData && pointData.patientId) {
            // 尝试从可视化数据中找到完整的患者数据
            const fullPatientData = this.visualizationData.find(p => 
              p.patient_id === pointData.patientId || p.patientId === pointData.patientId
            );
            
            // 合并数据，确保包含原始数据
            if (fullPatientData) {
              return {
                ...pointData,
                raw: fullPatientData.raw || {},
                diagnosis_date: fullPatientData.diagnosis_date || 
                              fullPatientData.raw?.diagnosis_date || 
                              fullPatientData.raw?.['诊断日期'] || 
                              fullPatientData.raw?.visit_date
              };
            }
          }
          
          return pointData;
        });
        
        // 更新选中的患者列表
        this.selectedPatients = selectedData;
        
        // 如果有选中的患者，设置第一个为当前选中
        if (selectedData.length > 0) {
          this.selectedPatient = selectedData[0];
          console.log('Updated selected patients with complete data');
        } else {
          this.selectedPatient = null;
        }
        
        // 通知父组件患者选择已改变
        this.$emit('patients-selected', selectedData);
      } else {
        this.selectedPatients = [];
        this.selectedPatient = null;
        
        // 通知父组件清空选择
        this.$emit('patients-selected', []);
      }
    },

    // 处理选择事件
    handleBrushSelected(params) {
      if (params.batch && params.batch[0] && params.batch[0].selected && params.batch[0].selected.length > 0) {
        // 获取所有选中的索引
        const selectedIndices = [];
        params.batch[0].selected.forEach(selection => {
          if (selection.dataIndex) {
            selectedIndices.push(...selection.dataIndex);
          }
        });

        if (selectedIndices.length > 0) {
          // 获取所有选中的数据点
          const selectedData = selectedIndices.map(index => {
            const pointData = this.chart.getOption().series[0].data[index];
            
            // 确保包含原始数据，特别是诊断日期
            if (pointData && pointData.patientId) {
              // 尝试从可视化数据中找到完整的患者数据
              const fullPatientData = this.visualizationData.find(p => 
                p.patient_id === pointData.patientId || p.patientId === pointData.patientId
              );
              
              // 合并数据，确保包含原始数据
              if (fullPatientData) {
                return {
                  ...pointData,
                  raw: fullPatientData.raw || {},
                  diagnosis_date: fullPatientData.diagnosis_date || 
                                  fullPatientData.raw?.diagnosis_date || 
                                  fullPatientData.raw?.['诊断日期'] || 
                                  fullPatientData.raw?.visit_date
                };
              }
            }
            
            return pointData;
          });

          // 更新选中的患者列表
          this.selectedPatients = selectedData;

          // 设置第一个选中的患者为当前选中
          if (selectedData.length > 0) {
            this.selectedPatient = selectedData[0];
          }

          console.log(`Selected ${selectedIndices.length} patients with complete data`);
          console.log('Selected patient data sample:', selectedData[0]);

          // 通知父组件患者选择已改变
          this.$emit('patients-selected', selectedData);
        }
      } else {
        // 如果没有选中任何点，清空选择
        this.selectedPatients = [];
        this.selectedPatient = null;

        // 通知父组件清空选择
        this.$emit('patients-selected', []);
      }
    },
    
    // 清除选择
    clearSelection() {
      this.selectedPatients = [];
      this.selectedPatient = null;
      this.brushAreas = [];
      this.chart.dispatchAction({
        type: 'brush',
        command: 'clear',
        areas: []
      });
    },

    // Select a single patient from the table
    selectSinglePatient(patient) {
      this.selectedPatient = patient;
    },

    // 添加图表大小自适应方法
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style scoped>
.patient-clustering {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.clustering-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.clustering-header h3 {
  margin: 0;
  font-size: 18px;
}

.processing-status {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left-color: #1976d2;
  border-radius: 50%;
  margin-right: 8px;
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.clustering-controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.clustering-title {
  font-weight: bold;
  margin-right: 15px;
}

.clustering-options {
  display: flex;
  gap: 10px;
}

.clustering-option {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.clustering-option:hover {
  background-color: #e0e0e0;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

.visualization-container {
  flex: 1;
  min-height: 400px;
  margin-bottom: 20px;
  position: relative; /* Add position relative for absolute positioning of legend */
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

/* New styles for the cluster legend */
.cluster-legend {
  position: absolute;
  top: 80px; /* 将图例往下移动一大块，从10px改为80px */
  right: 10px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 200px;
  max-height: 80%;
  overflow-y: auto;
  z-index: 10;
}

.cluster-legend-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
  transition: opacity 0.2s;
}

.cluster-legend-item.disabled {
  opacity: 0.4;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.cluster-name {
  flex: 1;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cluster-count {
  font-size: 11px;
  color: #666;
  margin-left: 4px;
}

.selected-patient {
  margin-top: 20px;
}

.selected-patient h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.patient-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.patient-header {
  padding: 10px 15px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-id {
  font-weight: bold;
}

.patient-cluster {
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
}

.patient-details {
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.patient-detail-row {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.detail-value {
  font-weight: 500;
}

.error-message {
  background-color: #ffd7d7;
  border: 1px solid #ffb2b2;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.error-icon {
  font-size: 24px;
  color: #e74c3c;
  margin-right: 12px;
}

.error-text {
  font-size: 14px;
  color: #c0392b;
  font-weight: 500;
}

.processing-steps {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
}

.step-icon.completed {
  background-color: #2ca02c;
}

.step-icon.current {
  background-color: #1976d2;
}

.step-text {
  font-size: 14px;
  color: #555;
}

/* Selection info */
.selection-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 10;
}

.selection-count {
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
}

.clear-selection-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-selection-btn:hover {
  background-color: #e0e0e0;
}

/* Multiple selected patients table */
.selected-patients-list {
  margin-top: 20px;
}

.patients-table {
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.patients-table table {
  width: 100%;
  border-collapse: collapse;
}

.patients-table th {
  background-color: #f5f5f5;
  padding: 10px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
}

.patients-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  font-size: 13px;
}

.patients-table tr:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.table-cluster-indicator {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  display: inline-block;
}

/* Backend connection status styles */
.connection-status {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  margin: 20px 0;
  text-align: center;
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.7;
}

.status-text {
  flex: 1;
  text-align: left;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.status-message {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 15px;
}

.status-message code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retry-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.retry-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}
</style>