<template>
  <div class="patient-clustering">
    <div class="clustering-header">
      <h3>Patient Data Clustering</h3>
      <div class="processing-status" v-if="isProcessing">
        <div class="spinner"></div>
        <span>{{ processingMessage }}</span>
      </div>
    </div>

    <!-- Error message -->
    <div class="error-message" v-if="errorMessage">
      <div class="error-icon">⚠️</div>
      <div class="error-text">{{ errorMessage }}</div>
    </div>

    <!-- Processing steps -->
    <div class="processing-steps" v-if="isProcessing && processingSteps.length > 0">
      <div class="step-item" v-for="(step, index) in processingSteps" :key="index">
        <div class="step-icon" :class="{ 'completed': step.completed, 'current': step.current }">
          <span v-if="step.completed">✓</span>
          <span v-else-if="step.current">→</span>
          <span v-else>○</span>
        </div>
        <div class="step-text">{{ step.text }}</div>
      </div>
    </div>

    <!-- Clustering Controls -->
    <div class="clustering-controls" v-if="!errorMessage">
      <div class="clustering-title">Cluster By:</div>
      <div class="clustering-options">
        <div class="clustering-option" :class="{ active: clusteringMethod === 'disease' }" @click="setClusteringMethod('disease')">Disease</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'age' }" @click="setClusteringMethod('age')">Age</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'visits' }" @click="setClusteringMethod('visits')">Visit Count</div>
        <div class="clustering-option" :class="{ active: clusteringMethod === 'algorithm' }" @click="setClusteringMethod('algorithm')">HDBSCAN</div>
      </div>
    </div>

    <!-- Visualization Container with Legend -->
    <div class="visualization-container" v-if="!errorMessage">
      <div ref="chartContainer" class="chart-container"></div>
      
      <!-- Selection info -->
      <div class="selection-info" v-if="selectedPatients.length > 0">
        <div class="selection-count">{{ selectedPatients.length }} patients selected</div>
        <button class="clear-selection-btn" @click="clearSelection">Clear</button>
      </div>
      
      <!-- Non-obtrusive cluster legend -->
      <div class="cluster-legend">
        <div v-for="(cluster, index) in clusterLegendItems" 
             :key="index" 
             class="cluster-legend-item"
             :class="{ 'disabled': !visibleClusters.includes(cluster.id) }"
             @click="toggleClusterVisibility(cluster.id)">
          <div class="color-dot" :style="{ backgroundColor: cluster.color }"></div>
          <div class="cluster-name">{{ cluster.name }}</div>
          <div class="cluster-count">({{ cluster.count }})</div>
        </div>
      </div>
    </div>

    <!-- Selected Patient Details -->
    <div class="selected-patient" v-if="selectedPatient">
      <h4>Selected Patient</h4>
      <div class="patient-card">
        <div class="patient-header">
          <span class="patient-id">Medical Card: {{ selectedPatient.patientId }}</span>
          <span class="patient-cluster" :style="{ backgroundColor: selectedPatient.color }">
            {{ getClusterLabel(selectedPatient.cluster) }}
          </span>
        </div>
        <div class="patient-details">
          <div class="patient-detail-row">
            <span class="detail-label">Age:</span>
            <span class="detail-value">{{ selectedPatient.age }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Gender:</span>
            <span class="detail-value">{{ selectedPatient.gender }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Diagnosis:</span>
            <span class="detail-value">{{ selectedPatient.diagnosis }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">Visit Count:</span>
            <span class="detail-value">{{ selectedPatient.visitCount }}</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">HbA1c:</span>
            <span class="detail-value">{{ formatNumber(selectedPatient.HbA1c) }}%</span>
          </div>
          <div class="patient-detail-row">
            <span class="detail-label">IOP (R/L):</span>
            <span class="detail-value">{{ formatNumber(selectedPatient.IOP_R) }} / {{ formatNumber(selectedPatient.IOP_L) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Multiple Selected Patients -->
    <div class="selected-patients-list" v-if="selectedPatients.length > 1">
      <h4>Selected Patients ({{ selectedPatients.length }})</h4>
      <div class="patients-table">
        <table>
          <thead>
            <tr>
              <th>Medical Card</th>
              <th>Age</th>
              <th>Gender</th>
              <th>Diagnosis</th>
              <th>Visit Count</th>
              <th>HbA1c</th>
              <th>Cluster</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(patient, index) in selectedPatients" :key="index" @click="selectSinglePatient(patient)">
              <td>{{ patient.patientId }}</td>
              <td>{{ patient.age }}</td>
              <td>{{ patient.gender }}</td>
              <td>{{ patient.diagnosis }}</td>
              <td>{{ patient.visitCount }}</td>
              <td>{{ formatNumber(patient.HbA1c) }}%</td>
              <td>
                <span class="table-cluster-indicator" :style="{ backgroundColor: patient.color }">
                  {{ getClusterLabel(patient.cluster) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import patientDataProcessor from '../utils/patientDataProcessor';

export default {
  name: 'PatientClustering',
  data() {
    return {
      isProcessing: false,
      processingComplete: false,
      chart: null,
      clusteringMethod: 'disease',
      visualizationData: [],
      clusterStats: [],
      selectedPatient: null,
      selectedPatients: [],
      errorMessage: null,
      colors: [
        '#4682B4', '#5F9EA0', '#6495ED', '#00BFFF', '#1E90FF', 
        '#87CEEB', '#87CEFA', '#ADD8E6', '#B0E0E6', '#00CED1',
        '#48D1CC', '#40E0D0', '#7FFFD4', '#66CDAA', '#20B2AA'
      ],
      processingMessage: '',
      processingSteps: [],
      visibleClusters: [], // Track which clusters are visible
      clusterLegendItems: [], // Store legend items for display
      brushAreas: [], // 存储选择框区域
      maxBrushCount: 2 // 最大选择框数量
    };
  },
  mounted() {
    this.initChart();
    this.loadData();
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose();
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    async loadData() {
      this.isProcessing = true;
      this.errorMessage = null;
      this.processingMessage = '正在加载数据...';
      
      // 初始化处理步骤
      this.processingSteps = [
        { text: '加载 All.json 文件', completed: false, current: true },
        { text: '提取并处理患者数据', completed: false, current: false },
        { text: '进行数据特征提取', completed: false, current: false },
        { text: '执行聚类分析', completed: false, current: false },
        { text: '生成可视化数据', completed: false, current: false }
      ];
      
      try {
        console.log('开始加载并处理患者数据...');
        
        // Step 1: 加载 All.json 文件
        try {
          this.processingMessage = '正在加载 All.json 文件...';
          await patientDataProcessor.loadData();
          this.processingSteps[0].completed = true;
          this.processingSteps[0].current = false;
          this.processingSteps[1].current = true;
        } catch (loadError) {
          throw new Error(`加载 All.json 失败: ${loadError.message}`);
        }
        
        // Step 2: 提取并处理患者数据
        try {
          this.processingMessage = '正在提取并处理患者数据...';
          patientDataProcessor.processData(); // 直接调用，不使用 await，因为这是同步方法
          this.processingSteps[1].completed = true;
          this.processingSteps[1].current = false;
          this.processingSteps[2].current = true;
        } catch (processError) {
          throw new Error(`处理患者数据失败: ${processError.message}`);
        }
        
        // Step 3-5: 执行数据聚类和可视化
        this.processingMessage = '正在执行聚类分析...';
        this.processingSteps[2].current = true;
        
        try {
          // 执行疾病聚类
          const diagnosisClusters = patientDataProcessor.clusterByDiagnosis();
          this.processingSteps[2].completed = true;
          this.processingSteps[2].current = false;
          this.processingSteps[3].current = true;
          
          // 处理聚类结果
          if (diagnosisClusters && diagnosisClusters.visualizationData) {
            this.visualizationData = diagnosisClusters.visualizationData;
            this.clusterStats = diagnosisClusters.clusterStats || [];
            
            this.processingSteps[3].completed = true;
            this.processingSteps[3].current = false;
            this.processingSteps[4].current = true;
            this.processingMessage = '正在生成可视化数据...';
            
            // 生成可视化
            this.processingComplete = true;
            this.processingSteps[4].completed = true;
            this.processingSteps[4].current = false;
            
            console.log(`成功加载 ${this.visualizationData.length} 条患者记录，聚类数量: ${this.clusterStats.length}`);
            this.processingMessage = '数据处理完成！';
            
            // 更新可视化
            this.updateVisualization();
          } else {
            throw new Error('聚类结果无效');
          }
        } catch (clusterError) {
          throw new Error(`聚类分析失败: ${clusterError.message}`);
        }
        
      } catch (error) {
        console.error('处理数据时出错:', error);
        this.errorMessage = `处理数据时出错: ${error.message || '未知错误'}。请确保 All.json 文件存在且格式正确。`;
      } finally {
        this.isProcessing = false;
      }
    },
    
    initChart() {
      const chartDom = this.$refs.chartContainer;
      this.chart = echarts.init(chartDom);
      
      // Set empty chart with loading state
      this.chart.setOption({
        title: {
          text: 'Patient Clustering',
          left: 'center'
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          type: 'scatter',
          data: []
        }]
      });
      
      // Add click event handler
      this.chart.on('click', this.handleChartClick);
    },
    
    handleChartClick(params) {
      if (params.data) {
        this.selectedPatient = params.data;
        // Clear multiple selections when clicking on a single point
        this.selectedPatients = [params.data];
      }
    },
    
    setClusteringMethod(method) {
      this.clusteringMethod = method;
      this.updateVisualization();
    },
    
    async updateVisualization() {
      if (!this.processingComplete) {
        return;
      }
      
      this.isProcessing = true;
      this.selectedPatient = null;
      this.errorMessage = null;
      this.processingMessage = `正在更新 ${this.clusteringMethod} 聚类...`;
      
      try {
        let result;
        
        // Apply clustering based on selected method
        switch (this.clusteringMethod) {
          case 'age':
            result = patientDataProcessor.clusterByAge();
            break;
            
          case 'visits':
            result = patientDataProcessor.clusterByVisitCount();
            break;
            
          case 'algorithm':
            // 重新执行聚类而不是运行完整的处理流程
            result = patientDataProcessor.clusterByDiagnosis();
            break;
            
          case 'disease':
          default:
            // Disease clustering is default
            result = {
              visualizationData: patientDataProcessor.embedding2D,
              clusterStats: patientDataProcessor.clusterStats
            };
            break;
        }
        
        if (result && result.visualizationData && result.visualizationData.length > 0) {
          this.visualizationData = result.visualizationData;
          this.clusterStats = result.clusterStats || [];
          this.processingMessage = '可视化更新完成';
          this.renderVisualization();
        } else {
          console.error('No visualization data available');
          this.errorMessage = '无法生成可视化数据。请尝试其他聚类方法。';
        }
      } catch (error) {
        console.error('Error updating visualization:', error);
        this.errorMessage = `更新可视化时出错: ${error.message || '未知错误'}`;
      } finally {
        this.isProcessing = false;
      }
    },
    
    renderVisualization() {
      if (!this.visualizationData || this.visualizationData.length === 0) {
        return;
      }
      
      // Get colored data based on clustering method
      const coloredData = patientDataProcessor.getVisualizationWithColors(
        this.clusteringMethod === 'algorithm' ? 'cluster' : this.clusteringMethod
      );
      
      // Get unique clusters and create legend items
      const uniqueClusters = [...new Set(coloredData.map(item => item.cluster))];
      
      // Initialize visible clusters if empty
      if (this.visibleClusters.length === 0) {
        this.visibleClusters = [...uniqueClusters];
      }
      
      // Create legend items
      this.clusterLegendItems = uniqueClusters.map(clusterId => {
        const stats = this.clusterStats.find(s => s.clusterId === clusterId);
        const clusterColor = this.getClusterColor(clusterId);
        const clusterPatients = coloredData.filter(item => item.cluster === clusterId);
        
        return {
          id: clusterId,
          name: stats?.label || `Cluster ${clusterId + 1}`,
          color: clusterColor,
          count: clusterPatients.length
        };
      });
      
      // Filter data based on visible clusters
      const filteredData = coloredData.filter(item => this.visibleClusters.includes(item.cluster));
      
      // Prepare data for scatter plot
      const scatterData = filteredData.map(item => ({
        ...item,
        value: [item.x, item.y],
        symbolSize: 10,
        itemStyle: {
          color: item.color
        }
      }));
      
      // Update chart
      this.chart.setOption({
        title: {
          text: `Patient Clustering by ${this.formatClusteringMethod(this.clusteringMethod)}`,
          left: 'center',
          top: 30 // 将标题下移
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const data = params.data;
            return `
              <div style="font-weight: bold; margin-bottom: 5px;">Medical Card: ${data.patientId}</div>
              <div>Age: ${data.age}</div>
              <div>Gender: ${data.gender}</div>
              <div>Diagnosis: ${data.diagnosis}</div>
              <div>Visit Count: ${data.visitCount}</div>
              <div>HbA1c: ${this.formatNumber(data.HbA1c)}%</div>
            `;
          }
        },
        legend: {
          show: false // Hide the default legend
        },
        xAxis: {
          type: 'value',
          show: false,
          min: (value) => Math.floor(value.min * 1.2),
          max: (value) => Math.ceil(value.max * 1.2)
        },
        yAxis: {
          type: 'value',
          show: false,
          min: (value) => Math.floor(value.min * 1.2),
          max: (value) => Math.ceil(value.max * 1.2)
        },
        grid: {
          left: 10,
          right: 10,
          top: 70, // 增加顶部空间，为工具栏腾出位置
          bottom: 10,
          containLabel: false
        },
        brush: {
          toolbox: ['rect', 'polygon', 'keep', 'clear', 'circle', 'lineX', 'lineY'],
          xAxisIndex: 0,
          brushLink: 'all',
          outOfBrush: {
            colorAlpha: 0.1
          },
          brushStyle: {
            borderWidth: 1,
            color: 'rgba(120,140,180,0.15)',
            borderColor: 'rgba(70,130,180,0.8)'
          },
          throttleType: 'debounce',
          throttleDelay: 300,
          brushMode: 'multiple', // 支持多选
          removeOnClick: true, // 点击已有选择框可删除
        },
        toolbox: {
          top: 5, // 工具栏位置调整
          right: 20,
          feature: {
            brush: {
              type: ['rect', 'polygon', 'circle', 'keep', 'clear', 'lineX', 'lineY'],
              title: {
                rect: '矩形选择',
                polygon: '多边形选择',
                circle: '圆形选择',
                lineX: '横向选择',
                lineY: '纵向选择',
                keep: '保持选择',
                clear: '清除选择'
              }
            },
            myTool: {
              show: true,
              title: '删除最后选择框',
              icon: 'path://M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z',
              onclick: () => {
                this.removeLastBrushArea();
              }
            },
            dataZoom: {
              show: true,
              title: {
                zoom: '缩放',
                back: '还原'
              }
            },
            restore: {
              show: true,
              title: '重置'
            }
          }
        },
        series: [{
          name: 'Patients',
          type: 'scatter',
          data: scatterData,
          symbolSize: 12,
          emphasis: {
            focus: 'self',
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      });

      // 恢复已有的选择框
      if (this.brushAreas.length > 0) {
        this.chart.dispatchAction({
          type: 'brush',
          areas: this.brushAreas
        });
      }

      // 添加brush事件监听
      this.chart.off('brushSelected'); // 先移除旧的监听器
      this.chart.on('brushSelected', this.handleBrushSelected);
      
      // 添加brushEnd事件监听，用于限制选择框数量
      this.chart.off('brushEnd'); // 先移除旧的监听器
      this.chart.on('brushEnd', this.handleBrushEnd);
      
      // 添加窗口大小变化时的自适应
      window.addEventListener('resize', this.resizeChart);
    },
    
    formatClusteringMethod(method) {
      switch (method) {
        case 'age': return 'Age';
        case 'visits': return 'Visit Count';
        case 'disease': return 'Disease';
        case 'algorithm': return 'HDBSCAN Algorithm';
        default: return method;
      }
    },
    
    getClusterColor(clusterId) {
      return this.colors[clusterId % this.colors.length];
    },
    
    getClusterLabel(clusterId) {
      const stats = this.clusterStats.find(s => s.clusterId === clusterId);
      return stats?.label || `Cluster ${clusterId + 1}`;
    },
    
    formatNumber(num) {
      if (num === undefined || num === null) return 'N/A';
      return Number(num).toFixed(1);
    },
    
    getDiagnosisPercentage(count, total) {
      if (!total) return 0;
      return (count / total) * 100;
    },
    
    // Toggle cluster visibility
    toggleClusterVisibility(clusterId) {
      const index = this.visibleClusters.indexOf(clusterId);
      if (index >= 0) {
        // Remove from visible clusters
        this.visibleClusters.splice(index, 1);
      } else {
        // Add to visible clusters
        this.visibleClusters.push(clusterId);
      }
      
      // Re-render the visualization with updated visibility
      this.renderVisualization();
    },

    // 处理brushEnd事件，限制选择框数量
    handleBrushEnd(params) {
      if (params.areas) {
        // 过滤掉空的选择框
        const validAreas = params.areas.filter(area => 
          area.brushType && 
          ((area.coordRange && area.coordRange.length > 0) || 
           (area.range && area.range.length > 0))
        );
        
        // 更新选择框数组
        this.brushAreas = validAreas;
        
        // 如果超过最大数量，只保留最新的N个
        if (this.brushAreas.length > this.maxBrushCount) {
          this.brushAreas = this.brushAreas.slice(-this.maxBrushCount);
          
          // 重新应用选择框
          this.chart.dispatchAction({
            type: 'brush',
            areas: this.brushAreas,
            removable: true
          });
        }
      }
    },
    
    // 移除最后添加的选择框
    removeLastBrushArea() {
      if (this.brushAreas.length > 0) {
        // 移除最后一个选择框
        this.brushAreas.pop();
        
        // 重新应用剩余的选择框
        this.chart.dispatchAction({
          type: 'brush',
          areas: this.brushAreas,
          removable: true
        });
        
        // 如果没有选择框，清除所有选择
        if (this.brushAreas.length === 0) {
          this.clearSelection();
        } else {
          // 重新计算选中的点
          this.updateSelectedPatients();
        }
      }
    },
    
    // 更新选中的患者
    updateSelectedPatients() {
      // 获取当前图表的选中状态
      const selectedItems = this.chart.getModel().getComponent('brush').brushTargetManager.mapper.mapping;
      
      if (selectedItems && selectedItems[0] && selectedItems[0].selected) {
        // 获取所有选中的索引
        const selectedIndices = [];
        selectedItems[0].selected.forEach(selection => {
          if (selection.dataIndex) {
            selectedIndices.push(...selection.dataIndex);
          }
        });
        
        // 获取选中的数据点
        const selectedData = selectedIndices.map(index => 
          this.chart.getOption().series[0].data[index]
        );
        
        // 更新选中的患者列表
        this.selectedPatients = selectedData;
        
        // 如果有选中的患者，设置第一个为当前选中
        if (selectedData.length > 0) {
          this.selectedPatient = selectedData[0];
        } else {
          this.selectedPatient = null;
        }
      } else {
        this.selectedPatients = [];
        this.selectedPatient = null;
      }
    },

    // 处理选择事件
    handleBrushSelected(params) {
      if (params.batch && params.batch[0] && params.batch[0].selected && params.batch[0].selected.length > 0) {
        // 获取所有选中的索引
        const selectedIndices = [];
        params.batch[0].selected.forEach(selection => {
          if (selection.dataIndex) {
            selectedIndices.push(...selection.dataIndex);
          }
        });
        
        if (selectedIndices.length > 0) {
          // 获取所有选中的数据点
          const selectedData = selectedIndices.map(index => 
            this.chart.getOption().series[0].data[index]
          );
          
          // 更新选中的患者列表
          this.selectedPatients = selectedData;
          
          // 设置第一个选中的患者为当前选中
          if (selectedData.length > 0) {
            this.selectedPatient = selectedData[0];
          }
          
          console.log(`Selected ${selectedIndices.length} patients`);
        }
      } else {
        // 如果没有选中任何点，清空选择
        this.selectedPatients = [];
        this.selectedPatient = null;
      }
    },
    
    // 清除选择
    clearSelection() {
      this.selectedPatients = [];
      this.selectedPatient = null;
      this.brushAreas = [];
      this.chart.dispatchAction({
        type: 'brush',
        command: 'clear',
        areas: []
      });
    },

    // Select a single patient from the table
    selectSinglePatient(patient) {
      this.selectedPatient = patient;
    },

    // 添加图表大小自适应方法
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style scoped>
.patient-clustering {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.clustering-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.clustering-header h3 {
  margin: 0;
  font-size: 18px;
}

.processing-status {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left-color: #1976d2;
  border-radius: 50%;
  margin-right: 8px;
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.clustering-controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.clustering-title {
  font-weight: bold;
  margin-right: 15px;
}

.clustering-options {
  display: flex;
  gap: 10px;
}

.clustering-option {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
}

.clustering-option:hover {
  background-color: #e0e0e0;
}

.clustering-option.active {
  background-color: #1976d2;
  color: white;
}

.visualization-container {
  flex: 1;
  min-height: 400px;
  margin-bottom: 20px;
  position: relative; /* Add position relative for absolute positioning of legend */
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

/* New styles for the cluster legend */
.cluster-legend {
  position: absolute;
  top: 80px; /* 将图例往下移动一大块，从10px改为80px */
  right: 10px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 200px;
  max-height: 80%;
  overflow-y: auto;
  z-index: 10;
}

.cluster-legend-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
  transition: opacity 0.2s;
}

.cluster-legend-item.disabled {
  opacity: 0.4;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.cluster-name {
  flex: 1;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cluster-count {
  font-size: 11px;
  color: #666;
  margin-left: 4px;
}

.selected-patient {
  margin-top: 20px;
}

.selected-patient h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.patient-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.patient-header {
  padding: 10px 15px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-id {
  font-weight: bold;
}

.patient-cluster {
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
}

.patient-details {
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.patient-detail-row {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.detail-value {
  font-weight: 500;
}

.error-message {
  background-color: #ffd7d7;
  border: 1px solid #ffb2b2;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.error-icon {
  font-size: 24px;
  color: #e74c3c;
  margin-right: 12px;
}

.error-text {
  font-size: 14px;
  color: #c0392b;
  font-weight: 500;
}

.processing-steps {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
}

.step-icon.completed {
  background-color: #2ca02c;
}

.step-icon.current {
  background-color: #1976d2;
}

.step-text {
  font-size: 14px;
  color: #555;
}

/* Selection info */
.selection-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 10;
}

.selection-count {
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
}

.clear-selection-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-selection-btn:hover {
  background-color: #e0e0e0;
}

/* Multiple selected patients table */
.selected-patients-list {
  margin-top: 20px;
}

.patients-table {
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.patients-table table {
  width: 100%;
  border-collapse: collapse;
}

.patients-table th {
  background-color: #f5f5f5;
  padding: 10px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #ddd;
}

.patients-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  font-size: 13px;
}

.patients-table tr:hover {
  background-color: #f9f9f9;
  cursor: pointer;
}

.table-cluster-indicator {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  display: inline-block;
}
</style> 