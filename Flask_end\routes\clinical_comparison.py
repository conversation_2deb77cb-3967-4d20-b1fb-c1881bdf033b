# 导入必要的模块
from flask import Blueprint, request, jsonify  # Flask框架相关模块：Blueprint用于创建蓝图，request用于获取请求数据，jsonify用于返回JSON响应
import json  # 用于处理JSON数据
import os    # 用于处理文件路径

# 创建一个名为'clinical_comparison'的蓝图对象
# 蓝图是Flask中用于组织路由和视图函数的方式，可以将相关功能模块化
bp = Blueprint('clinical_comparison', __name__)
# 内存缓存字典，用于存储已加载的数据，避免重复读取文件
# 这样可以提高性能，因为文件读取是相对耗时的操作
cache = {}

def calculate_color(item):
    """
    计算检查项目的颜色类型和强度
    参数:
        item (dict): 包含检查项目信息的字典，必须包含以下键：
                    - "ItemValue": 检查项目的实际值
                    - "ItemRange": 检查项目的正常范围，格式为"最小值-最大值"

    返回:
        tuple: (颜色类型, 颜色强度)
               - 颜色类型: "normal"(正常), "high"(偏高), "low"(偏低)
               - 颜色强度: 0-1之间的浮点数，表示偏离正常范围的程度
    """
    try:
        # 将检查项目的值转换为浮点数
        value = float(item["ItemValue"])
        # 解析正常范围，将"最小值-最大值"字符串分割并转换为浮点数
        low, high = map(float, item["ItemRange"].split("-"))
        # 判断检查值是否在正常范围内
        if low <= value <= high:
            return "normal", 0  # 正常范围内，强度为0
        # 判断检查值是否偏高或偏低
        elif value > high:
            # 值偏高，计算偏高的强度
            # 强度计算公式：(实际值 - 上限) / (上限 * 2)，最大值限制为1
            intensity = min(1, (value - high) / (high * 2) if high != 0 else 1)
            return "high", intensity
        else:
            # 值偏低，计算偏低的强度
            # 强度计算公式：(下限 - 实际值) / (下限 * 2)，最大值限制为1
            intensity = min(1, (low - value) / (low * 2) if low != 0 else 1)
            return "low", intensity
    except Exception:
        # 如果出现任何异常（如数据格式错误），返回正常状态
        return "normal", 0

def build_tooltip(item):
    """
    构建检查项目的提示信息字符串

    参数:
        item (dict): 包含检查项目信息的字典，必须包含以下键：
                    - "ItemRange": 检查项目的正常范围
                    - "Itemunit": 检查项目的单位
                    - "ItemValue": 检查项目的实际值

    返回:
        str: 格式化的提示信息字符串，如"10 mg/L < 15 < 20 mg/L"
    """
    try:
        # 安全地获取单位字段，处理可能的字段名变化
        unit = item.get('Itemunit', item.get('ItemUnit', item.get('itemUnit', '')))

        # 分割正常范围字符串，获取最小值和最大值
        item_range = item.get('ItemRange', item.get('itemRange', ''))
        if not item_range or '-' not in item_range:
            return f"{item.get('ItemValue', '')} {unit}"

        low, high = item_range.split("-")
        value = float(item.get('ItemValue', 0))
        low_val = float(low)
        high_val = float(high)

        if value > high_val:
            return f"{item.get('ItemValue', '')} {unit} > {high} {unit}"
        elif value < low_val:
            return f"{item.get('ItemValue', '')} {unit} < {low} {unit}"
        # 构建格式化的提示信息：最小值 单位 < 实际值 < 最大值 单位
        else:
            return f"{low} {unit} < {item.get('ItemValue', '')} < {high} {unit}"
    except Exception as e:
        # 如果格式化失败，返回简单的范围和单位信息
        unit = item.get('Itemunit', item.get('ItemUnit', item.get('itemUnit', '')))
        item_range = item.get('ItemRange', item.get('itemRange', ''))
        return f"{item_range} {unit}"

# 使用装饰器定义路由，指定URL路径和允许的HTTP方法
# @bp.route: 将函数绑定到特定的URL路径
# '/api/clinical_comparison': API的访问路径
# methods=['POST']: 只允许POST请求方法
@bp.route('/api/clinical_comparison', methods=['POST'])
def clinical_comparison():
    """
    临床检查数据对比API接口

    功能：接收两个患者ID，返回这两个患者的检查数据进行对比

    请求方法：POST
    请求体格式：JSON
    {
        "patient_ids": ["患者ID1", "患者ID2"]
    }

    返回格式：JSON
    成功时：
    {
        "status": "success",
        "patient1": [...],  # 患者1的检查数据
        "patient2": [...]   # 患者2的检查数据
    }

    失败时：
    {
        "status": "error",
        "msg": "错误信息"
    }
    """
    print("clinical_comparison接口被调用")  # 调试信息，用于跟踪接口调用情况
    try:
        # 获取POST请求中的JSON数据
        data = request.get_json()

        # 从请求数据中提取患者ID列表，如果没有则默认为空列表
        ids = data.get("patient_ids", [])
        print(ids)  # 打印患者ID列表，用于调试

        # 验证输入参数：必须是包含1个或2个元素的列表
        if not isinstance(ids, list) or len(ids) < 1 or len(ids) > 2:
            # 返回400错误状态码（Bad Request）和错误信息
            return jsonify({"status": "error", "msg": "需要1个或2个患者ID"}), 400

        # 数据缓存机制：避免重复读取文件
        if "all_data" not in cache:
            # 构建JSON文件的完整路径
            # os.path.dirname(__file__): 获取当前文件所在目录
            # "../data/check_info.json": 相对路径，指向上级目录的data文件夹
            json_path = os.path.join(os.path.dirname(__file__), "../data/check_info.json")
            # 打开并读取JSON文件
            # encoding="utf-8": 指定文件编码为UTF-8，确保中文字符正确读取
            with open(json_path, "r", encoding="utf-8") as f:
                cache["all_data"] = json.load(f)  # 将JSON数据加载到缓存中

        # 从缓存中获取所有患者数据
        all_data = cache["all_data"]
        def get_patient_data(pid):
            """
            根据患者ID获取患者的检查数据

            参数:
                pid (str): 患者ID（就诊卡号）

            返回:
                list: 包含患者所有检查项目的列表，每个项目都添加了颜色和提示信息
                None: 如果找不到患者
            """
            # 在所有患者数据中查找指定ID的患者
            # next(): 返回迭代器中第一个满足条件的元素
            # x.get("就诊卡号", "").strip() == pid.strip(): 比较就诊卡号，去除首尾空格
            # None: 如果没找到匹配的患者，返回None
            patient = next((x for x in all_data if x.get("就诊卡号", "").strip() == pid.strip()), None)

            if not patient:
                return None  # 患者不存在

            result = []  # 存储处理后的检查项目数据
            # 遍历患者的所有检查项目
            for item in patient.get("检查项目", []):
                # 为每个检查项目计算颜色类型和强度
                colorType, colorIntensity = calculate_color(item)

                # 将原始数据和新增的颜色、提示信息合并
                result.append({
                    **item,  # 展开原始检查项目数据
                    "colorType": colorType,        # 添加颜色类型
                    "colorIntensity": colorIntensity,  # 添加颜色强度
                    "tooltip": build_tooltip(item)     # 添加提示信息
                })


            return result

        # 获取患者的检查数据
        p1 = get_patient_data(ids[0])

        # 检查是否成功获取到第一个患者的数据
        if p1 is None:
            return jsonify({"status": "error", "msg": "患者不存在"}), 404

        # 构建响应数据
        response_data = {
            "status": "success",
            "patient1": p1
        }

        # 如果提供了第二个患者ID，也获取其数据
        if len(ids) == 2:
            p2 = get_patient_data(ids[1])
            if p2 is None:
                return jsonify({"status": "error", "msg": "第二个患者不存在"}), 404
            response_data["patient2"] = p2

        # 成功获取数据，返回JSON响应
        return jsonify(response_data)
    except Exception as e:
        # 捕获所有异常，返回500错误状态码（Internal Server Error）
        # str(e): 将异常对象转换为字符串，获取错误信息
        return jsonify({"status": "error", "msg": str(e)}), 500