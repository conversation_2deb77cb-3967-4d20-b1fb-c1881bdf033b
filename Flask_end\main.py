# 导入必要的模块
import os    # 用于处理文件路径
import json  # 用于处理JSON数据
import math  # 用于检查NaN值

def is_nan_value(value):
    """
    检查一个值是否为NaN

    参数:
        value: 要检查的值

    返回:
        bool: 如果是NaN返回True，否则返回False
    """
    # 检查多种可能的NaN表示形式
    if value is None:
        return False  # None不是NaN，保持原样

    # 检查字符串形式的NaN
    if isinstance(value, str):
        # 去除首尾空格并转换为小写进行比较
        cleaned_value = value.strip().lower()
        return cleaned_value in ['nan', 'null', 'none', 'n/a', 'na']

    # 检查数值形式的NaN
    if isinstance(value, (int, float)):
        return math.isnan(value) if isinstance(value, float) else False

    return False

def process_nan_values(data):
    """
    递归处理数据结构中的NaN值，将其替换为空字符串

    参数:
        data: 要处理的数据（可以是字典、列表或其他类型）

    返回:
        处理后的数据
    """
    if isinstance(data, dict):
        # 如果是字典，递归处理每个键值对
        for key, value in data.items():
            data[key] = process_nan_values(value)
    elif isinstance(data, list):
        # 如果是列表，递归处理每个元素
        for i, item in enumerate(data):
            data[i] = process_nan_values(item)
    else:
        # 如果是其他类型的值，检查是否为NaN
        if is_nan_value(data):
            return ""  # 将NaN值替换为空字符串

    return data

def process_all_json():
    """
    处理All.json文件，将指定字段设置为空字符串，并处理所有NaN值

    功能：
    1. 读取data/All.json文件
    2. 遍历所有数据项
    3. 将"secondary_diagnosis_code"和"secondary_diagnosis_name"字段的值设置为空字符串
    4. 将所有NaN值替换为空字符串
    5. 将修改后的数据写回文件
    """

    # 构建JSON文件的完整路径
    # os.path.dirname(__file__): 获取当前Python文件所在的目录
    # "data/All.json": 数据文件的相对路径
    json_path = os.path.join(os.path.dirname(__file__), "data", "All.json")

    print(f"正在处理文件: {json_path}")

    try:
        # 读取JSON文件
        # "r": 以只读模式打开文件
        # encoding="utf-8": 指定文件编码为UTF-8，确保中文字符正确读取
        with open(json_path, "r", encoding="utf-8") as f:
            my_data = json.load(f)  # 将JSON文件内容加载为Python对象

        print(f"成功读取文件，共有 {len(my_data)} 条数据")

        # 统计修改的记录数量
        modified_count = 0
        nan_replaced_count = 0

        # 遍历所有数据项
        for index, item in enumerate(my_data):
            # 标记当前项是否被修改
            item_modified = False

            # 检查并处理"secondary_diagnosis_code"字段
            if "secondary_diagnosis_code" in item:
                # 只有当值不为空时才进行修改和计数
                if item["secondary_diagnosis_code"] != "":
                    item["secondary_diagnosis_code"] = ""
                    item_modified = True
                    print(f"第 {index + 1} 条记录: secondary_diagnosis_code 已清空")

            # 检查并处理"secondary_diagnosis_name"字段
            if "secondary_diagnosis_name" in item:
                # 只有当值不为空时才进行修改和计数
                if item["secondary_diagnosis_name"] != "":
                    item["secondary_diagnosis_name"] = ""
                    item_modified = True
                    print(f"第 {index + 1} 条记录: secondary_diagnosis_name 已清空")

            # 处理当前项中的所有NaN值
            original_item = json.dumps(item, ensure_ascii=False)  # 保存原始状态用于比较
            my_data[index] = process_nan_values(item)  # 处理NaN值
            modified_item = json.dumps(my_data[index], ensure_ascii=False)

            # 检查是否有NaN值被替换
            if original_item != modified_item:
                nan_replaced_count += 1
                print(f"第 {index + 1} 条记录: 发现并处理了NaN值")
                item_modified = True

            # 如果当前项被修改，增加修改计数
            if item_modified:
                modified_count += 1

        # 将修改后的数据写回文件
        # "w": 以写入模式打开文件（会覆盖原文件内容）
        # ensure_ascii=False: 确保中文字符正确保存
        # indent=2: 设置JSON格式化缩进，使文件更易读
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(my_data, f, ensure_ascii=False, indent=2)

        print(f"\n处理完成！")
        print(f"总共修改了 {modified_count} 条记录")
        print(f"其中 {nan_replaced_count} 条记录包含NaN值并已处理")
        print(f"数据已保存到: {json_path}")

    except FileNotFoundError:
        print(f"错误：找不到文件 {json_path}")
        print("请确认文件路径是否正确")
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式不正确 - {e}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

# 程序主入口
if __name__ == "__main__":
    """
    当直接运行此脚本时执行数据处理
    """
    print("开始处理All.json文件...")
    print("功能：")
    print("1. 清空 secondary_diagnosis_code 和 secondary_diagnosis_name 字段")
    print("2. 将所有 NaN 值替换为空字符串")
    print("=" * 60)

    # 调用处理函数
    process_all_json()

    print("=" * 60)
    print("程序执行完毕")