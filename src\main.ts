import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import ECharts from 'vue-echarts'
import { use } from 'echarts/core'
// Preload patient data processor to start the data loading process early
// @ts-ignore - Ignore TypeScript errors for the JS module
import patientDataProcessor from './utils/patientDataProcessor'

// Import ECharts components
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from 'echarts/charts'

import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components'

import {
  CanvasRenderer
} from 'echarts/renderers'

// Register necessary ECharts components
use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON>h<PERSON><PERSON>,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  ToolboxComponent,
  <PERSON>ZoomComponent,
  CanvasRenderer
])

const app = createApp(App)

// Register the ECharts component
app.component('v-chart', ECharts)

// Use router
app.use(router)

// Begin data loading in the background
patientDataProcessor.loadData().then(() => {
  console.log('Patient data loaded successfully');
// @ts-ignore - Ignore TypeScript errors for the error parameter
}).catch(error => {
  console.error('Failed to load patient data:', error);
});

// Mount the app
app.mount('#app')
