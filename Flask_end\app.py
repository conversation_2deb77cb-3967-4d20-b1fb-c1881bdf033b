# 导入必要的模块
from flask import Flask, jsonify  # Flask: 创建Web应用的核心类, jsonify: 将Python对象转换为JSON响应
from flask_cors import CORS       # CORS: 跨域资源共享，允许前端从不同域名访问API
import os                         # 操作系统接口，用于处理文件路径
import json                       # JSON数据处理模块

# 导入clinical_comparison蓝图模块
# 使用try-except结构确保即使模块导入失败，应用也能正常启动
try:
    # 从routes目录下的clinical_comparison模块导入蓝图对象
    # 'as clinical_comparison_bp': 给导入的蓝图对象起一个别名
    from routes.clinical_comparison import bp as clinical_comparison_bp
except ImportError:
    # 如果导入失败（比如文件不存在），将蓝图设置为None
    # 这样可以避免程序崩溃，提高应用的健壮性
    clinical_comparison_bp = None

# 导入diagnosis_analysis蓝图模块
try:
    from routes.diagnosis_analysis import bp as diagnosis_analysis_bp
except ImportError:
    diagnosis_analysis_bp = None

# 导入ai_diagnosis蓝图模块
try:
    from routes.ai_diagnosis import bp as ai_diagnosis_bp
except ImportError:
    ai_diagnosis_bp = None

# 导入clustering_analysis蓝图模块
try:
    from routes.clustering_analysis import bp as clustering_analysis_bp
except ImportError:
    clustering_analysis_bp = None

# 导入visualization_data蓝图模块
try:
    from routes.visualization_data import bp as visualization_data_bp
except ImportError:
    visualization_data_bp = None
    
# 导入diagnostic_analysis.test模块中的路由注册函数
try:
    from diagnostic_analysis.test import register_routes
except ImportError:
    register_routes = None

# 创建Flask应用实例
# __name__: 当前模块的名称，Flask用它来确定应用的根路径
app = Flask(__name__)
# 启用跨域资源共享(CORS)
# 这允许前端应用（如React、Vue等）从不同的域名或端口访问这个API
# 在开发环境中特别重要，因为前端和后端通常运行在不同端口
CORS(app)
# 注册蓝图到Flask应用
# 只有在成功导入蓝图的情况下才进行注册
if clinical_comparison_bp:
    # register_blueprint(): 将蓝图注册到主应用
    # 这样蓝图中定义的所有路由都会被添加到主应用中
    app.register_blueprint(clinical_comparison_bp)

if diagnosis_analysis_bp:
    app.register_blueprint(diagnosis_analysis_bp)

# 注册AI诊断蓝图
if ai_diagnosis_bp:
    app.register_blueprint(ai_diagnosis_bp)

# 注册聚类分析蓝图
if clustering_analysis_bp:
    app.register_blueprint(clustering_analysis_bp)

# 注册可视化数据蓝图
if visualization_data_bp:
    app.register_blueprint(visualization_data_bp)

# 注册diagnostic_analysis.test中的路由
if register_routes:
    register_routes(app)

# 定义获取所有患者ID的API接口
# @app.route: Flask装饰器，用于将函数绑定到特定的URL路径
# '/api/patient_ids': API的访问路径
# methods=['GET']: 指定允许的HTTP方法，这里只允许GET请求
@app.route('/api/patient_ids', methods=['GET'])
def get_patient_ids():
    """
    获取所有患者ID列表的API接口
    功能：从JSON数据文件中读取所有患者的就诊卡号，返回ID列表
    请求方法：GET
    请求参数：无

    返回格式：JSON数组
    例如：["P001", "P002", "P003", ...]

    用途：为前端提供患者选择列表，用于患者数据对比功能
    """
    # 构建JSON数据文件的完整路径
    # os.path.dirname(__file__): 获取当前Python文件(app.py)所在的目录
    # "data\check_info.json": 数据文件的相对路径
    # 注意：这里使用了反斜杠，在Windows系统中是正确的，但建议使用正斜杠或os.path.join更好的跨平台兼容性
    json_path = os.path.join(os.path.dirname(__file__), "data\check_info.json")
    # 打印文件路径，用于调试和确认文件位置
    print("Loading:", json_path)

    # 打开并读取JSON文件
    # "r": 以只读模式打开文件
    # encoding="utf-8": 指定文件编码为UTF-8，确保中文字符正确读取
    # with语句: 确保文件在使用完毕后自动关闭，即使出现异常也能正确关闭
    with open(json_path, "r", encoding="utf-8") as f:
        all_data = json.load(f)  # 将JSON文件内容加载为Python对象（通常是列表或字典）
    # 使用列表推导式提取所有患者的就诊卡号
    # 解析过程：
    # 1. 遍历all_data中的每个item（每个患者的数据）
    # 2. 检查item中是否包含"就诊卡号"这个键
    # 3. 如果包含，获取就诊卡号的值并去除首尾空格（strip()）
    # 4. 将所有符合条件的就诊卡号组成一个新列表
    ids = [item["就诊卡号"].strip() for item in all_data if "就诊卡号" in item]
    # 将Python列表转换为JSON格式并返回给客户端
    # jsonify(): Flask提供的函数，自动设置正确的Content-Type头部
    return jsonify(ids)

# 定义根路径的路由
# 这是一个简单的测试接口，用于验证服务器是否正常运行

# Python程序入口点
# 这个条件确保只有直接运行这个文件时才会执行以下代码
# 如果这个文件被其他模块导入，则不会执行
if __name__ == '__main__':
    # 启动Flask开发服务器
    # debug=True: 启用调试模式
    #   - 代码修改后自动重启服务器
    #   - 出现错误时显示详细的错误信息
    #   - 提供交互式调试器
    # 注意：生产环境中应该设置debug=False，因为调试模式会暴露敏感信息
    app.run(debug=True)
