# 诊断分析相关的API接口
from flask import Blueprint, jsonify, request
import os
import json
from collections import defaultdict, Counter

# 创建蓝图
bp = Blueprint('diagnosis_analysis', __name__)

def load_all_data():
    """加载All.json数据"""
    json_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "All.json")
    try:
        with open(json_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading All.json: {e}")
        return []

@bp.route('/api/diagnosis_names', methods=['GET'])
def get_diagnosis_names():
    """
    获取所有主要诊断名称列表
    返回格式：["黄斑水肿", "原发性开角型青光眼", ...]
    """
    try:
        all_data = load_all_data()
        
        # 统计所有主要诊断名称
        diagnosis_names = []
        for item in all_data:
            if "主要诊断名称" in item and item["主要诊断名称"]:
                diagnosis_name = item["主要诊断名称"].strip()
                if diagnosis_name and diagnosis_name not in diagnosis_names:
                    diagnosis_names.append(diagnosis_name)
        
        # 按字母顺序排序
        diagnosis_names.sort()
        
        return jsonify({
            "success": True,
            "data": diagnosis_names,
            "total": len(diagnosis_names)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/api/diagnosis_statistics', methods=['GET'])
def get_diagnosis_statistics():
    """
    获取主要诊断名称的统计信息
    返回每个诊断名称的患者数量
    """
    try:
        all_data = load_all_data()
        
        # 统计每个诊断名称的患者数量
        diagnosis_counter = Counter()
        diagnosis_patients = defaultdict(set)  # 使用set避免重复患者
        
        for item in all_data:
            if "主要诊断名称" in item and item["主要诊断名称"] and "就诊卡号" in item:
                diagnosis_name = item["主要诊断名称"].strip()
                patient_id = item["就诊卡号"].strip()
                
                if diagnosis_name and patient_id:
                    diagnosis_patients[diagnosis_name].add(patient_id)
        
        # 转换为统计结果
        statistics = []
        for diagnosis_name, patient_set in diagnosis_patients.items():
            statistics.append({
                "diagnosis_name": diagnosis_name,
                "patient_count": len(patient_set),
                "patient_ids": list(patient_set)
            })
        
        # 按患者数量降序排序
        statistics.sort(key=lambda x: x["patient_count"], reverse=True)
        
        return jsonify({
            "success": True,
            "data": statistics,
            "total_diagnoses": len(statistics)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/api/diagnosis_patients/<diagnosis_name>', methods=['GET'])
def get_patients_by_diagnosis(diagnosis_name):
    """
    根据主要诊断名称获取相关的患者ID列表
    用于知识图谱展示
    """
    try:
        all_data = load_all_data()
        
        # 查找指定诊断名称的所有患者
        patients = set()
        patient_details = []
        
        for item in all_data:
            if ("主要诊断名称" in item and 
                item["主要诊断名称"] and 
                item["主要诊断名称"].strip() == diagnosis_name and
                "就诊卡号" in item):
                
                patient_id = item["就诊卡号"].strip()
                if patient_id and patient_id not in patients:
                    patients.add(patient_id)
                    patient_details.append({
                        "patient_id": patient_id,
                        "name": item.get("姓名", "").strip(),
                        "gender": item.get("性别", "").strip(),
                        "age": item.get("年龄", "").strip(),
                        "diagnosis_date": item.get("诊断日期", "").strip(),
                        "main_diagnosis": item.get("主要诊断", "").strip(),
                        "department_code": item.get("处置科室代码", "")
                    })
        
        return jsonify({
            "success": True,
            "diagnosis_name": diagnosis_name,
            "patient_count": len(patient_details),
            "patients": patient_details
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/api/knowledge_graph_data/<diagnosis_name>', methods=['GET'])
def get_knowledge_graph_data(diagnosis_name):
    """
    获取知识图谱数据
    返回格式适用于前端图谱展示
    """
    try:
        all_data = load_all_data()
        
        # 查找指定诊断名称的所有患者
        patients = set()
        patient_nodes = []
        
        for item in all_data:
            if ("主要诊断名称" in item and 
                item["主要诊断名称"] and 
                item["主要诊断名称"].strip() == diagnosis_name and
                "就诊卡号" in item):
                
                patient_id = item["就诊卡号"].strip()
                if patient_id and patient_id not in patients:
                    patients.add(patient_id)
                    patient_nodes.append({
                        "id": patient_id,
                        "name": f"患者 {patient_id}",
                        "type": "patient",
                        "details": {
                            "patient_id": patient_id,
                            "name": item.get("姓名", "").strip(),
                            "gender": item.get("性别", "").strip(),
                            "age": item.get("年龄", "").strip(),
                            "diagnosis_date": item.get("诊断日期", "").strip()
                        }
                    })
        
        # 构建图谱数据结构
        nodes = [
            {
                "id": diagnosis_name,
                "name": diagnosis_name,
                "type": "diagnosis",
                "category": 0
            }
        ]
        
        # 添加患者节点
        for i, patient in enumerate(patient_nodes):
            nodes.append({
                "id": patient["id"],
                "name": patient["name"],
                "type": "patient",
                "category": 1,
                "details": patient["details"]
            })
        
        # 构建连接关系
        links = []
        for patient in patient_nodes:
            links.append({
                "source": diagnosis_name,
                "target": patient["id"],
                "relation": "诊断"
            })
        
        return jsonify({
            "success": True,
            "diagnosis_name": diagnosis_name,
            "nodes": nodes,
            "links": links,
            "patient_count": len(patient_nodes)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
